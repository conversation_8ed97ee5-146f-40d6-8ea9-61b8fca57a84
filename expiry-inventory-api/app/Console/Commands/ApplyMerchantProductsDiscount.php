<?php

namespace App\Console\Commands;

use App\Jobs\HandleApplyDiscountPaginatedMerchantProducts;
use App\Models\Merchant;
use Illuminate\Console\Command;

class ApplyMerchantProductsDiscount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'apply:discount {subdomain}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public $cursor = null;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $subdomain = $this->argument('subdomain');

        $this->line("----- Apply Products Discount -----");

        $merchant = Merchant::where('subdomain', $subdomain)->first();
        if (!$merchant) {
            $this->error("Merchant not found");
            return;
        }
        $this->line("Running Apply Products Discount for merchant {$merchant->subdomain}");
        HandleApplyDiscountPaginatedMerchantProducts::dispatch($merchant);
    }
}
