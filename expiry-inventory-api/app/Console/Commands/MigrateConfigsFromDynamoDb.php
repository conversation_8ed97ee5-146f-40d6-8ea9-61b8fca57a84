<?php

namespace App\Console\Commands;

use App\Models\DDBModuleDetail;
use App\Models\Merchant;
use App\Models\MerchantConfig;
use App\Models\Setting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MigrateConfigsFromDynamoDb extends Command
{
    protected $signature = 'configs:migrate-from-ddb {--dry-run : Do not write to DB, only log}';

    protected $description = 'Migrate default and merchant configs from DynamoDB DDBModuleDetail into SQL tables (settings, merchant_configs)';

    public function handle()
    {
        $dry = (bool) $this->option('dry-run');

        $this->info('Migrating default settings...');
        $defaultKeys = [
            'configs',
            'print_settings',
            'plan_settings',
            'expiry_date_liquid_template',
        ];
        $migratedDefault = 0;
        foreach ($defaultKeys as $key) {
            $uuid = 'default_' . $key;
            $detail = DDBModuleDetail::find(['uuid' => $uuid, 'key' => $key]);
            if ($detail && !Setting::where('key', $key)->exists()) {
                $this->line(" - default {$key}");
                if (!$dry) {
                    Setting::create(['key' => $key, 'value' => $detail->value]);
                }
                $migratedDefault++;
            }
        }
        $this->info("Default settings migrated: {$migratedDefault}");

        $this->info('Migrating merchant settings...');
        $keys = [
            'configs',
            'print_settings',
        ];
        $count = 0;
        $merchants = Merchant::withTrashed()->get(['id']);
        foreach ($merchants as $merchant) {
            foreach ($keys as $key) {
                $uuid = 'merchants_' . $merchant->id;
                $detail = DDBModuleDetail::find(['uuid' => $uuid, 'key' => $key]);
                if ($detail && !MerchantConfig::where('merchant_id', $merchant->id)->where('key', $key)->exists()) {
                    $this->line(" - merchant {$merchant->id} {$key}");
                    if (!$dry) {
                        MerchantConfig::create([
                            'merchant_id' => $merchant->id,
                            'key' => $key,
                            'value' => $detail->value,
                        ]);
                    }
                    $count++;
                }
            }
        }
        $this->info("Merchant configs migrated: {$count}");

        $this->info('Done.');

        return 0;
    }
}

