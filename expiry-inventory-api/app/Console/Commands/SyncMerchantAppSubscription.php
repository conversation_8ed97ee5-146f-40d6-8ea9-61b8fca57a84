<?php

namespace App\Console\Commands;

use App\Jobs\HandleSyncAppSubscription;
use App\Models\Merchant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class SyncMerchantAppSubscription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:subcription {--subdomain=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $subdomain = $this->option('subdomain');

        $this->line("----- Sync Merchant Products -----");

        $merchants = Merchant::query()->when($subdomain, function ($query, $subdomain) {
            $query->where('subdomain', $subdomain);
        })->get();
        $jobs = [];
        foreach ($merchants as $merchant) {
            $this->line("Running Sync Merchant App Subscription for merchant {$merchant->subdomain}");
            $jobs[] = new HandleSyncAppSubscription($merchant);

        }
        Bus::chain($jobs)
            ->dispatch();

    }
}
