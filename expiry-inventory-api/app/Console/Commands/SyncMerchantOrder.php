<?php

namespace App\Console\Commands;

use App\Jobs\HandleSyncShopifyOrder;
use App\Models\Merchant;
use Illuminate\Console\Command;

class SyncMerchantOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:order {subdomain} {shopify_order_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public $cursor = null;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $subdomain = $this->argument('subdomain');
        $shopify_order_id = $this->argument('shopify_order_id');

        $this->line("----- Sync Merchant Order -----");

        $merchant = Merchant::where('subdomain', $subdomain)->first();
        if (!$merchant) {
            $this->error("Merchant not found");
            return;
        }
        $this->line("Running Sync Merchant Order {$shopify_order_id} for merchant {$merchant->subdomain}");
        HandleSyncShopifyOrder::dispatch($shopify_order_id, $merchant, 'update');
    }
}
