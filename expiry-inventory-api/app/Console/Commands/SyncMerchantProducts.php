<?php

namespace App\Console\Commands;

use App\Jobs\HandleSyncShopifyProductsGraphql;
use App\Models\Merchant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class SyncMerchantProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:products {--subdomain=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public $cursor = null;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $subdomain = $this->option('subdomain');

        $this->line("----- Sync Merchant Products -----");

        // get the first and second of the merchants
        $merchants = Merchant::when($subdomain, function ($query, $subdomain) {
            $query->where('subdomain', $subdomain);
        })->orderBy('id', 'asc')->limit(2)->get();
        $first_merchant = $merchants[0] ?? null;
        $second_merchant = $merchants[1] ?? null;
        if (!$first_merchant) {
            $this->error("Merchant not found");
            return;
        }
        $this->line("Running Sync Merchant Products for merchant {$first_merchant->subdomain}");
        HandleSyncShopifyProductsGraphql::dispatch($first_merchant, null, $second_merchant);


    }
}
