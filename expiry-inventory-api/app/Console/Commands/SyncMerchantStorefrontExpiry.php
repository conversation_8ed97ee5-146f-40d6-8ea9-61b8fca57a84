<?php

namespace App\Console\Commands;

use App\Jobs\HandleApplyStorefrontExpiryPaginatedMerchantProducts;
use App\Models\Merchant;
use Illuminate\Console\Command;

class SyncMerchantStorefrontExpiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:storefront-expiry {--subdomain=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $subdomain = $this->option('subdomain');

        $this->line("----- Sync Merchant Storefront Expiry -----");

        $merchants = Merchant::query()->when($subdomain, function($query, $subdomain) {
            $query->where('subdomain', $subdomain);
        })->get();

        foreach($merchants as $merchant) {
            $this->line("Running Sync Merchant Storefront Expiry for merchant {$merchant->subdomain}");
            HandleApplyStorefrontExpiryPaginatedMerchantProducts::dispatch($merchant);
            $this->line("Dispatched Sync Merchant Storefront Expiry for merchant {$merchant->subdomain}");
        }

    }
}
