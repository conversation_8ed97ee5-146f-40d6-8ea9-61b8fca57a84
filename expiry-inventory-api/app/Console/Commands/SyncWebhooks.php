<?php

namespace App\Console\Commands;

use App\Jobs\HandleSyncShopifyWebhooks;
use App\Models\Merchant;
use Illuminate\Console\Command;

class SyncWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:webhooks {--subdomain=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $subdomain = $this->option('subdomain');

        $this->line("----- Sync Webhooks -----");
        
        $merchants = Merchant::query()->when($subdomain, function($query, $subdomain) {
            $query->where('subdomain', $subdomain);
        })->get();
        
        foreach($merchants as $merchant) {
            $this->line("Sync webhooks for {$merchant->subdomain}");
            HandleSyncShopifyWebhooks::dispatch($merchant);
        }

        $this->line("----- End Sync Webhooks -----");
    }
}
