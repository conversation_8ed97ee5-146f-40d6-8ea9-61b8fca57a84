<?php

namespace App\Console;

use App\Jobs\HandleApplyDiscountPaginatedMerchantProducts;
use App\Jobs\HandleApplyStorefrontExpiryPaginatedMerchantProducts;
use App\Jobs\HandleMerchantBatchesExpiry;
use App\Jobs\HandleSyncAppSubscription;
use App\Jobs\HandleSyncShopifyWebhooks;
use App\Models\Merchant;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Throwable;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->call(function () {
            Log::info('----------- Begin Daily Schedule ------------');
            $merchants = Merchant::withTrashed()->get();
            foreach ($merchants as $merchant) {
                Log::info($merchant->subdomain);
                Bus::chain([
                    new HandleSyncShopifyWebhooks($merchant),
                    new HandleMerchantBatchesExpiry($merchant),
                    new HandleApplyDiscountPaginatedMerchantProducts($merchant),
                    new HandleApplyStorefrontExpiryPaginatedMerchantProducts($merchant),
                    new HandleSyncAppSubscription($merchant),
                ])->catch(function (Throwable $e) {
                    Log::info('EventChainError', ['error' => $e]);
                })->dispatch();
            }
            Log::info('----------- Done Daily Schedule ------------');
        })->daily()->timezone('UTC');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
