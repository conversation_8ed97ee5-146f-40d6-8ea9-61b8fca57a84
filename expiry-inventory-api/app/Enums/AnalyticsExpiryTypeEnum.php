<?php

namespace App\Enums;

final class AnalyticsExpiryTypeEnum
{
    public const EXPIRED = 'expired';
    public const EXPIRING_SOON = 'expiring_soon';
    public const BEST_BEFORE = 'best_before';
    public const UPCOMING_BEST_BEFORE = 'upcoming_best_before';
    public const DISCOUNTED = 'discounted';

    public static function all(): array
    {
        return [
            static::EXPIRED,
            static::EXPIRING_SOON,
            static::BEST_BEFORE,
            static::UPCOMING_BEST_BEFORE,
            static::DISCOUNTED,
        ];
    }
}
