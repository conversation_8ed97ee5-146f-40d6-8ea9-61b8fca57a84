<?php

namespace App\Enums;

final class ProductInventoryStatusEnum
{
    public const IN_STOCK = 'in_stock';
    public const OUT_OF_STOCK = 'out_of_stock';
    public const EXPIRED = 'expired';
    public const PAST_BEST_BEFORE = 'past_best_before';

    public static function all(): array
    {
        return [
            static::IN_STOCK,
            static::OUT_OF_STOCK,
            static::EXPIRED,
            static::PAST_BEST_BEFORE,
        ];
    }
}
