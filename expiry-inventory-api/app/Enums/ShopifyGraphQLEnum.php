<?php

namespace App\Enums;

final class ShopifyGraphQLEnum
{
    public const PRODUCT_PAGE_SIZE = 50;
    public const PRODUCT_VARIANT_PAGE_SIZE = 100;

    public const APP_SUBSCRIPTION = 'AppSubscription';
    public const PRODUCT = 'Product';
    public const PRODUCT_VARIANT = 'ProductVariant';

    public const INVENTORY_ITEM = 'InventoryItem';
    public const LOCATION = 'Location';

    public const INVENTORY_ON_HAND = 'on_hand';

    /**
     * Summary of Inventory states (name)
     * https://shopify.dev/docs/apps/build/orders-fulfillment/inventory-management-apps#inventory-states
     * 
     * Valid values are: available, damaged, incoming, quality_control, reserved, safety_stock.
     */
    public const INVENTORY_STATE_AVAILABLE = 'available';

    /**
     * Summary of INVENTORY ON_HAND (reason)
     * https://shopify.dev/docs/apps/build/orders-fulfillment/inventory-management-apps/manage-quantities-states#set-inventory-quantities-on-hand
     * Valid values are: correction, cycle_count_available, damaged, movement_canceled, movement_created, movement_received, movement_updated, other, promotion, quality_control, received, reservation_created, reservation_deleted, reservation_updated, restock, safety_stock, shrinkage.
     */
    public const INVENTORY_ON_HAND_CORRECTION = 'correction';




}

