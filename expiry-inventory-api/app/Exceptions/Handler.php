<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Throwable $exception)
    {
        if ($exception instanceof ValidationException) {
            $msg = 'The given data was invalid.';

            Log::error('[422] '.$msg . $request->url().$request->getMethod(), $exception->errors());

            return json_resp($exception->errors(),$msg, 422);
        }
        elseif ($exception instanceof  ModelNotFoundException) {
            $arr = explode('\\', $exception->getModel());
            $msg = end($arr).' not found.';

            Log::error('[404] ' . $msg);

            return json_resp([],$msg, 404);
        }
        elseif ($this->isAuthenticateException($exception)) {
            $msg = $exception->getMessage() ?:'Unauthenticated.';

            Log::error('[401] ' . $msg);

            return json_resp([],$msg,401);
        }
        elseif ($this->isAuthorizeException($exception)) {
            $msg = $exception->getMessage() ?: 'This action is unauthorized.';

            Log::error('[403] ' . $msg);

            return json_resp([],$msg,403);
        }
        elseif($exception instanceof HttpException && $exception->getStatusCode() < 500) {
            $msg = $exception->getMessage();

//            Log::error('['.$exception->getStatusCode().'] ' . $msg);

            return json_resp([],$msg,$exception->getStatusCode());
        }

        Log::error('['.$exception->getCode().'] '. $exception->getMessage());

        return parent::render($request, $exception);
    }

    private function isAuthenticateException($e) {
        return $e instanceof AuthenticationException
            || ($e instanceof HttpException && $e->getStatusCode() == 401);
    }

    private function isAuthorizeException($e) {
        return $e instanceof AuthenticationException
            || ($e instanceof HttpException && $e->getStatusCode() == 403);
    }
}
