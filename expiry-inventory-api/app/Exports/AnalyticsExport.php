<?php

namespace App\Exports;

use App\Enums\AnalyticsExpiryTypeEnum;
use App\Services\AnalyticsService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromCollection;

class AnalyticsExport extends Export implements FromCollection, ShouldQueue
{
    public function headings(): array
    {
        $type = Arr::get($this->options, 'type', AnalyticsExpiryTypeEnum::EXPIRED);
        $days = Arr::get($this->options, 'days', 30);
        return [
            [
                'Date of report: ' . Carbon::now()->utc()->format('d/m/Y H:i:s') . '(UTC)',
                '',
                ''
            ],
            [
                'Type: ' .  Str::ucfirst(Str::replace('_', ' ', $type)),
                '',
                ''
            ],
            [
                'Period: ' . $days,
                '',
                ''
            ],
            [
                'Date',
                'Quantity',
                'Cost',
            ],
        ];
    }

    public function collection()
    {
        $filters = $this->options;
        $filters['merchant_id'] = $this->merchant->id;
        $analyticsService = new AnalyticsService(null);
        return $analyticsService->getExpiryAnalyticsDates($filters);
    }

    public function map($analytics): array
    {
        $data = [
            Carbon::parse($analytics['date'])->format('d/m/Y'),
            $analytics['quantity'],
            $analytics['cost'],
        ];
        return $data;
    }
}
