<?php

namespace App\Exports;

use App\Enums\AnalyticsExpiryTypeEnum;
use App\Services\AnalyticsService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromCollection;

class AnalyticsProductsExport extends Export implements FromCollection, ShouldQueue
{
    public function headings(): array
    {
        $type = Arr::get($this->options, 'type', AnalyticsExpiryTypeEnum::EXPIRED);
        $days = Arr::get($this->options, 'days', 30);
        return [
            [
                'Date of report: ' . Carbon::now()->utc()->format('d/m/Y H:i:s') . '(UTC)',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                ''
            ],
            [
                'Type: ' .  Str::ucfirst(Str::replace('_', ' ', $type)),
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                ''
            ],
            [
                'Period: ' . $days,
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                ''
            ],
            [
                'Name',
                'SKU',
                'Expiry Date',
                'Best Before',
                'Quantity',
                'Current Price',
                'Discount',
                'Original Price',
                'Cost'
            ],
        ];
    }

    public function collection()
    {
        $filters = $this->options;
        $filters['merchant_id'] = $this->merchant->id;
        $filters['limit'] = 99999;
        $analyticsService = new AnalyticsService(null);
        $analytics = $analyticsService->getAnalyticsProducts($filters);
        return collect($analytics['data']);
    }

    public function map($analytics_product): array
    {
        $data = [
            $analytics_product['name'],
            $analytics_product['sku'],
            $analytics_product['nearest_expire_date'] ? Carbon::parse($analytics_product['nearest_expire_date'])->format('d/m/Y') : "",
            implode(',', $analytics_product['best_before_dates']),
            $analytics_product['quantity'],
            $analytics_product['price'],
            $analytics_product['discount'],
            $analytics_product['original_price'],
            ($analytics_product['price'] ?: 0) * ($analytics_product['quantity'])
        ];
        return $data;
    }
}
