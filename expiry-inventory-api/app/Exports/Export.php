<?php

namespace App\Exports;

use App\Enums\ExportEnum;
use App\Mail\ExportCompletedMail;
use App\Models\Merchant;
use App\Models\ExportHistory;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\StringValueBinder;

abstract class Export extends StringValueBinder implements WithMapping, WithHeadings, WithEvents, ShouldAutoSize, WithCustomValueBinder
{
    use Exportable;
    use RegistersEventListeners;

    protected $from_date = null;
    protected $to_date = null;
    protected $export_id = null;
    protected Merchant $merchant;

    protected $options = null;
    protected $extras = null;

    public function fromDate(string $from_date)
    {
        $this->from_date = $from_date;
        return $this;
    }

    public function toDate(string $to_date)
    {
        $this->to_date = $to_date;
        return $this;
    }

    public function setExportId($id)
    {
        $this->export_id = $id;
        return $this;
    }

    public function setMerchant($merchant)
    {
        $this->merchant = $merchant;
        return $this;
    }

    public function setOptions($options = null)
    {
        $this->options = $options;
        return $this;
    }

    public function setExtras($extras = null)
    {
        $this->extras = $extras;
        return $this;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                if($this->export_id) {
                    $history = ExportHistory::find($this->export_id);
                    if($history) {
                        $history->status = "success";
                        $history->save();

                        $history->load('merchant');
                        $this->sendExportCompletedEmail($history);
                    }
                }
            },
        ];
    }

    protected function sendExportCompletedEmail(ExportHistory $export_history)
    {
        $merchant = $export_history->merchant;
        $merchant->load('user');
        if($merchant) {
            $emails = $merchant->getNotifcationEmails();
            $merchant_setting = $merchant->setting();
            $isNotifyStaffs = Arr::get($merchant_setting, 'is_notify_batch_export', true);
            $this->sendExportedEmail($merchant, $export_history, ($isNotifyStaffs ? $emails : [$merchant->getShopEmail()]));
        }
    }

    protected function sendExportedEmail(Merchant $merchant, ExportHistory $export_history, $emails)
    {
        $target = null;
        switch($export_history->model)
        {
            case ExportEnum::ORDER:
            case ExportEnum::PRODUCT:
            case ExportEnum::INVENTORY_BATCH:
            case ExportEnum::DASHBOARD:
            case ExportEnum::DASHBOARD_PRODUCTS:
                $target = Str::replace('_', ' ', $export_history->model);
                break;
        }

        if($target && $merchant->user) {
            Log::info('ExportEmailNotify', ['merchant' => $merchant->subdomain, 'export' => $export_history->id, 'email' => $emails]);
            foreach ($emails as $email) {
                if ($email && strlen($email) > 0) {
                    Mail::to($email)->send(new ExportCompletedMail($merchant->user->name, $target, $export_history->url));
                }
            }
        }
    }
}
