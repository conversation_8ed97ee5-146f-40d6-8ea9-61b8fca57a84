<?php

namespace App\Exports;

use App\Enums\BatchItemStatus;
use App\Models\Location;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\FromQuery;

class InventoryBatchExport extends Export implements FromQuery, ShouldQueue
{
    public function headings(): array
    {
        $from = $this->from_date ? Carbon::parse($this->from_date)->startOfDay()->format('d/m/Y H:i:s') : "N/A";
        $to = $this->to_date ? Carbon::parse($this->to_date)->endOfDay()->format('d/m/Y H:i:s') : "N/A";
        $meta_presets = Arr::get($this->extras, 'meta_presets', []);
        return [
            [
                'Date of report: ' . Carbon::now()->utc()->format('d/m/Y H:i:s') . '(UTC)',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                ...array_map(fn () => '', $meta_presets),
                'Date From: ' . $from,
                'Date To: ' . $to
            ],
            [
                'Date',
                'Batch Name',
                'Inventory Batch ID',
                'Location',
                'Location Name',
                'Variant Name',
                'Variant SKU',
                'Quantity',
                'Expire date (D/M/Y)',
                'Best Before date (D/M/Y)',
                'Item Status',
                'Sync quantity with Shopify (Y/N)',
                'Date received (D/M/Y)',
                'Lot / Batch number',
                'Bin Location',
                'Barcode',
                'Invoice Number',
                'Description',
                'Status',
                ...$meta_presets,
            ],
        ];
    }

    public function query()
    {
        $query = $this->merchant->inventory_batches()->with(['location', 'items.product', 'items.dates'])
            ->when($this->from_date, fn($q, $date) => $q->where('created_at', '>=', Carbon::parse($date)->startOfDay()->format('Y-m-d 00:00:00')))
            ->when($this->to_date, fn($q, $date) => $q->where('created_at', '<=', Carbon::parse($date)->endOfDay()->format('Y-m-d 23:59:59')))
            ->when($this->options, function($q, $options) {
                foreach($options as $key => $value) {
                    if(isset($value)) {
                        switch($key) {
                            case 'status':
                                if (in_array($value, [BatchItemStatus::IN_STOCK, BatchItemStatus::DISCOUNTED, BatchItemStatus::OUT_OF_STOCK, BatchItemStatus::EXPIRED])) {
                                    $q->where($key, $value);
                                }
                                else if ($value == "past_best_before") {
                                    $q->whereHas('items', function ($query) {
                                        $query->whereHas('dates', function ($q) {
                                            $q->whereDate('date', '<=', today());
                                        });
                                    });
                                }
                                else if ($value == "upcoming_best_before") {
                                    $q->whereHas('items', function ($query) {
                                        $query->whereHas('dates', function ($q) {
                                            $q->whereDate('date', '>', today());
                                        });
                                    });
                                }
                                break;
                            case 'location':
                                $location = $value ? Location::find($value) : null;
                                if ($location) {
                                    $q->where('location_id', $location->id);
                                }
                                break;
                        }
                    }
                }
            });
        return $query;
    }

    public function map($inventory_batch): array
    {
        $meta_presets = Arr::get($this->extras, 'meta_presets', []);
        $meta = $inventory_batch->meta ?: [];
        $first_item = $inventory_batch->items->shift();
        $data = [
            (new Carbon($inventory_batch->created_at)),
            $inventory_batch->name,
            $inventory_batch->id,
            $inventory_batch->location->shopify_location_id,
            $inventory_batch->location->name,
            $first_item->product->display_name,
            $first_item->product->sku,
            $first_item->quantity,
            $first_item->expire_at ? Carbon::parse($first_item->expire_at)->format('d/m/Y') : '',
            implode(';', $first_item->dates->pluck('date')->map(fn ($date) => Carbon::parse($date)->format('d/m/Y'))->toArray()),
            $first_item->status,
            $inventory_batch->is_sync_quantity ? 'Y' : 'N',
            $inventory_batch->received_at ? Carbon::parse($inventory_batch->received_at)->format('d/m/Y') : '',
            $inventory_batch->lot_number,
            $inventory_batch->bin_location,
            $inventory_batch->barcode,
            $inventory_batch->invoice_number,
            $inventory_batch->description,
            $inventory_batch->status,
            ...array_map(fn ($preset_meta_name) => Arr::first($meta, fn ($i_meta) => Arr::get($i_meta, 'name') == $preset_meta_name, ['value' => ''])['value'], $meta_presets)
        ];
        foreach($inventory_batch->items as $batch_item) {
            $data[] = [
                '',
                '',
                '',
                '',
                '',
                $batch_item->product->display_name,
                $batch_item->product->sku,
                $batch_item->quantity,
                $batch_item->expire_at ? Carbon::parse($batch_item->expire_at)->format('d/m/Y') : '',
                implode(';', $batch_item->dates->pluck('date')->map(fn ($date) => Carbon::parse($date)->format('d/m/Y'))->toArray()),
                $batch_item->status,
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                ...array_map(fn () => '', $meta_presets),
            ];
        }
        return $data;
    }
}
