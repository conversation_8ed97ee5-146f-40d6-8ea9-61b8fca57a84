<?php

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromQuery;

class OrderExport extends Export implements FromQuery, ShouldQueue
{
    public function headings(): array
    {
        $from = $this->from_date ? Carbon::parse($this->from_date)->startOfDay()->format('d/m/Y H:i:s') : "N/A";
        $to = $this->to_date ? Carbon::parse($this->to_date)->endOfDay()->format('d/m/Y H:i:s') : "N/A";
        return [
            [
                'Date of report: ' . Carbon::now()->utc()->format('d/m/Y H:i:s') . '(UTC)',
                '',
                '',
                '',
                '',
                'Date From: ' . $from,
                'Date To: ' . $to
            ],
            [
                'Order #',
                'Date',
                'User',
                'Amount',
                'Status',
                'Fulfillment Status',
                'Batch Assigned Status'
            ],
        ];
    }

    public function query()
    {
        $query = $this->merchant->orders()->with('customer.user')
            ->when($this->from_date, fn($q, $date) => $q->where('created_at', '>=', Carbon::parse($date)->startOfDay()->format('Y-m-d 00:00:00')))
            ->when($this->to_date, fn($q, $date) => $q->where('created_at', '<=', Carbon::parse($date)->endOfDay()->format('Y-m-d 23:59:59')))
            ->when($this->options, function ($q, $options) {
                foreach ($options as $key => $value) {
                    if (isset ($value)) {
                        switch ($key) {
                            case 'batch_assign_status':
                                $q->where($key, $value);
                                break;
                        }
                    }
                }
            });
        return $query;
    }

    public function map($order): array
    {
        return [
            '#' . $order->order_number,
            (new Carbon($order->created_at))->toDateTimeString(),
            $order->customer->user->name,
            $order->amount,
            Str::ucfirst($order->status),
            Str::ucfirst($order->fulfillment_status),
            Str::ucfirst($order->batch_assign_status)
        ];
    }
}
