<?php

namespace App\Exports;

use App\Enums\InventoryBatchStatus;
use App\Services\ProductService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromQuery;

class ProductExport extends Export implements FromQuery, ShouldQueue
{
    public function headings(): array
    {
        return [
                'Name',
                'Variant Name',
                'Shopify ID',
                'Shopify Variant ID',
                'SKU',
                'Price',
                'Location',
                'Location ID',
                'Quantity',
                'Status',
                'Total Batch Count',
                'Available Batch Count'
        ];
    }

    public function query()
    {
        $productService = app(ProductService::class);
        $options = $this->options ?: [];
        $filters = array_merge([
            'merchant_id' => $this->merchant->id,
        ], $options);
        $query = $productService->getProductsQuery($filters);
        $query->with('locations');
        return $query;
    }

    public function map($product): array
    {
        $product_locations = $product->locations;
        $data = [];
        if ($product_locations->count()) {
            foreach ($product_locations as $product_location) {
                $batch_items_in_location = $product->batch_items->filter(fn ($item) => $item->batch?->location_id == $product_location->id);
                $data[] = [
                    $product->parent_name,
                    $product->name ?: '',
                    $product->parent_id,
                    $product->shopify_variant_id,
                    $product->sku ?: '',
                    $product->original_price,
                    $product_location->name,
                    $product_location->shopify_location_id,
                    $product_location->pivot->on_hand ?: '0',
                    Str::ucfirst($product->status),
                    $batch_items_in_location->count() ?: '0',
                    $batch_items_in_location->filter(fn ($item) => in_array($item->batch?->status, [InventoryBatchStatus::IN_STOCK, InventoryBatchStatus::DISCOUNTED]))->count() ?: '0'
                ];
            }
            if (count($data) == 1) {
                $data = $data[0];
            }
        }
        else {
            $data = [
                $product->parent_name,
                $product->name ?: '',
                $product->parent_id,
                $product->shopify_variant_id,
                $product->sku ?: '',
                $product->original_price,
                '',
                '',
                '0',
                Str::ucfirst($product->status),
                $product->batch_items->count() ?: '0',
                $product->batch_items->filter(fn ($item) => in_array($item->batch?->status, [InventoryBatchStatus::IN_STOCK, InventoryBatchStatus::DISCOUNTED]))->count() ?: '0'
            ];
        }
        return $data;
    }
}
