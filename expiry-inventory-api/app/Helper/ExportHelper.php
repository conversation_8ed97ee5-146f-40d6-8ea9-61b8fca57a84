<?php

namespace App\Helper;

use App\Models\Merchant;
use App\Models\ExportHistory;
use App\Enums\ExportEnum;
use App\Enums\PlanEnum;
use App\Exports\AnalyticsExport;
use App\Exports\AnalyticsProductsExport;
use App\Exports\Export;
use App\Exports\OrderExport;
use App\Exports\ProductExport;
use App\Exports\InventoryBatchExport;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class ExportHelper
{
    protected $model;
    protected $from;
    protected $to;

    protected $file_prefix;
    protected Export $exporter;

    protected $path;
    protected $options = null;

    public function __construct($model, $path = "downloads", $options = [])
    {
        $this->model = $model;
        $this->path = $path;
        $this->options = $options;
        $this->init();
    }

    public function setFrom($date = null)
    {
        $this->exporter = $this->exporter->fromDate($date);
        $this->from = $date;
    }

    public function setTo($date = null)
    {
        $this->exporter = $this->exporter->toDate($date);
        $this->to = $date;
    }

    public function export(Merchant $merchant = null, $exportOptions = [ 'format' => 'csv' ])
    {
        $date = Carbon::now()->format("dmyHis");
        $filename = "{$this->file_prefix}_{$date}.{$exportOptions['format']}";

        $this->options = array_merge($this->options ?: [], $exportOptions);

        $path = $this->path;
        $filepath = (strlen($path) > 0 ? "{$path}/" : "") . $filename;
        $url = Storage::disk('s3')->url($filepath);
        if($url) {
            $export_history = ExportHistory::create([
                'model' => $this->model,
                'filename' => $filename,
                'path' => $filepath,
                'url' => $url,
                'from_date' => $this->from ? Carbon::parse($this->from)->startOfDay() : null,
                'to_date' => $this->to ? Carbon::parse($this->to)->endOfDay() : null,
                'options' => $this->options
            ]);
            if($merchant) {
                $export_history->merchant()->associate($merchant)->save();
                $this->exporter->setMerchant($merchant);
                $merchantSettings = $merchant->setting();
                $meta_presets = $merchantSettings['plan'] == PlanEnum::FREE ? [] : Arr::get($merchantSettings, 'meta_presets', []);
                $this->exporter->setExtras([ 'meta_presets' => $meta_presets ]);
            }

            // set export ID for status update when completed, set options to enable data filtering
            if($this->exporter->setExportId($export_history->id)->setOptions($this->options)->store($filepath, 's3', null, ['visibility' => 'public'])) {
                return $url;
            }
        }
        return false;
    }

    protected function init()
    {
        switch($this->model)
        {
            case ExportEnum::ORDER:
                $this->file_prefix = ExportEnum::ORDER_FILEPREFIX;
                $this->exporter = new OrderExport;
                break;

            case ExportEnum::INVENTORY_BATCH:
                $this->file_prefix = ExportEnum::INVENTORY_BATCH_FILEPREFIX;
                $this->exporter = new InventoryBatchExport;
                break;

            case ExportEnum::PRODUCT:
                $this->file_prefix = ExportEnum::PRODUCT_FILEPREFIX;
                $this->exporter = new ProductExport;
                break;

            case ExportEnum::DASHBOARD:
                $this->file_prefix = ExportEnum::DASHBOARD_FILEPREFIX;
                $this->exporter = new AnalyticsExport;
                break;

            case ExportEnum::DASHBOARD_PRODUCTS:
                $this->file_prefix = ExportEnum::DASHBOARD_PRODUCTS_FILEPREFIX;
                $this->exporter = new AnalyticsProductsExport;
                break;
        }
    }
}
