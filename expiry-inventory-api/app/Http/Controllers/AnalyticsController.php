<?php

namespace App\Http\Controllers;

use App\Enums\ExportEnum;
use App\Helper\ExportHelper;
use App\Http\Requests\AnalyticsRequest;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;

class Analytics<PERSON>ontroller extends Controller
{
    protected AnalyticsService $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    public function analyticsGetDates(AnalyticsRequest $request)
    {
        $input = $request->validated();

        $merchant = auth()->user();
        $input['merchant_id'] = $merchant->id;

        return json_resp($this->analyticsService->getExpiryAnalyticsDates($input));
    }

    public function analyticsGetProductsList(AnalyticsRequest $request)
    {
        $input = $request->validated();

        $merchant = auth()->user();
        $input['merchant_id'] = $merchant->id;

        return json_resp($this->analyticsService->getAnalyticsProducts($input));
    }

    public function exportAnalyticsDates(AnalyticsRequest $request)
    {
        $merchant = auth()->user();

        $input = $request->validated();

        $exporter = new ExportHelper(ExportEnum::DASHBOARD, "downloads", $input);

        try {
            $result = $exporter->export($merchant);
            if ($result) {
                return json_resp([], 'Success');
            } else {
                return json_resp([], 'Failed to export dashboard data.', 400);
            }
        } catch (\Exception $e) {
            return json_resp([], 'Failed to export dashboard data.', 400);
        }
    }

    public function exportAnalyticsProductsList(AnalyticsRequest $request)
    {
        $merchant = auth()->user();

        $input = $request->validated();

        $exporter = new ExportHelper(ExportEnum::DASHBOARD_PRODUCTS, "downloads", $input);

        try {
            $result = $exporter->export($merchant);
            if ($result) {
                return json_resp([], 'Success');
            } else {
                return json_resp([], 'Failed to export dashboard products data.', 400);
            }
        } catch (\Exception $e) {
            return json_resp([], 'Failed to export dashboard products data.', 400);
        }
    }
}