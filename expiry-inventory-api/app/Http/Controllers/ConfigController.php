<?php

namespace App\Http\Controllers;

use App\Enums\PlanEnum;
use App\Jobs\ApplyDiscount;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Jobs\ApplyStorefrontExpiry;
use App\Jobs\HandleApplyDiscountPaginatedMerchantProducts;
use App\Jobs\HandleApplyStorefrontExpiryPaginatedMerchantProducts;
use App\Models\Config;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class ConfigController extends Controller
{
    public function show()
    {
        return json_resp(auth()->user()->setting());
    }

    public function showPrintSettings()
    {
        $setting = auth()->user()->setting();
        return json_resp($setting['plan'] != PlanEnum::FREE ? auth()->user()->setting('print_settings') : Config::getDefaultConfig('print_settings'));
    }

    public function showExpiryTemplate()
    {
        $setting = auth()->user()->setting();
        return json_resp($setting['plan'] != PlanEnum::FREE ? Config::getDefaultConfig('expiry_date_liquid_template') : "");
    }

    public function update(Request $request)
    {
        $merchant = auth()->user();
        $merchant_setting = json_decode(json_encode($merchant->setting()), true);

        $request->merge([
            'plan' => $merchant_setting['plan'],
            'billing_period' => Arr::get($merchant_setting, 'billing_period', 'month'),
            'shop' => Arr::get($merchant_setting, 'shop'),
        ]);
        $merchant->saveSetting($request->all());

        $old_apply_discount = Arr::get($merchant_setting, 'is_apply_discount', false);
        $old_apply_slowing_product = Arr::get($merchant_setting, 'is_apply_slow_moving', false);
        if (
            $old_apply_discount != $request->is_apply_discount || $request->is_apply_discount == true ||
            $old_apply_slowing_product != $request->is_apply_slow_moving || $request->is_apply_slow_moving == true
        ) {
            // $this->applyDiscountToProducts($merchant->products);
            HandleApplyDiscountPaginatedMerchantProducts::dispatch($merchant);
        }
        $old_show_expire_at = Arr::get($merchant_setting, 'is_show_storefront_expire_at', false);
        if ($old_show_expire_at != $request->is_show_storefront_expire_at) {
            ShopifyAPIHelper::withMerchant($merchant)->createOrUpdateShopMetafield(
                'show_expire_at',
                $request->is_show_storefront_expire_at,
                'boolean'
            );
            // $this->applyStorefrontExpiryToProducts($merchant->products);
            HandleApplyStorefrontExpiryPaginatedMerchantProducts::dispatch($merchant);
        }

        return json_resp([], 'Success');
    }

    public function updatePrintSettings(Request $request)
    {
        auth()->user()->saveSetting($request->liquid, 'print_settings');
        return json_resp([], 'Success');
    }

    protected function applyDiscountToProducts($products)
    {
        foreach ($products->chunk(50)->all() as $chunked_products) {
            ApplyDiscount::dispatch(collect($chunked_products));
        }
    }

    protected function applyStorefrontExpiryToProducts($products)
    {
        foreach ($products->chunk(50)->all() as $chunked_products) {
            ApplyStorefrontExpiry::dispatch(collect($chunked_products));
        }
    }
}
