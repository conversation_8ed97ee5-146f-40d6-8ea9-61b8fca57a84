<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    protected $model;

    public function __construct(){
        $model = "App\\Models\\".str_replace('Controller', '', class_basename($this));
        $this->model = $model;
    }

    protected function save($data, $model = null) {
        if(!$model) {
            $model = new $this->model;
        }

        $model->fill($data)->save();

        return $model;
    }

    protected function getByType($type, $value) {
        return $this->model::where($type, $value)->firstOrFail();
    }

    public function resp($data, $status = 200, $type = 'json') {
        if($type != 'json') {
            return $data;
        }

        if(is_object($data)) {
            try {
                $data = $data->toArray();
            }
            catch (\Exception $e) {
                $data = objectToArray($data);
            }
        }

        //Get data
        if(!is_array($data) && !is_object($data)) {
            $data = ['message'  => $data];
        }
        elseif(!isset($data['data']) && $status == 200) {
            $oldData = $data;

            $data = [];
            $data['data'] = $oldData;
        }

        $data['code_status'] = $status;

        if(request()->wantsJson()) {
            return response()->json($data, $status);
        }

        return response($data, $status);
    }

    protected function validation($data, $rules, $message = []) {
        $validator = Validator::make($data, $rules, $message);

        if ($validator->fails()) {
            throw new ValidationException($validator);
//            abort(422, 'Validate Error');
        }
    }

    protected function paginate($model, $limit = null) {
        $request = request();
        $limit = $limit ?: 10;
        $page = $request->page ?: 1;
        return $model->paginate($limit, ['*'], 'page', $page)->toArray();
    }

    /*
     * - where_type = and/or/has
     * - type = and/or/has/is_null/is_not_null/in/nin
     * - condition = eq/neq/gteq/lteq/like/lt/gt
     *
     * search
     * ======
     * [
     *      'filters'   => [
     *          [
     *              'where_type'    => '',
     *              'wheres'        => [
     *                  [
     *                      'type'      => '',
     *                      'field'     => '',
     *                      'condition  => ''
     *                  ],
     *                  [
     *                      'type'      => '',
     *                      'field'     => '',
     *                      'condition  => ''
     *                  ]
     *              ],
     *          ],
     *          [
     *              'where_type'    => '',
     *              'wheres'        => [
     *                  [
     *                      'type'      => '',
     *                      'field'     => '',
     *                      'condition  => ''
     *                  ],
     *                  [
     *                      'type'      => '',
     *                      'field'     => '',
     *                      'condition  => ''
     *                  ]
     *              ],
     *          ],
     *      ],
     *      'relation'  => [
     *          ''  => [
     *              "where_type" : "have/xhave",
     *              'filters'   => [
     *                  [
     *                      'where_type'    => '',
     *                      'wheres'        => [
     *                          [
     *                              'type'      => '',
     *                              'field'     => '',
     *                              'condition  => ''
     *                          ],
     *                          [
     *                              'type'      => '',
     *                              'field'     => '',
     *                              'condition  => ''
     *                          ]
     *                      ],
     *                  ],
     *                  [
     *                      'where_type'    => '',
     *                      'wheres'        => [
     *                          [
     *                              'type'      => '',
     *                              'field'     => '',
     *                              'condition  => ''
     *                          ],
     *                          [
     *                              'type'      => '',
     *                              'field'     => '',
     *                              'condition  => ''
     *                          ]
     *                      ],
     *                  ],
     *              ],
     *          ]
     *      ],
     * ]
     *
     * */

    /*
     * json
     * ====
     * {
            "search" : {
                "filters" : [
                    {
                        "where_type" : "and",
                        "wheres" : [
                            {
                                "type"			: "and",
                                "field" 		: "name",
                                "condition" 	: "eq",
                                "value" 		: "Oscar"
                            },
                            {
                                "type"			: "or",
                                "field" 		: "email",
                                "condition" 	: "like",
                                "value" 		: "%wind%"
                            }
                        ]
                    },
                    {
                        "where_type" : "and",
                        "wheres" : [
                            {
                                "type"			: "nin",
                                "field" 		: "name",
                                "condition" 	: "eq",
                                "value" 		: [""]
                            },
                            {
                                "type"			: "or",
                                "field" 		: "email",
                                "condition" 	: "like",
                                "value" 		: "%wind%"
                            }
                        ]

                    }
                ]
            }
        }
     *
     *
     * */

    public function getIndex($model, $paginate = null) {
        $request  = request();

        if(!$paginate) {
            $paginate = $request;
        }

        //point to laravel table
        $limit = $paginate->limit ?: 0;

        $model = $this->setSearch($model, $request->search);

        if($paginate->order_by) {
            foreach($paginate->order_by as $k  => $v) {
                $model->orderBy($k, $v);
            }
        }

        return $model->paginate($limit)->toArray();
    }

    public function setSearch($model, $search = null) {
        if(!$search) {
            return $model;
        }

        foreach ($search as $filters    => $filter) {
            if($filters == 'filters') {
                $this->setGroupWhere($model, $filter);
            }
            elseif($filters == 'relation') {
                foreach ($filter as $relationTable  => $v) {
                    if(!isset($v['where_type']) || $v['where_type'] == 'have') {
                        $model->whereHas($relationTable, function ($q) use ($v){
                            $this->setGroupWhere($q, $v['filters']);
                        });
                    }
                    else {
                        $model->whereDoesntHave($relationTable, function ($q) use ($v){
                            $this->setGroupWhere($q, $v['filters']);
                        });
                    }

                }
            }
        }

        return $model;
    }

    public function setGroupWhere($model, $filter) {
        $model->where(function ($md) use ($filter) {
            foreach ($filter as $group) {
                switch ($group['where_type']) {
                    case "and" :
                        $md->where(function ($q) use ($group) {
                            $this->setWhere($q, $group['wheres']);
                        });
                        break;
                    case "or" :
                        $md->orWhere(function ($q) use ($group) {
                            $this->setWhere($q, $group['wheres']);
                        });
                        break;
                    default:
                        abort(400, 'condition not found');
                }
            }
        });

    }

    public function setWhere($q, $wheres) {
        foreach ($wheres as $where) {
            $condition = '';

            if(strpos($where['type'], 'null') === false) {
                $condition = $this->convertCondition($where['condition']);
            }

            switch ($where['type']) {
                case "and" :
                    $q->where($where['field'], $condition, $where['value']);
                    break;
                case "or" :
                    $q->orWhere($where['field'], $condition, $where['value']);
                    break;
                case "between" :
                    if(!is_array($where['value'])) {
                        abort(400, 'Value must be an array');
                    }

                    $q->whereBetween($where['field'], $where['value']);
                    break;
                case "in" :
                    if(!is_array($where['value'])) {
                        abort(400, 'Value must be an array');
                    }

                    $q->whereIn($where['field'], $where['value']);
                    break;
                case "nin" :
                    if(!is_array($where['value'])) {
                        abort(400, 'Value must be an array');
                    }

                    $q->whereNotIn($where['field'], $where['value']);
                    break;
                case "is_null" :
                    $q->whereNull($where['field']);
                    break;
                case "is_not_null" :
                    $q->whereNotNull($where['field']);
                    break;
                default:
                    abort(400, 'Condition not found');
            }
        }
    }

    protected function convertCondition($method) {
        switch ($method) {
            case "like" :
                return $method;
                break;
            case "eq" :
                return "=";
                break;
            case "neq" :
                return "!=";
                break;
            case "gteq" :
                return ">=";
                break;
            case "gt" :
                return ">";
                break;
            case "lteq" :
                return "<=";
                break;
            case "lt" :
                return "<";
                break;
            default :
                abort(400, 'Condition not found');
        }
    }

    protected function isOver(Carbon $date) {
        if(!$date) {
            return false;
        }
        return now()->gt($date);
    }

    public function sendMail($receiver, $class, $data, $cc = null, $file = null) {
        $mail = Mail::to($receiver);

        if($cc) {
            $mail->cc($cc);
        }

        if($file){
            $mail->queue(new $class($data, $file));

            return true;
        }

        $mail->queue(new $class($data));
    }

    public function welcome() {
        return view('welcome');
    }
}
