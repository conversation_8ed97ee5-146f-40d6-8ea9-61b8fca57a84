<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CustomerController extends Controller
{
    public function customerUpdated(Request $request)
    {
        Log::info('Customer Updated webhook', $request->all());

        $merchant = auth()->user();

        $customer = $merchant->customers()->where('shopify_customer_id', $request->id)->first();

        if($customer) {
            $customer->user()->first()->fill([
                'name'  => $request->first_name . ' ' . $request->last_name,
                'email' => $request->email
            ])->save();
        }

        return json_resp([], 'Success');
    }
}
