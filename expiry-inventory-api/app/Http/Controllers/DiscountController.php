<?php

namespace App\Http\Controllers;

use App\Enums\BatchItemStatus;
use App\Enums\ProductStatus;
use App\Http\Controllers\Controller;
use App\Services\ShopifyService;

class DiscountController extends Controller
{
    public function getPendingDiscountsCount()
    {
        $merchant = auth()->user();

        $discounts_count = $merchant->discounts()->where('discounts.status', 'pending')->count();

        return json_resp(['pending_discounts_count' => $discounts_count]);
    }

    public function getPendingDiscounts()
    {
        $merchant = auth()->user();

        $discounts = $merchant->discounts()->with(['product', 'batch_item'])->where('discounts.status', 'pending')->get();

        return json_resp($discounts);
    }

    public function approveDiscount($discount_id)
    {
        $merchant = auth()->user();

        $discount = $merchant->discounts()->findOrFail($discount_id);

        $discount->activate();

        return json_resp([], 'Success');
    }

    public function cancelDiscount($discount_id)
    {
        $merchant = auth()->user();

        $discount = $merchant->discounts()->findOrFail($discount_id);

        if ($discount->status == 'active') {
            $product = $discount->product;
            if ($product->status == ProductStatus::DISCOUNTED) {
                (new ShopifyService)
                    ->setMerchant($merchant)
                    ->setProduct($product)
                    ->updateShopifyProductPrices();

                $product->status = ProductStatus::ACTIVE;
                $product->price = $product->original_price;
                $product->save();
            }

            $discount->status = 'rejected';
            $discount->save();

            $batch_item = $product->batch_item;
            if ($batch_item->status == BatchItemStatus::DISCOUNTED) {
                $batch_item->status = BatchItemStatus::IN_STOCK;
                $batch_item->save();

                $batch_item->batch->checkForLatestStatus();
            }
        } else {
            // pending discount
            $discount->status = 'rejected';
            $discount->save();
        }

        return json_resp([], 'Success');
    }
}