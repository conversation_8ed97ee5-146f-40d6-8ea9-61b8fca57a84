<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ImportHistoryController extends Controller
{
    public function index(Request $request)
    {
        $merchant = auth()->user();

        $import_histories_query = $merchant->import_histories();

        if($request->order_by && in_array($request->order_by, ['id', 'created_at'])) {
            $order_direction = $request->order_direction ?: 'desc';
            $import_histories_query->orderBy($request->order_by, $order_direction ?: 'desc');
        }

        $import_histories = $this->paginate($import_histories_query, 25);

        return json_resp($import_histories);
    }
}
