<?php

namespace App\Http\Controllers;

use App\Enums\BatchItemStatus;
use App\Enums\ExportEnum;
use App\Enums\ImportEnum;
use App\Enums\PlanEnum;
use App\Enums\ShopifyGraphQLEnum;
use App\Exceptions\ExpiryInventoryException;
use App\Helper\ExportHelper;
use App\Models\Product;
use App\Models\InventoryBatch;
use App\Models\BatchItem;
use App\Models\InventoryAction;
use App\Models\InventoryHistory;
use App\Models\InventoryBatchVerification;

use App\Traits\HasShopifyAuth;
use App\Imports\InventoryBatchImport;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Http\Helpers\StorefrontExpiryDateHelper;
use App\Jobs\ApplyDiscount;
use App\Jobs\HandleClearMerchantBatchesData;
use App\Models\ImportHistory;
use App\Models\Location;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

use Carbon\Carbon;
use Illuminate\Support\Arr;

class InventoryBatchController extends Controller
{
    use HasShopifyAuth;

    public $import_id = null;

    public function index(Request $request)
    {
        $merchant = auth()->user();
        if ($request->location) {
            $inventory_batches = $merchant->inventory_batches()->with(['items.product', 'items.dates'])->where('location_id', $request->location);
        } else {
            $inventory_batches = $merchant->inventory_batches()->with(['items.product', 'items.dates']);
        }

        // Expose last verified date for sorting and display
        $inventory_batches->addSelect([
            'last_verified_at' => InventoryBatchVerification::select('created_at')
                ->whereColumn('inventory_batch_id', 'inventory_batches.id')
                ->orderByDesc('id')
                ->limit(1)
        ]);


        if ($request->search) {
            $inventory_batches->where(function ($q) use ($request) {
                $q->orWhereRaw('LOWER(inventory_batches.name) LIKE "%' . $request->search . '%"');
                $q->orWhereHas('items.product', function ($q) use ($request) {
                    $q->where(function ($q) use ($request) {
                        $q->orWhereRaw('LOWER(products.name) LIKE "%' . $request->search . '%"')
                            ->orWhereRaw('LOWER(products.parent_name) LIKE "%' . $request->search . '%"');
                    });
                });
            });
        }

        if ($request->filter) {
            if (in_array($request->filter, [BatchItemStatus::IN_STOCK, BatchItemStatus::DISCOUNTED, BatchItemStatus::OUT_OF_STOCK, BatchItemStatus::EXPIRED])) {
                $inventory_batches->whereIn('status', explode(',', $request->filter));
            } else if ($request->filter == "past_best_before") {
                $inventory_batches->whereHas('items', function ($query) {
                    $query->whereHas('dates', function ($q) {
                        $q->whereDate('date', '<=', today());
                    });
                });
            } else if ($request->filter == "upcoming_best_before") {
                $inventory_batches->whereHas('items', function ($query) {
                    $query->whereHas('dates', function ($q) {
                        $q->whereDate('date', '>', today());
                    });
                });
            } else if ($request->filter == "unverified") {
                $inventory_batches->doesntHave('verifications')
                    ->whereHas('items', function ($q) {
                        $q->where('inventory_batch_products.quantity', '>', 0);
                    })
                    ->whereIn('status', [BatchItemStatus::IN_STOCK, BatchItemStatus::DISCOUNTED]);
            }
        }

        if ($request->order_by && in_array($request->order_by, ['name', 'lot_number', 'bin_location', 'status', 'expiry_date', 'created_at', 'best_before', 'quantity', 'last_verified_at'])) {
            $order_direction = $request->order_direction ?: 'desc';
            switch ($request->order_by) {
                case 'expiry_date':
                case 'quantity':
                    $inventory_batches->orderBy(
                        BatchItem::select($request->order_by)
                            ->whereColumn('inventory_batch_products.inventory_batch_id', 'inventory_batches.id')
                            ->latest()
                            ->take(1),
                        $order_direction
                    );
                    break;
                case 'best_before':
                    $inventory_batches->orderBy(
                        BatchItem::select('inventory_batch_product_dates.date')
                            ->whereColumn('inventory_batch_products.inventory_batch_id', 'inventory_batches.id')
                            ->join('inventory_batch_product_dates', 'inventory_batch_products.id', '=', 'inventory_batch_product_dates.batch_item_id')
                            ->latest()
                            ->take(1),
                        $order_direction
                    );
                    break;
                case 'last_verified_at':
                    $inventory_batches->orderBy(
                        InventoryBatchVerification::select('created_at')
                            ->whereColumn('inventory_batch_id', 'inventory_batches.id')
                            ->orderByDesc('id')
                            ->limit(1),
                        $order_direction
                    );
                    break;
                default:
                    $inventory_batches->orderBy($request->order_by, $order_direction ?: 'desc');
            }
        }

        if ($request->product_id) {
            $inventory_batches->whereHas('items', function ($q) use ($request) {
                $q->where('product_id', $request->product_id);
            });
        }

        $inventory_batches = $this->paginate($inventory_batches, 25);

        $inventory_batches['data'] = array_map(function ($_inventory_batch) {
            $order_ids = $this->getOrdersByBatch($_inventory_batch['id'], 'id');
            $_inventory_batch['order_count'] = count($order_ids);
            return $_inventory_batch;
        }, $inventory_batches['data']);

        return json_resp($inventory_batches);
    }

    public function show(InventoryBatch $inventory_batch)
    {
        $merchant = auth()->user();

        // ToDo: implement in middleware
        if ($inventory_batch->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to inventory batch.');
        }

        $inventory_batch->load(['location', 'items.product', 'items.dates']);
        $inventory_batch->has_order_assigned = $inventory_batch->hasAssignedToOrderItem();

        return json_resp($inventory_batch);
    }

    public function assignedOrders($inventory_batch_id)
    {
        $inventory_batch = InventoryBatch::find($inventory_batch_id);
        $orders = $this->getOrdersByBatch($inventory_batch_id);
        return json_resp([
            'inventory_batch' => $inventory_batch,
            'orders' => $orders
        ]);
    }

    /**
     * API used by POS UI extension
     * Usage: To get nearest expiring batch to display pop-up on user add item to cart
     * Request param: line_items - [{variantId: 1, quantity: 1}, {variantId: 2, quantity: 2}]
     */
    public function getNearestExpiringBatch(Request $request)
    {
        $merchant = auth()->user();
        $variant_ids = Arr::pluck($request->line_items, 'variantId');
        $products = $merchant->products()->whereIn('shopify_variant_id', $variant_ids)->get();
        if (!$products->count()) {
            return json_resp([
                'inventory_batches' => []
            ]);
        }

        $inventory_batches = [];
        foreach ($request->line_items as $line_item) {
            $variant_id = $line_item['variantId'];
            $requested_quantity = $line_item['quantity'];
            $batch_data = [];
            $product = $products->where('shopify_variant_id', $variant_id)->first();
            if (!$product) {
                $inventory_batches[] = [
                    'variant_id' => $variant_id,
                    'inventory_batches' => $batch_data
                ];
                continue;
            }

            // Get all available batch items ordered by nearest expiry
            $batch_items = $product->batch_items()
                ->expiringSoon()
                ->orderNearestAuto()
                ->get();

            $remaining_quantity = $requested_quantity;

            foreach ($batch_items as $batch_item) {
                if ($remaining_quantity <= 0) {
                    break;
                }

                $assignable_quantity = min($batch_item->quantity, $remaining_quantity);

                if ($assignable_quantity > 0) {
                    $inventory_batch = $batch_item->batch;
                    $batch_data[] = [
                        'id' => $inventory_batch->id,
                        'name' => $inventory_batch->name,
                        'location' => $inventory_batch->location->name,
                        'lot_number' => $inventory_batch->lot_number,
                        'bin_location' => $inventory_batch->bin_location,
                        'barcode' => $inventory_batch->barcode,
                        'expiry_date' => $batch_item->expire_at,
                        'best_before_date' => $batch_item->dates->whereNull('deleted_at')->pluck('date'),
                        'quantity' => $assignable_quantity
                    ];

                    $remaining_quantity -= $assignable_quantity;
                }
            }

            $inventory_batches[] = [
                'variant_id' => $variant_id,
                'inventory_batches' => $batch_data
            ];
        }

        return json_resp([
            'inventory_batches' => $inventory_batches
        ]);
    }

    public function store(Request $request)
    {
        $merchant = auth()->user();

        self::$shop = $merchant->subdomain;
        self::$access_token = $merchant->access_token;

        $request->validate([
            'location_id' => [
                'required',
                Rule::exists('locations', 'id')->where(function ($query) use ($merchant) {
                    return $query->where('merchant_id', $merchant->id);
                })
            ],
            'items' => 'required|array',
            'items.*.product_id' => [
                'required',
                Rule::exists('products', 'id')->where(function ($query) use ($merchant) {
                    return $query->where('merchant_id', $merchant->id);
                })
            ],
            'items.*.quantity' => 'required',
            'items.*.expire_at' => 'date_format:"Y-m-d"|nullable',
            'items.*.dates' => 'array|nullable',
            'is_sync_quantity' => 'required|boolean',
            'name' => 'string|nullable',
            'received_at' => 'date_format:"Y-m-d"|nullable',
            'lot_number' => 'string|nullable',
            'bin_location' => 'string|nullable',
            'barcode' => 'string|nullable',
            'invoice_number' => 'string|nullable',
            'description' => 'string|nullable'
        ]);

        $merchantSettings = $merchant->setting();
        if ($merchantSettings['plan'] == PlanEnum::FREE) {
            try {
                $merchant->checkBatchLimit($request->items);
            } catch (ExpiryInventoryException $e) {
                if ($e->getExceptionType() === 'batch_limit_exceeded') {
                    return json_resp(['required_plan_upgrade' => true], $e->getMessage(), 403);
                }
            }
        }

        $request->merge([
            'merchant_id' => $merchant->id,
            'name' => $request->name ?: 'Created at ' . Carbon::now()->toJSON()
        ]);

        $inventory_batch = $this->save($request->only([
            'merchant_id',
            'location_id',
            'name',
            'received_at',
            'lot_number',
            'bin_location',
            'barcode',
            'invoice_number',
            'description',
            'is_sync_quantity',
            'meta'
        ]));

        $this->syncBatchProducts($inventory_batch, $request);

        $inventory_batch->refresh();

        return json_resp($inventory_batch, 'Success');
    }

    public function update(Request $request, InventoryBatch $inventory_batch)
    {
        $merchant = auth()->user();

        if ($inventory_batch->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to inventory batch.');
        }

        self::$shop = $merchant->subdomain;
        self::$access_token = $merchant->access_token;

        $request->validate([
            'items' => 'required|array',
            'items.*.product_id' => [
                'required',
                Rule::exists('products', 'id')->where(function ($query) use ($merchant) {
                    return $query->where('merchant_id', $merchant->id);
                })
            ],
            'items.*.quantity' => 'required',
            'items.*.expire_at' => 'date_format:"Y-m-d"|nullable',
            'items.*.dates' => 'array|nullable',
            'is_sync_quantity' => 'required|boolean',
            'name' => 'string|nullable',
            'received_at' => 'date_format:"Y-m-d"|nullable',
            'lot_number' => 'string|nullable',
            'bin_location' => 'string|nullable',
            'barcode' => 'string|nullable',
            'invoice_number' => 'string|nullable',
            'description' => 'string|nullable',
            'adjusted_by' => 'string|nullable'
        ]);

        $merchantSettings = $merchant->setting();
        if ($merchantSettings['plan'] == PlanEnum::FREE) {
            try {
                $merchant->checkBatchLimit($request->items);
            } catch (ExpiryInventoryException $e) {
                if ($e->getExceptionType() === 'batch_limit_exceeded') {
                    return json_resp(['required_plan_upgrade' => true], $e->getMessage(), 403);
                }
            }
        }

        $inventory_batch = $this->save($request->only([
            'name',
            'received_at',
            'lot_number',
            'bin_location',
            'barcode',
            'invoice_number',
            'description',
            'is_sync_quantity',
            'meta',
        ]), $inventory_batch);

        if ($inventory_batch->status != BatchItemStatus::EXPIRED) {
            $this->syncBatchProducts($inventory_batch, $request, 'update');
        }

        return json_resp([], 'Success');
    }

    public function delete(InventoryBatch $inventory_batch)
    {
        $merchant = auth()->user();

        if ($inventory_batch->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to inventory batch.');
        }

        // if ($inventory_batch->hasAssignedToOrderItem()) {
        //     abort(400, 'Cannot delete batch with order assigned.');
        // }

        $inventory_batch->delete();

        return json_resp([], 'Success');
    }

    public function history(Request $request)
    {
        $merchant = auth()->user();

        $inventory_histories = $merchant->inventory_histories();

        $response = [];
        if ($request->product) {
            $product = Product::findOrFail($request->product);
            $response['product'] = $product;
            $inventory_histories->with([
                'batch' => function ($q) {
                    $q->withTrashed();
                }
            ])->whereHas('batch.items', function ($q) use ($request) {
                $q->where('inventory_batch_products.product_id', $request->product);
            });
        }
        if ($request->location) {
            $inventory_histories->whereHas('batch', function ($q) use ($request) {
                $q->withTrashed()->where('inventory_batches.location_id', $request->location);
            });
        }
        if ($request->import_history) {
            $import_history = ImportHistory::findOrFail($request->import_history);
            $response['import'] = $import_history;
            $inventory_histories->where('import_id', $request->import_history);
            info($inventory_histories->toSql());
        }
        $inventory_histories->orderBy('created_at', 'DESC');

        $response['history'] = $this->paginate($inventory_histories, 25);

        return json_resp($response);
    }

    public function import(Request $request)
    {
        $merchant = auth()->user();

        $request->validate([
            'batch_import' => 'required|file'
        ]);

        $file = $request->file('batch_import');
        $filename = $file->getClientOriginalName();
        $path = $file->storeAs(
            'imports',
            $filename,
            's3'
        );

        $import_history = ImportHistory::create([
            'merchant_id' => $merchant->id,
            'model' => ImportEnum::INVENTORY_BATCH,
            'path' => $filename
        ]);

        $import = new InventoryBatchImport($merchant, $import_history);
        $import->queue($path, 's3');

        return json_resp([], 'Success');
    }

    public function export(Request $request)
    {
        $merchant = auth()->user();

        $request->validate([
            'from' => 'required|date',
            'to' => 'required|date',
            'status' => 'string|nullable',
            'location' => 'numeric|nullable',
        ]);
        $options = [];

        if (isset($request->status)) {
            $options['status'] = $request->status;
        }

        if (isset($request->location)) {
            $options['location'] = $request->location;
        }

        $exporter = new ExportHelper(ExportEnum::INVENTORY_BATCH, "downloads", $options);
        if (isset($request->from)) {
            $exporter->setFrom($request->from);
        }
        if (isset($request->to)) {
            $exporter->setTo($request->to);
        }

        try {
            $result = $exporter->export($merchant);
            if ($result) {
                return json_resp([], 'Success');
            } else {
                return json_resp([], 'Failed to export batches.', 400);
            }
        } catch (\Exception $e) {
            return json_resp([], 'Failed to export batches.', 400);
        }
    }

    public function clearBatchesData(Request $request)
    {
        $request->validate([
            'password' => 'required|current_password:shopify'
        ]);

        $merchant = auth()->user();

        $password_matches = $merchant->checkPassword($request->password);
        if (!$password_matches) {
            return json_resp([], 'Incorrect password.', 403);
        }

        HandleClearMerchantBatchesData::dispatch($merchant);

        return json_resp([], 'Success');
    }

    protected function getOrdersByBatch($inventory_batch_id, $field = '*')
    {
        $inventory_batch = InventoryBatch::find($inventory_batch_id);
        $orders = [];
        foreach ($inventory_batch->items as $batch_item) {
            if ($field === '*') {
                $_orders = Order::with([
                    'customer.user',
                    'items' => function ($query) use ($batch_item) {
                        $query->whereHas('assignments', function ($q) use ($batch_item) {
                            $q->where('order_products_assignments.batch_item_id', $batch_item->id);
                        });
                    }
                ]);
            } else {
                $_orders = Order::with([]);
            }
            $_orders = $_orders->whereHas('items', function ($query) use ($batch_item) {
                $query->whereHas('assignments', function ($q) use ($batch_item) {
                    $q->where('order_products_assignments.batch_item_id', $batch_item->id);
                });
            })->get($field);
            $orders = array_merge($orders, $_orders->toArray());
        }
        return $orders;
    }

    public function syncBatchProducts(InventoryBatch $inventory_batch, $request, $type = 'new')
    {
        $location = $inventory_batch->location()->first();
        foreach ($request->items as $item) {
            if (gettype($item) == 'object') {
                $item = json_decode(json_encode($item), true);
            }

            $is_new = false;
            $product = Product::find($item['product_id']);
            $batch_item = $product->batch_items()
                ->where('inventory_batch_id', $inventory_batch->id)
                ->firstOr(function () use ($inventory_batch, $product, &$is_new) {
                    $is_new = true;
                    return new BatchItem([
                        'inventory_batch_id' => $inventory_batch->id,
                        'product_id' => $product->id
                    ]);
                });

            $adjustment_quantity = 0;
            if ($type == 'new') {
                $adjustment_quantity = $item['quantity'];
            } else if ($type == 'update') {
                $adjustment_quantity = $item['quantity'] - $batch_item->quantity;
            }

            $is_explicit_save = true;
            if (!$is_new) {
                // check are best before dates the same
                $existing_dates = $batch_item->dates->pluck('date');
                if ($existing_dates == $request->dates) {
                    $is_explicit_save = false;
                }
            }

            $batch_item->fill([
                'quantity' => $item['quantity'],
                'expire_at' => $item['expire_at'],
            ]);
            if ($is_explicit_save) {
                $batch_item->saveQuietly();
                $this->syncBatchItemDates($batch_item, $item['dates']);
                $batch_item->checkForLatestStatus();

                // explicitly apply event jobs
                ApplyDiscount::dispatch(collect([$product]));
                StorefrontExpiryDateHelper::updateStorefrontExpiryDate(collect([$product]));
                $batch_item->batch->checkForLatestStatus();
            } else {
                $this->syncBatchItemDates($batch_item, $item['dates']);
                $batch_item->save();
            }

            if ($request->is_sync_quantity) {
                if (!isset($request->type) || $request->type != 'auto') {
                    $this->syncShopifyInventory($product, $location, $adjustment_quantity);
                }
            }

            $this->createInventoryHistoryEntry($batch_item, $adjustment_quantity, $type, $request->adjusted_by);

            // check if is expired
            if ($batch_item->isExpired()) {
                $adjustment_quantity = -($item['quantity']);
                if ($request->is_sync_quantity) {
                    $this->syncShopifyInventory($product, $location, $adjustment_quantity);
                }

                $this->createInventoryHistoryEntry($batch_item, $adjustment_quantity, BatchItemStatus::EXPIRED, $request->adjusted_by);
            }
        }
    }

    protected function syncBatchItemDates(BatchItem $batch_item, $dates)
    {
        $existing_item_dates = $batch_item->dates;
        foreach ($dates as $date) {
            $existing = $existing_item_dates->firstWhere('date', $date);
            if (!$existing) {
                $batch_item->dates()->create([
                    'date' => $date
                ]);
            }
        }

        foreach ($existing_item_dates as $item_date) {
            $remained = in_array($item_date->date, $dates);
            if (!$remained) {
                $item_date->delete();
            }
        }
    }

    protected function createInventoryHistoryEntry(BatchItem $batch_item, $adjustment_quantity, $type = 'new', $adjusted_by = null)
    {
        if ($adjustment_quantity != 0) {
            $action_slug = null;
            if ($type == 'new') {
                $action_slug = 'batch-created';
            } else if ($type == 'update') {
                $action_slug = 'batch-updated';
            } else if ($type == 'expired') {
                $action_slug = 'batch-marked-expired';
            } else if ($type == 'transfer') {
                $action_slug = 'batch-transfered';
            }
            $batch_action = InventoryAction::getBySlug($action_slug);

            InventoryHistory::createWithBatchItem([
                'merchant_id' => $batch_item->batch->merchant->id,
                'action_id' => $batch_action->id,
                'batch_id' => $batch_item->inventory_batch_id,
                'adjustment' => $adjustment_quantity,
                'adjusted_by' => $adjusted_by,
            ], $batch_item, $this->import_id);
        }
    }

    protected function syncShopifyInventory(Product $product, Location $location, $adjustment_quantity)
    {
        if ($adjustment_quantity != 0) {
            if ($product->locations()->whereKey($location->id)->exists()) {
                $shopify_helper = new ShopifyAPIHelper(self::$shop, self::$access_token);
                $admin_graphql_inventory_item_id = $shopify_helper->getProductVariantInventoryItemID($product->shopify_variant_id);
                // adjust quantity for shopify inventory item
                $shopify_helper->adjustInventoryLevel($admin_graphql_inventory_item_id, $location->shopify_location_id, $adjustment_quantity, ShopifyGraphQLEnum::INVENTORY_ON_HAND_CORRECTION);
            }
        }
    }

    public function getPriorityBatches($product_id)
    {
        $product = Product::find($product_id);
        $nearest_expiry_batch_item = $product->batch_items()->expiringSoon()->scopeOrderByPriorityBestBefore();
        return json_resp($nearest_expiry_batch_item->get());
    }
}
