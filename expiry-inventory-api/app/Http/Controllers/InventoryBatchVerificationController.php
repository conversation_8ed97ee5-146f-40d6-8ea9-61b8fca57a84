<?php

namespace App\Http\Controllers;

use App\Models\InventoryBatch;
use App\Models\InventoryBatchVerification;
use Illuminate\Http\Request;

class InventoryBatchVerificationController extends Controller
{
    public function index(InventoryBatch $inventory_batch)
    {
        $merchant = auth()->user();
        if ($inventory_batch->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to inventory batch.');
        }
        return json_resp(
            InventoryBatchVerification::where('inventory_batch_id', $inventory_batch->id)
                ->orderByDesc('id')
                ->get()
        );
    }

    public function store(Request $request, InventoryBatch $inventory_batch)
    {
        $merchant = auth()->user();
        if ($inventory_batch->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to inventory batch.');
        }
        $request->validate([
            'quantity' => 'required|integer',
            'adjusted_by' => 'required|string',
        ]);

        $verification = InventoryBatchVerification::create([
            'merchant_id' => $merchant->id,
            'inventory_batch_id' => $inventory_batch->id,
            'quantity' => $request->quantity,
            'adjusted_by' => $request->adjusted_by,
        ]);

        return json_resp($verification, 'Success');
    }
}

