<?php

namespace App\Http\Controllers;

use App\Models\Merchant;
use App\Models\Location;
use App\Traits\HasShopifyAuth;
use App\Http\Helpers\ShopifyAPIHelper;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LocationController extends Controller
{
    use HasShopifyAuth;

    public function index(Request $request)
    {
        $merchant = auth('shopify')->user();
        $locations = $merchant->locations();

        if ($request->product_id) {
            $locations = $locations->whereHas('products', function ($query) use ($request) {
                $query->where('product_id', $request->product_id);
            });
        }

        $locations = $locations->get();
        return json_resp($locations);
    }

    public function locationCreated(Request $request)
    {
        Log::info('Locations Created webhook', $request->only([
            'id',
            'name'
        ]));

        $merchant = auth()->user();

        Location::firstOrCreate(
            ['merchant_id' => $merchant->id, 'shopify_location_id' => $request->id],
            ['name' => $request->name]
        );

        return json_resp([], 'Success');
    }

    public function locationUpdated(Request $request)
    {
        Log::info('Locations Updated webhook', $request->only([
            'id',
            'name'
        ]));

        $merchant = auth()->user();

        $location = Location::firstOrNew([
            'merchant_id' => $merchant->id,
            'shopify_location_id' => $request->id
        ]);
        $this->save([
            'name' => $request->name
        ], $location);

        return json_resp([], 'Success');
    }

    public function locationDeleted(Request $request)
    {
        Log::info('Locations Deleted webhook', $request->only([
            'id',
            'name'
        ]));

        $merchant = auth()->user();

        $location = $merchant->locations()->firstWhere('shopify_location_id', $request->id);
        if ($location) {
            $location->delete();
        }

        return json_resp([], 'Success');
    }

    public static function syncShopifyLocations(Merchant $merchant)
    {
        $shopify_locations = ShopifyAPIHelper::withMerchant($merchant)->getLocations();
        $created_locations = [];
        foreach ($shopify_locations as $_location) {
            $location = Location::firstOrCreate(
                ['merchant_id' => $merchant->id, 'shopify_location_id' => $_location->id],
                ['name' => $_location->name]
            );
            array_push($created_locations, $location);
        }
        return $created_locations;
    }
}
