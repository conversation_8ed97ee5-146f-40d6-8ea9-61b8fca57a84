<?php

namespace App\Http\Controllers;

use App\Mail\VerificationCodeMail;
use App\Models\UserVerify;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class MerchantController extends Controller
{
    public function passwordSetupRequiredCheck()
    {
        $merchant = auth()->user();

        return json_resp([ 'is_password_setup_required' => $merchant->isPasswordSetupRequired() ], 'Success');
    }

    public function passwordSetup(Request $request)
    {
        $request->validate([
            'password' => 'required|string|min:8|confirmed'
        ]);

        $merchant = auth()->user();

        if (!$merchant->isPasswordSetupRequired()) {
            return json_resp([], 'Password is already set', 403);
        }

        $merchant->password = Hash::make($request->password);
        $merchant->save();

        return json_resp([], 'Success');
    }

    public function sendEmailVerification()
    {
        $merchant = auth()->user();

        // check can send again (timeout interval)
        $isRecentlyRequested = $merchant->verifications()->recentlyRequested()->exists();
        if ($isRecentlyRequested) {
            return json_resp([], 'Too many attempts, please request again later', 400);
        }

        $email = $merchant->getShopEmail();
        $code = $merchant->getNewVerificationCode($email);
        Mail::to($email)->send(new VerificationCodeMail($merchant->user ? $merchant->user->name : $merchant->subdomain, $code));

        return json_resp([ 'request_timeout_period' => UserVerify::$request_timeout_second ], 'Success');
    }

    public function verifyForgetPasswordToken(Request $request, $json_res = true)
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $merchant = auth()->user();

        $valid_verification = $merchant->verifications()->expired(false)->used(false)->where('token', $request->code)->first();
        if (!$valid_verification) {
            return $json_res ? json_resp([], 'Invalid verification code', 400) : false;
        }

        return $json_res ? json_resp([], 'Success') : $valid_verification;
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
            'password' => 'required|string|min:8|confirmed'
        ]);

        $valid_verification = $this->verifyForgetPasswordToken($request, false);
        if (!$valid_verification) {
            return json_resp([], 'Invalid verification code', 400);
        }

        $merchant = auth()->user();

        $merchant->password = Hash::make($request->password);
        $merchant->save();

        $valid_verification->used_at = now();
        $valid_verification->save();

        return json_resp([], 'Success');
    }
}