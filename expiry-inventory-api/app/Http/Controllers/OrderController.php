<?php

namespace App\Http\Controllers;

use App\Enums\ExportEnum;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Merchant;
use App\Models\OrderItem;
use App\Models\BatchItem;
use App\Models\InventoryAction;
use App\Models\InventoryHistory;
use App\Helper\ExportHelper;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Jobs\HandleSyncShopifyOrder;
use App\Jobs\HandleSyncShopifyProduct;
use App\Models\Config;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    protected $shopify_api_helper = null;

    public function index(Request $request)
    {
        $merchant = auth()->user();

        $orders = $merchant->orders()->with(['customer.user', 'items.product', 'items.batch_items.batch']);

        if ($request->search) {
            $orders = $orders->where(function ($q) use ($request) {
                $q->orWhereRaw('LOWER(orders.order_number) LIKE "%' . $request->search . '%"');
                $q->orWhereHas('items.product', function ($q) use ($request) {
                    $q->where(function ($q) use ($request) {
                        $q->orWhereRaw('LOWER(products.name) LIKE "%' . $request->search . '%"')
                            ->orWhereRaw('LOWER(products.parent_name) LIKE "%' . $request->search . '%"');
                    });
                });
                $q->orWhereHas('customer.user', function ($q) use ($request) {
                    $q->where(function ($q) use ($request) {
                        $q->orWhereRaw('LOWER(users.name) LIKE "%' . $request->search . '%"')
                            ->orWhereRaw('LOWER(users.email) LIKE "%' . $request->search . '%"');
                    });
                });
            });
        }

        if ($request->filter) {
            $orders = $orders->where('batch_assign_status', $request->filter);
        }

        if ($request->order_by && in_array($request->order_by, ['order_number', 'customer', 'amount'])) {
            $order_direction = $request->order_direction ?: 'desc';
            if ($request->order_by == 'customer') {
                $orders->orderBy(
                    User::select('name')
                        ->whereColumn('orders.customer_id', 'users.id'),
                    $order_direction
                );
            } else {
                $orders->orderBy($request->order_by, $order_direction ?: 'desc');
            }
        } else {
            $orders->orderBy('order_number', 'DESC');
        }

        $orders = $this->paginate($orders, 25);

        return json_resp($orders);
    }

    public function show(Order $order)
    {
        $merchant = auth()->user();

        if ($order->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to order.');
        }

        $order->load(['customer.user', 'items.product', 'items.batch_items.dates', 'items.batch_items.batch.location']);

        return json_resp($order);
    }

    public function showByShopifyID($order_id)
    {
        $merchant = auth()->user();

        $order = $merchant->orders()->where('shopify_order_id', $order_id)->firstOrFail();

        $order->load(['customer.user', 'items.product']);

        return json_resp($order);
    }

    public function autoAssignBatch(Order $order, Request $request)
    {
        $merchant = auth()->user();
        $request->validate([
            'manual_order_list' => 'array|nullable',
        ]);
        if ($order->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to order.');
        }

        $errors = $this->assignBatchToOrder($order, $request->manual_order_list);

        return json_resp(['errors' => $errors], 'Success');
    }

    public function unassignBatch(Order $order)
    {
        $merchant = auth()->user();

        if ($order->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to order.');
        }

        $errors = $this->unassignBatchesFromOrder($order);

        return json_resp(['errors' => $errors], 'Success');
    }

    public function bulkAutoAssignBatch(Request $request)
    {
        $merchant = auth()->user();

        $request->validate([
            'order_ids' => 'array',
            'manual_order_list' => 'array|nullable',
        ]);
        $manual_order_list = $request->manual_order_list;
        $errors = Order::whereIn('id', $request->order_ids)->where('merchant_id', $merchant->id)->get()->map(function ($order) use ($manual_order_list) {
            return $this->assignBatchToOrder($order, $manual_order_list);
        })->flatten();

        return json_resp(['errors' => $errors], 'Success');
    }

    public function bulkAutoAssignBatchForType(Request $request, $type)
    {
        $merchant = auth()->user();

        $request->validate([
            'order_ids' => 'array',
            'manual_order_list' => 'array|nullable',
        ]);
        $manual_order_list = $request->manual_order_list;
        if ($type === 'shopify') {
            $errors = Order::whereIn('shopify_order_id', $request->order_ids)->where('merchant_id', $merchant->id)->get()->map(function ($order) use ($manual_order_list) {
                return $this->assignBatchToOrder($order, $manual_order_list);
            })->flatten();

            return json_resp(['errors' => $errors], 'Success');
        } else {
            return $this->bulkAutoAssignBatch($request);
        }
    }

    protected function assignBatchToOrder(Order $order, $manual_order_list = [])
    {
        $errors = [];

        foreach ($order->items()->get() as $order_item) {
            $total_assigned_item_quantity = $order_item->getTotalQuantityAssigned();

            if ($order_item->quantity > $total_assigned_item_quantity) {
                $assigned_quantity = $this->assignBatch($order_item, manual_order_list: $manual_order_list);

                if (!$assigned_quantity) {
                    // items cannot be assigned
                    $errors[] = "Order #{$order->order_number}: Item - {$order_item->product->parent_name}" . ($order_item->product->parent_name != $order_item->product->name ? "(" . $order_item->product->name . ")" : "") . " cannot be assigned";
                } else if ($assigned_quantity < $order_item->quantity) {
                    // items partially assigned
                    $errors[] = "Order #{$order->order_number}: Item - {$order_item->product->parent_name}" . ($order_item->product->parent_name != $order_item->product->name ? "(" . $order_item->product->name . ")" : "") . " partially assigned ({$assigned_quantity})";
                }
            }
        }

        if (count($errors) <= 0) {
            $order->batch_assign_status = 'assigned';
            $order->save();
        }

        return $errors;
    }

    protected function unassignBatchesFromOrder(Order $order)
    {
        $errors = [];

        $order_updated_action = InventoryAction::getBySlug('order-updated');

        foreach ($order->items()->get() as $order_item) {
            $order_item->batch_items->whereNull('deleted_at')->each(function ($batch_item) use ($order, $order_item, $order_updated_action) {
                $batch_item->quantity += $batch_item->pivot->quantity;
                $batch_item->save();

                InventoryHistory::createWithBatchItem([
                    'merchant_id' => $order->merchant_id,
                    'action_id' => $order_updated_action->id,
                    'batch_id' => $batch_item->inventory_batch_id,
                    'order_id' => $order_item->order_id,
                    'adjustment' => $batch_item->pivot->quantity
                ], $batch_item);
            });

            $order_item->assignments()->delete();
        }

        if (count($errors) <= 0) {
            $order->batch_assign_status = 'unassigned';
            $order->save();
        }

        return $errors;
    }

    public function export(Request $request)
    {
        $merchant = auth()->user();

        $request->validate([
            'from' => 'required|date',
            'to' => 'required|date',
            'filter' => 'string|in:assigned,unassigned|nullable'
        ]);
        $options = [];

        if (isset($request->filter)) {
            $options['batch_assign_status'] = $request->filter;
        }

        $exporter = new ExportHelper(ExportEnum::ORDER, "downloads", $options);
        if (isset($request->from)) {
            $exporter->setFrom($request->from);
        }
        if (isset($request->to)) {
            $exporter->setTo($request->to);
        }

        try {
            $result = $exporter->export($merchant);
            if ($result) {
                return json_resp([], 'Success');
            } else {
                return json_resp([], 'Failed to export orders.', 400);
            }
        } catch (\Exception $e) {
            return json_resp([], 'Failed to export orders.', 400);
        }
    }

    public function getDefaultPrintTemplate()
    {
        return json_resp(Config::getDefaultConfig('print_settings'));
    }

    public function print(Request $request, Order $order)
    {
        $merchant = auth()->user();

        if ($order->merchant_id != $merchant->id) {
            abort(403, 'Forbidden access to order.');
        }

        $specificItems = $this->mapPrintSpecificItems($request->itemstr);

        $order->load([
            'items.product',
            'customer'
        ])->get();

        $data = $order->only([
            'order_number',
            'amount',
            'billing_address',
            'shipping_address',
            'note',
            'created_at'
        ]);

        $order_items = $order->items;

        $items = [];
        foreach ($order_items as $order_item) {
            $product = $order_item->product;

            $assignments = [];
            if ($specificItems) {
                $assignments = Arr::get($specificItems, $order_item->id);
                if (is_null($assignments)) {
                    continue;
                }
            }

            $item = [
                'image' => $product ? $product->image_url : null,
                'name' => $product ? ($product->parent_name . ($product->name != $product->parent_name ? " ({$product->name})" : '')) : $order_item->name,
                'price' => $order_item->price
            ];

            $total_quantity = 0;
            $assigned_batches = [];
            foreach ($order_item->batch_items as $item_assigned) {
                $assigned_quantity = null;
                if (count($assignments)) {
                    $assigned_quantity = Arr::get($assignments, $item_assigned->pivot->id);
                    // skip if not included in specific items
                    if (!$assigned_quantity) {
                        continue;
                    }
                }
                $assigned_batch_quantity = $assigned_quantity ?: $item_assigned->pivot->quantity;
                $assigned_batches[] = [
                    'id' => $item_assigned->batch->id,
                    'name' => $item_assigned->batch->name,
                    'location' => $item_assigned->batch->location->name,
                    'expire_at' => $item_assigned->expire_at,
                    'best_before' => $item_assigned->dates->whereNull('deleted_at')->pluck('date'),
                    'received_at' => $item_assigned->batch->received_at,
                    'lot_number' => $item_assigned->batch->lot_number,
                    'bin_location' => $item_assigned->batch->bin_location,
                    'barcode' => $item_assigned->batch->barcode,
                    'invoice_number' => $item_assigned->batch->invoice_number,
                    'description' => $item_assigned->batch->description,
                    'quantity' => $assigned_batch_quantity
                ];
                $total_quantity += $assigned_batch_quantity;
            }
            $item['batches'] = $assigned_batches;

            if (count($assignments)) {
                $unassigned_quantity = Arr::get($assignments, "N");
                if ($unassigned_quantity) {
                    $total_quantity += $unassigned_quantity;
                }
                $item['quantity'] = $total_quantity;
            }
            else {
                $item['quantity'] = $order_item->quantity;
            }

            $items[] = $item;
        }

        $merchantSetting = $merchant->setting('configs', false) ?: [];

        $data['items'] = $items;
        $data['shop_name'] = $merchant->user->name;
        $data['shop_contact'] = $merchant->user->email;
        $data['shop_url'] = $merchant->subdomain;
        $data['shop'] = Arr::get($merchantSetting, 'shop', []);

        return json_resp($data);
    }

    public function orderCreated(Request $request)
    {
        Log::info('Orders Created Webhook', $request->only([
            'id',
            'current_total_price',
            'order_number',
            'line_items',
            'financial_status',
            'fulfillment_status',
            'customer',
            'billing_address',
            'shipping_address',
            'note',
        ]));

        $merchant = auth()->user();

        HandleSyncShopifyOrder::dispatch($request->id, $merchant, 'create');
        // HandleSyncShopifyOrder::dispatch(json_decode(json_encode($request->all()), FALSE), $merchant, 'create');

        return json_resp([], 'Success');
    }

    public function orderUpdated(Request $request)
    {
        Log::info('Orders Updated Webhook', $request->only([
            'id',
        ]));

        $merchant = auth()->user();

        HandleSyncShopifyOrder::dispatch($request->id, $merchant, 'update');
        // HandleSyncShopifyOrder::dispatch(json_decode(json_encode($request->all()), FALSE), $merchant, 'update');
        // $data = json_decode(json_encode($request->all()), FALSE);
        // $data->line_items = array_map(function ($line_item) {
        //     return [
        //         'id' => Arr::get($line_item, 'id'),
        //         'product_id' => Arr::get($line_item, 'product_id'),
        //         'variant_id' => Arr::get($line_item, 'variant_id'),
        //         'quantity' => Arr::get($line_item, 'quantity'),
        //         'price' => Arr::get($line_item, 'price'),
        //     ];
        // }, json_decode(json_encode($data->line_items), true));

        // HandleSyncShopifyOrder::dispatch($data, $merchant, 'update');

        return json_resp([], 'Success');
    }

    public function orderCancelled(Request $request)
    {
        Log::info('Orders Cancelled Webhook', $request->only([
            'id',
            'current_total_price',
            'order_number',
            'line_items',
            'financial_status',
            'fulfillment_status',
            'customer'
        ]));

        $merchant = auth()->user();

        HandleSyncShopifyOrder::dispatch(json_decode(json_encode($request->all()), FALSE), $merchant, 'cancel');

        return json_resp([], 'Success');
    }

    public function orderDeleted(Request $request)
    {
        Log::info('Orders Deleted Webhook', [
            'id' => $request->id
        ]);

        $merchant = auth()->user();

        HandleSyncShopifyOrder::dispatch(json_decode(json_encode($request->all()), FALSE), $merchant, 'delete');

        return json_resp([], 'Success');
    }

    public function syncOrderDetailsFromShopify($shopify_order, Merchant $merchant, Customer $customer = null)
    {
        $order = Order::firstOrNew([
            'shopify_order_id' => $shopify_order->id,
            'customer_id' => $customer ? $customer->id : null,
            'merchant_id' => $merchant->id
        ]);

        $this->save([
            'order_number' => $shopify_order->order_number,
            'amount' => $shopify_order->current_total_price,
            'billing_address' => $shopify_order->billing_address,
            'shipping_address' => $shopify_order->shipping_address,
            'note' => $shopify_order->note,
            'status' => $shopify_order->financial_status,
            'fulfillment_status' => $shopify_order->fulfillment_status == 'fulfilled' ? 'fulfilled' : 'unfulfilled'
        ], $order);

        return $order;
    }

    public function syncOrderItemsFromShopify(Order $order, Merchant $merchant, $line_items, $fulfillments = [], $refunds = [])
    {
        $this->shopify_api_helper = ShopifyAPIHelper::withMerchant($merchant);
        $fulfillment_items = count($fulfillments) > 0 ? $this->mapFulfillmentItems($fulfillments) : [];
        foreach ($line_items as $line_item) {
            $product = $this->getOrCreateProduct($line_item, $merchant);

            if ($product) {
                $order_item = OrderItem::firstOrNew([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                ]);
            }
            else {
                $order_item = OrderItem::firstOrNew([
                    'order_id' => $order->id,
                    'line_item_id' => $line_item['id'],
                ]);
            }

            $order_item->fill([
                'line_item_id' => $line_item['id'],
                'product_id' => $product ? $product->id : null,
                'name' => $line_item['name'],
                'price' => $line_item['price'],
                'quantity' => $line_item['current_quantity']
            ])->save();

            // check is there a need to revert quantity assigned from batch
            $assigned_quantity = $order_item->getTotalQuantityAssigned();
            if ($assigned_quantity > $order_item->quantity) {
                $quantity_to_revert = $assigned_quantity - $order_item->quantity;
                $order_item = $this->revertItemAssignment($order_item, $quantity_to_revert, $merchant);
            }

            if (isset($fulfillment_items[$line_item['id']])) {
                $this->assignBatch($order_item, $merchant, $fulfillment_items[$line_item['id']]);
            }
        }
    }

    public function revertItemAssignment(OrderItem $order_item, $quantity_to_revert, $merchant = null)
    {
        $merchant = $merchant ?: auth()->user();
        $order_updated_action = InventoryAction::getBySlug('order-updated');

        $items_assigned = $order_item->assignments->toArray();

        // revert from the end of assigned array (longer expiry date)
        for ($index = count($items_assigned) - 1; $index >= 0; $index--) {
            $batch_item = BatchItem::find($items_assigned[$index]['batch_item_id']);

            $assigned_quantity = $items_assigned[$index]['quantity'];
            $quantity_to_remove = 0;
            if ($assigned_quantity > $quantity_to_revert) {
                // adjust assigned quantity
                $quantity_to_remove = $quantity_to_revert;
                $items_assigned[$index]['quantity'] = $assigned_quantity - $quantity_to_remove;
            } else {
                // remove assigned batch
                $quantity_to_remove = $assigned_quantity;
                array_pop($items_assigned);
            }

            // revert to batch
            $batch_item->quantity += $quantity_to_remove;
            $batch_item->save();

            // record history
            InventoryHistory::createWithBatchItem([
                'merchant_id' => $merchant->id,
                'action_id' => $order_updated_action->id,
                'batch_id' => $batch_item->inventory_batch_id,
                'order_id' => $order_item->order_id,
                'adjustment' => $quantity_to_remove
            ], $batch_item);

            $quantity_to_revert -= $quantity_to_remove;
            if ($quantity_to_revert <= 0) {
                break;
            }
        }
        $order_item->batch_items()->sync($items_assigned);
        $order_item->save();

        return $order_item;
    }

    public function getOrCreateCustomer($customer_data, Merchant $merchant)
    {
        if (is_null($customer_data)) {
            return null;
        }
        $user_data = [
            'name' => implode(' ', [
                isset($customer_data->first_name) ? $customer_data->first_name : null,
                isset($customer_data->last_name) ? $customer_data->last_name : null
            ]),
            'email' => isset($customer_data->email) ? $customer_data->email : null,
            'phone' => isset($customer_data->phone) ? $customer_data->phone : null,
        ];
        $customer = Customer::firstOrNew([
            'shopify_customer_id' => $customer_data->id,
            'merchant_id' => $merchant->id
        ]);
        $customer->fill($user_data)->save();
        $customer->refresh();
        return $customer;
    }

    protected function assignBatch(OrderItem $order_item, $merchant = null, $fulfillment_locations = [], $manual_order_list = [])
    {
        $merchant = $merchant ?: auth()->user();
        $product = $order_item->product;
        $total_quantity_assigned = $order_item->getTotalQuantityAssigned();

        if ($total_quantity_assigned > $order_item->quantity) {
            $order_item = $this->revertItemAssignment($order_item, $total_quantity_assigned, $merchant);
            return $order_item->getTotalQuantityAssigned();
        } else if ($order_item->quantity == $total_quantity_assigned) {
            return $total_quantity_assigned;
        }

        $items_assigned = [];
        $order_item->assignments->each(function ($assign_item) {
            $items_assigned[$assign_item['batch_item_id']] = [
                'quantity' => $assign_item['quantity']
            ];
        });

        $in_stock_batch_exists = $product->batch_items()
            ->expiringSoon()

            ->exists();

        if (!$in_stock_batch_exists) {
            return $total_quantity_assigned;
        }

        $assigned_quantity = 0;

        if (count($fulfillment_locations) > 0) {
            foreach ($fulfillment_locations as $shopify_location_id => $fulfilled_location) {
                $q = $product->batch_items()->with('batch')
                    ->whereHas('batch', fn($query) => $query->whereHas('location', fn($q) => $q->where('shopify_location_id', $shopify_location_id)))
                    ->expiringSoon();


                if ($manual_order_list && count($manual_order_list)) {
                    $available_batch_items_in_location = $q->orderManually($manual_order_list)->get();
                } else {
                    $available_batch_items_in_location = $q->orderNearestAuto()->get();
                }

                $assigned_quantity += $this->assignBatchItemsToOrderItem($available_batch_items_in_location, $order_item, $merchant, $items_assigned, $fulfilled_location['quantity']);
            }
        } else {
            $q = $product->batch_items()->with('batch')
                ->expiringSoon();


            if ($manual_order_list && count($manual_order_list)) {
                $available_item_batches = $q->orderManually($manual_order_list)->get();
            } else {
                $available_item_batches = $q->orderNearestAuto()->get();
            }

            $assigned_quantity += $this->assignBatchItemsToOrderItem($available_item_batches, $order_item, $merchant, $items_assigned, $order_item->quantity);
        }

        $order_item->batch_items()->sync($items_assigned);
        $order_item->save();

        return $assigned_quantity;
    }

    protected function mapFulfillmentItems($fulfillments)
    {
        $fulfillment_items = [];
        foreach ($fulfillments as $fulfillment) {
            if ($fulfillment['status'] === 'success') {
                foreach ($fulfillment['line_items'] as $fulfillment_item) {
                    $item = [];
                    if (isset($fulfillment_items[$fulfillment_item['id']])) {
                        $item = $fulfillment_items[$fulfillment_item['id']];
                    }

                    $fulfill_location = isset($item[$fulfillment['location_id']]) ? $item[$fulfillment['location_id']] : ['quantity' => 0];
                    $fulfill_location['quantity'] += $fulfillment_item['quantity'];

                    $item[$fulfillment['location_id']] = $fulfill_location;

                    $fulfillment_items[$fulfillment_item['id']] = $item;
                }
            }
        }
        return $fulfillment_items;
    }

    protected function assignBatchItemsToOrderItem($assignable_batch_items, OrderItem $order_item, Merchant $merchant, &$items_assigned, $quantity_to_assign)
    {
        $order_created_action = InventoryAction::getBySlug('order-created');
        $assigned_quantity = 0;
        foreach ($assignable_batch_items as $batch_item) {
            $assignable_quantity = $quantity_to_assign > $batch_item->quantity ? $batch_item->quantity : $quantity_to_assign;

            $batch_item->quantity -= $assignable_quantity;
            $batch_item->save();

            $items_assigned[$batch_item->id] = [
                'quantity' => $assignable_quantity
            ];

            InventoryHistory::createWithBatchItem([
                'merchant_id' => $merchant->id,
                'action_id' => $order_created_action->id,
                'batch_id' => $batch_item->inventory_batch_id,
                'order_id' => $order_item->order_id,
                'adjustment' => -($assignable_quantity)
            ], $batch_item);

            $quantity_to_assign -= $assignable_quantity;
            $assigned_quantity += $assignable_quantity;

            if ($quantity_to_assign <= 0) {
                break;
            }
        }
        return $assigned_quantity;
    }

    protected function getOrCreateProduct($line_item, Merchant $merchant)
    {
        if (!$line_item['product_exists']) return null;

        $product = Product::where([
            'merchant_id' => $merchant->id,
            'shopify_variant_id' => $line_item['variant_id']
        ])->firstOr(function () use ($line_item, $merchant) {
            $variant = $this->shopify_api_helper->getProductVariant($line_item['variant_id']);
            $product = Product::firstOrCreate([
                'merchant_id' => $merchant->id,
                'shopify_variant_id' => extract_shopify_integer_id($variant->id),
                'parent_id' => extract_shopify_integer_id($variant->product->id)
            ], [
                'name' => $variant->title == "Default Title" ? $variant->product->title : $variant->title,
                'parent_id' => extract_shopify_integer_id($variant->product->id),
                'parent_name' => $variant->product->title,
                'quantity' => array_sum(Arr::pluck($variant->inventoryItem->inventoryLevels->nodes, 'quantities.0.quantity')),
                'sku' => $variant->sku,
                'price' => $variant->price,
                'status' => Str::lower($variant->product->status),
                'original_price' => $variant->price,
                'compare_price' => $variant->compareAtPrice,
                'is_inventory_managed' => $variant->inventoryItem->tracked,
                'image_url' => $variant->title == "Default Title" ? ($variant->product->featuredImage ? $variant->product->featuredImage->url : null) : ($variant->image ? $variant->image->url : null)
            ]);
            return $product;
        });
        HandleSyncShopifyProduct::dispatch($product->parent_id, $merchant);
        return $product;
    }

    // items separated by |
    // Order item - order_item_id
    // Order item, assigned and quantity - order_item_id;assignment_id:quantity
    // Order item, not-assigned and quantity - order_item_id;N:quantity
    // Order item, multiple assignments - order_item_id;assignment_id:quantity,assignment_id:quantity

    // example
    // 1|2;1:2,N:1|3;N:2
    private function mapPrintSpecificItems($encrypted_items_string)
    {
        $decyptedItemsString = $this->decryptItemsString($encrypted_items_string);

        if (!$decyptedItemsString) return null;

        $items = explode("|", $decyptedItemsString);

        $specificItems = [];
        array_walk($items, function ($value) use (&$specificItems) {
            $ids_assigned = explode(";", $value);
            $order_item_id = $ids_assigned[0];
            $assignments = [];
            if (count($ids_assigned) > 1) {
                $assignments = $this->extractPackingSlipItemAssignedQuantity(explode(",", $ids_assigned[1]));
            }
            $specificItems[$order_item_id] = $assignments;
        });

        return $specificItems;
    }

    private function extractPackingSlipItemAssignedQuantity($assigned_items) {
        $assignments = [];
        array_walk($assigned_items, function ($value) use (&$assignments) {
            $assigned_quantity = explode(":", $value);
            $assignments[$assigned_quantity[0]] = $assigned_quantity[1];
        });
        return $assignments;
    }

    private function decryptItemsString($encrypted_items_string)
    {
        if (!$encrypted_items_string) return false;

        $cipher = 'AES-256-CBC';
        $passphrase = config('services.shopify.enc_passphrase');

        $ivlen = openssl_cipher_iv_length($cipher) * 2; // hex
        $sha2len = (32*2); // hex
        $iv = hex2bin(substr($encrypted_items_string, 0, $ivlen));
        $hmac = hex2bin(substr($encrypted_items_string, $ivlen, $sha2len));
        $ciphertext_raw = hex2bin(substr($encrypted_items_string, $ivlen + $sha2len));
        $generated_hmac = hash_hmac('sha256', $ciphertext_raw, $passphrase, $as_binary=true);

        if (!hash_equals($generated_hmac, $hmac)) {
            return false;
        }

        return openssl_decrypt($ciphertext_raw, $cipher, $passphrase, $options=OPENSSL_RAW_DATA, $iv);
    }
}
