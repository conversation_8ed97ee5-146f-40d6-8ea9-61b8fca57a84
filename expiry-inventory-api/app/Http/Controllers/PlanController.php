<?php

namespace App\Http\Controllers;

use App\Enums\PlanEnum;
use App\Enums\ShopifyGraphQLEnum;
use App\Enums\SubscriptionStatus;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Jobs\HandleSyncShopifySubscription;
use App\Models\Config;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PlanController extends Controller
{
    public function index()
    {
        return json_resp(Config::getDefaultConfig('plan_settings'));
    }

    public function update($charge_id)
    {
        $merchant = auth()->user();
        return $this->syncShopifyPlan($charge_id, $merchant);
    }

    public function upgradePlan(Request $request)
    {
        $merchant = auth()->user();

        $plan_type = isset($request->plan_type) ? $request->plan_type : 'pro';
        $plan_period = isset($request->plan_period) ? $request->plan_period : 'month';

        $plan_settings = Config::getDefaultConfig('plan_settings');

        $recurringCharge = ShopifyAPIHelper::withMerchant($merchant)->createRecurringCharge(
            $plan_settings[$plan_type]['name'],
            $plan_settings[$plan_type][$plan_period]['price'],
            'https://' . $merchant->subdomain . '/admin/apps/' . config('services.shopify.key') . '/plans',
            $plan_period,
            $merchant->test
        );

        if (!isset($recurringCharge->appSubscriptionCreate)) {
            abort(400, 'Charge creation failed');
        }
        $appSubscription = $recurringCharge->appSubscriptionCreate;
        if (count($appSubscription->userErrors) > 0) {
            abort(400, $appSubscription->userErrors[0]->message);
        }

        $admin_graphql_id = $appSubscription->appSubscription->id;

        $charge_id = extract_shopify_integer_id($admin_graphql_id);

        $merchant->subscriptions()->create([
            'charge_id' => $charge_id,
            'type' => $plan_type,
            'billing_period' => $plan_period,
            'test' => $merchant->test
        ]);

        return json_resp([
            'confirmation_url' => $appSubscription->confirmationUrl
        ]);
    }

    public function downgradePlan()
    {
        $merchant = auth()->user();

        $active_subscription = $merchant->subscriptions()->where('status', 'active')->first();
        if ($active_subscription) {
            ShopifyAPIHelper::withMerchant($merchant)->deleteRecurringCharge($active_subscription->charge_id);
            $active_subscription->fill([
                'status' => SubscriptionStatus::CANCELLED
            ])->save();
        }

        $merchantSettings = $merchant->setting();
        $merchantSettings['plan'] = PlanEnum::FREE;
        $merchantSettings['billing_period'] = 'month';
        $merchant->saveSetting($merchantSettings);

        return json_resp($active_subscription);
    }

    public function syncShopifyPlan($charge_id, $merchant, $abort_on_failed = true)
    {

        $admin_graphql_charge_id = generate_shopify_graphql_id($charge_id, ShopifyGraphQLEnum::APP_SUBSCRIPTION);
        $recurringCharge = ShopifyAPIHelper::withMerchant($merchant)->getCurrentAppSubscriptions($admin_graphql_charge_id);
        if (!$recurringCharge) {
            if ($abort_on_failed) {
                abort(400, 'Invalid charge ID');
            }
        }

        if (count($recurringCharge->currentAppInstallation->activeSubscriptions) <= 0) {
            abort(400, 'No charge subscribed');
        }

        $recurringCharge = Arr::first($recurringCharge->currentAppInstallation->activeSubscriptions, function ($activeSubscription) use ($admin_graphql_charge_id) {
            return $activeSubscription->id == $admin_graphql_charge_id;
        });

        if (!$recurringCharge || count($recurringCharge->lineItems) <= 0) {
            abort(400, 'No charge subscribed');
        }

        $recurringPricing = $recurringCharge->lineItems[0]->plan->pricingDetails;
        $subscriptionStatus = Str::lower($recurringCharge->status);

        if ($subscriptionStatus != SubscriptionStatus::ACTIVE) {
            if ($abort_on_failed) {
                abort(400, 'Subscription is not active');
            }
        }

        $subscription = $this->updateSubscription($recurringCharge, $merchant);

        return json_resp($subscription);
    }

    public function subscriptionUpdated(Request $request)
    {
        Log::info('Subscription Updated webhook', $request->all());

        $admin_graphql_id = $request->app_subscription['admin_graphql_api_id'];

        $charge_id = extract_shopify_integer_id($admin_graphql_id);

        HandleSyncShopifySubscription::dispatch($charge_id, auth()->user());

        return json_resp([], 'Success');
    }

    public function updateSubscription($recurringCharge, $merchant)
    {

        $charge_id = extract_shopify_integer_id($recurringCharge->id);
        $subscription = $merchant->subscriptions()->where('charge_id', $charge_id)->first();
        $recurringPricing = $recurringCharge->lineItems[0]->plan->pricingDetails;
        $subscriptionStatus = Str::lower($recurringCharge->status);
        if (!$subscription) {
            $subscription = $merchant->subscriptions()->create([
                'charge_id' => $charge_id,
                'type' => Str::lower($recurringCharge->name),
                'test' => $recurringCharge->test
            ]);
        }
        $subscription->fill([
            'status' => $subscriptionStatus,
            'bill_ends_at' => $recurringCharge->currentPeriodEnd ? Carbon::parse($recurringCharge->currentPeriodEnd) : $subscription->bill_ends_at,
            'canceled_at' => $subscriptionStatus == SubscriptionStatus::CANCELLED ? Carbon::now() : $subscription->canceled_at
        ])->save();

        // deactivate any other subscription
        $subscriptions = $merchant->subscriptions()->where([['id', '!=', $subscription->id], ['status', '!=', SubscriptionStatus::CANCELLED]])->get();
        if ($subscriptions->count() > 0) {
            foreach ($subscriptions as $sub) {
                $sub->fill([
                    'status' => SubscriptionStatus::CANCELLED,
                    'canceled_at' => Carbon::now()
                ])->save();
            }
        }

        $merchantSettings = $merchant->setting();
        $merchantSettings['plan'] = $subscription->status == SubscriptionStatus::ACTIVE ? $subscription->type : PlanEnum::FREE;
        $merchantSettings['billing_period'] = $recurringPricing->interval == 'ANNUAL' ? 'year' : 'month';
        $merchant->saveSetting($merchantSettings);

        return $subscription;
    }
}
