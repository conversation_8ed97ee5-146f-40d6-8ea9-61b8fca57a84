<?php

namespace App\Http\Controllers;

use App\Enums\BatchItemStatus;
use App\Enums\ExportEnum;
use App\Enums\ProductStatus;
use App\Helper\ExportHelper;
use App\Models\Merchant;
use App\Models\Location;
use App\Models\BatchItem;

use App\Http\Helpers\ShopifyAPIHelper;
use App\Http\Requests\ProductIndexRequest;
use App\Http\Requests\ProductExportRequest;
use App\Jobs\HandleSyncShopifyProduct;
use App\Services\ProductService;
use GuzzleHttp\Psr7\Header;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class ProductController extends Controller
{
    protected ProductService $productService;

    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    public function index(ProductIndexRequest $request)
    {
        $input = $request->validated();

        $merchant = auth()->user();

        $input['merchant_id'] = $merchant->id;

        return json_resp($this->productService->getAllPaginatedProducts($input));
    }

    public function getParentProductsWithBatches()
    {
        $merchant = auth()->user();
        $filters = [
            'merchant_id' => $merchant->id
        ];
        $parent_products_with_batch = $this->productService->getAllParentProductsWithBatches($filters);
        return json_resp([
            'data' => $parent_products_with_batch,
            'total' => $parent_products_with_batch->count(),
        ]);
    }

    public function show(Request $request, $product_id)
    {
        $merchant = auth()->user();

        $product = $merchant->products()->withTrashed(false)->findOrFail($product_id);

        $location = Location::findOrFail($request->location);

        $batch_query = $product->batch_items()->with(['batch', 'dates'])
            ->whereHas('batch', function ($query) use ($location) {
                $query->where('inventory_batches.location_id', $location->id);
            });

        $product->batch_items = (clone $batch_query)->when($request->filter, function ($q) use ($request) {
            switch ($request->filter) {
                case BatchItemStatus::IN_STOCK:
                    $q->inStock();
                    break;

                case BatchItemStatus::OUT_OF_STOCK:
                    $q->outOfStock();
                    break;

                case BatchItemStatus::DISCOUNTED:
                    $q->discounted();
                    break;

                case BatchItemStatus::EXPIRED:
                    $q->expired();
                    break;

                case 'past_best_before':
                    $q->whereHas('dates', function ($q) {
                        $q->whereDate('date', '<=', today());
                    });
                    break;

                case 'upcoming_best_before':
                    $q->whereHas('dates', function ($q) {
                        $q->whereDate('date', '>', today());
                    });
                    break;
            }
        })->get();

        $product->quantity_at_location = 0;
        $product->stocked_quantity = 0;
        $product->is_item_stocked = false;

        if (!$product->trashed()) {
            $tracked_location = $product->locations()->firstWhere('locations.id', $location->id);
            $product->quantity_at_location = $tracked_location ? $tracked_location->pivot->on_hand : 0;
            $product->is_item_stocked = !is_null($tracked_location);
            $product->stocked_quantity = $batch_query->expiringSoon()->get()->sum('quantity');
        }

        return json_resp($product);
    }

    public function getPreviewUrl($product_id)
    {
        $merchant = auth()->user();

        $product = $merchant->products()->findOrFail($product_id);

        $store_preview_url = null;
        $store_product_url = null;

        if (!$product->trashed()) {
            $shopify_api_helper = ShopifyAPIHelper::withMerchant($merchant);

            $product_storeurls = $shopify_api_helper->getProductStoreURL($product->parent_id);

            $store_preview_url = $product_storeurls->onlineStorePreviewUrl;
            $store_product_url = $product_storeurls->onlineStoreUrl;
        }

        return json_resp([
            'preview_url' => $store_preview_url,
            'store_url' => $store_product_url
        ]);
    }

    public function getVariants($parent_id)
    {
        $merchant = auth()->user();
        $products = $merchant->products()->with('batch_items.dates')->withTrashed(false)->where('parent_id', $parent_id)->get();
        return json_resp($products);
    }

    public function getRelatedVariants($variant_id)
    {
        $merchant = auth()->user();
        $variant = $merchant->products()->withTrashed(false)->where('shopify_variant_id', $variant_id)->first();
        $related_variants = $merchant->products()->with('batch_items.dates')->withTrashed(false)->where('parent_id', $variant->parent_id)->get();
        return json_resp($related_variants);
    }

    public function export(ProductExportRequest $request)
    {
        $merchant = auth()->user();
        $options = $request->validated();

        $exporter = new ExportHelper(ExportEnum::PRODUCT, "downloads", $options);
        try {
            $result = $exporter->export($merchant);
            if ($result) {
                return json_resp([], 'Success');
            } else {
                return json_resp([], 'Failed to export products.', 400);
            }
        } catch (\Exception $e) {
            return json_resp([], 'Failed to export products.', 400);
        }
    }

    public function productCreated(Request $request)
    {
        Log::info('Products Created webhook', $request->only([
            'id',
            'title',
        ]));

        $merchant = auth()->user();
        HandleSyncShopifyProduct::dispatch($request->id, $merchant);

        return json_resp([], 'Success');
    }

    public function productUpdated(Request $request)
    {
        Log::info('Products Updated webhook', $request->only([
            'id',
            'title',
        ]));

        $merchant = auth()->user();
        HandleSyncShopifyProduct::dispatch($request->id, $merchant);

        return json_resp([], 'Success');
    }

    public function productDeleted(Request $request)
    {
        Log::info('Products Deleted webhook', [
            'id' => $request->id
        ]);

        $merchant = auth()->user();

        $product = $merchant->products()->firstWhere('parent_id', $request->id);
        if ($product) {
            $product->status = ProductStatus::DELETED;
            $product->save();
            $product->delete();
        }

        return json_resp([], 'Success');
    }
}
