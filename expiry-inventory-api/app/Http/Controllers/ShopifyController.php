<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Merchant;
use App\Http\Controllers\Controller;

use App\Http\Helpers\APIHelper;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Jobs\InitMerchant;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ShopifyController extends Controller
{
    public function install(Request $request) {
        $hmac = $request->hmac;
        $shop_url = $request->shop;

        $this->verifyShopURL($shop_url);
        $this->verifySignature($hmac, $request->except('hmac'));

        $query = $this->generateScopeGrantPayload();

        return json_resp('https://' . $shop_url . '/admin/oauth/authorize?' . $query);
    }

    public function callback(Request $request) {
        $hmac = $request->hmac;
        $shop_url = $request->shop;
        $host = $request->host;

        $this->verifyShopURL($shop_url);
        $this->verifySignature($hmac, $request->except('hmac'));

        $access_token = $this->requestAccessToken($shop_url, $request->code);
        $shop_details = ShopifyAPIHelper::withAuth($shop_url, $access_token)->getShopDetails($shop_url, $access_token);

        $user = User::firstOrCreate(
            ['email' => $shop_details->email],
            ['name' => $shop_details->name]
        );

        // withTrashed in case merchant reinstall app
        $merchant = Merchant::withTrashed()->firstOrNew(['subdomain' => $shop_url]);
        $installed = ($merchant->exists && !$merchant->trashed());
        $this->save([
            'user_id'        => $user->id,
            'subdomain'      => $shop_url,
            'access_token'   => $access_token,
            'uninstalled_at' => null
        ], $merchant);

        if(!$installed) {
            $merchant->initSetting();
            $merchantSettings = $merchant->setting();
            $merchantSettings['shop'] = $shop_details;
            $merchant->saveSetting($merchantSettings);
    
            InitMerchant::dispatch($merchant);
        }

        $embeddedUrl = $this->getEmbeddedUrl($shop_url, $host);

        return json_resp($embeddedUrl);
    }

    public function checkAccessScopes(Request $request)
    {
        $request->validate([
            'access_scopes' => 'required|array'
        ]);

        $merchant = auth()->user();

        $granted_access_scopes = (new ShopifyAPIHelper($merchant->subdomain, $merchant->access_token))->getAccessScopes();
        
        $app_access_scopes = $request->access_scopes;

        $notFound = false;
        foreach($app_access_scopes as $current_scope) {
            $found = array_filter($granted_access_scopes, function($granted_scope) use($current_scope) {
                return $current_scope == $granted_scope->handle;
            });
            if(!$found) {
                $notFound = true;
                break;
            }
        }
        
        return json_resp(['require_oauth' => $notFound]);
    }

    public function login(Request $request)
    {
        if(!isset($request->shop)) {
            abort(404, 'Shop URL is not valid');
        }

        $merchant = Merchant::withTrashed()->firstOrNew(['subdomain' => $request->shop]);
        $query = $this->generateScopeGrantPayload();
        if($merchant && !$merchant->trashed()) {
            return json_resp('https://' . $merchant->subdomain . '/admin/apps/' . config('services.shopify.key'));
        }

        return json_resp('https://' . $request->shop . '/admin/oauth/authorize?' . $query);
    }

    private function verifyShopURL($shop_url) {
        if(!preg_match('/[a-zA-Z0-9][a-zA-Z0-9\-]*\.myshopify\.com/', $shop_url)) {
            abort(404, 'Shop URL is not valid');
        }
    }

    private function getEmbeddedUrl($shop, $host) {
        $decodedHost = base64_decode($host, true);
        return "https://$decodedHost/apps/" . config('services.shopify.key') . '/dashboard' . '?shop=' . $shop . '&host=' . $host;
    }

    private function verifySignature($hmac, $query_params) {
        $shopify_api_secret = config('services.shopify.secret');

        $hashing_payload = $this->extractQueryParams($query_params);

        $generated_hmac = hash_hmac('sha256', $hashing_payload, $shopify_api_secret);

        if(!hash_equals($hmac, $generated_hmac)) {
            abort(404, 'Signature is not valid');
        }
    }

    private function generateScopeGrantPayload() {
        $redirectUrl = config('services.shopify.app_url') . '/shopify/auth/callback';
        $nonce = Str::random(8);
        $query = http_build_query([
            'client_id'         => config('services.shopify.key'),
            'scope'             => implode(',', config('services.shopify.access_scopes')),
            'redirect_uri'      => $redirectUrl,
            'state'             => $nonce,
            'grant_options[]'   => ''
        ]);
        return $query;
    }

    private function requestAccessToken($shop_url, $auth_code) {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json'
            ],
            'json' => [
                'client_id' => config('services.shopify.key'),
                'client_secret' => config('services.shopify.secret'),
                'code' => $auth_code
            ]
        ];
        $response = APIHelper::request('POST', 'https://' . $shop_url . '/admin/oauth/access_token', $requestContent);
        return $response->access_token;
    }

    private function extractQueryParams($query_payload) {
        ksort($query_payload);
        return implode('&', array_map(
            function ($v, $k) { return sprintf("%s=%s", $k, $v); },
            $query_payload,
            array_keys($query_payload)
        ));
    }
}
