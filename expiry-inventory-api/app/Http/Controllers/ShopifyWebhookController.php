<?php

namespace App\Http\Controllers;

use App\Models\Merchant;
use App\Traits\HasShopifyAuth;

use App\Http\Helpers\ShopifyAPIHelper;

use Illuminate\Support\Str;
use Illuminate\Http\Request;

class ShopifyWebhookController extends Controller
{
    use HasShopifyAuth;

    protected static $shopify_api_helper;

    private static $ORDER_FILTER_FIELDS = [
        'id', 'order_number', 'current_total_price', 'billing_address', 'shipping_address', 'note', 'financial_status',
        'fulfillment_status', 'customer', 'line_items', 'fulfillments', 'refunds'
    ];
    private static $PRODUCT_FILTER_FIELDS = [
        'id', 'title', 'status', 'variants', 'featuredImage'
    ];

    public function uninstall()
    {
        $merchant = auth()->user();

        if(!$merchant->trashed()) {
            // app uninstalled
            $merchant->delete();
        }

        return json_resp([], 'Success');
    }

    public function update(Request $request)
    {
        $merchant = auth()->user();

        $merchantSettings = $merchant->setting();
        $merchantSettings['shop'] = $request->all();
        $merchant->saveSetting($merchantSettings);

        return json_resp([], 'Success');
    }

    public function shopRedact(Request $request) {
        $this->validateGDPRWebhookRequest(__FUNCTION__, $request);

        auth()->user()->forceDelete();

        return json_resp([], 'Success');
    }

    public function customersRequest(Request $request)
    {
        $this->validateGDPRWebhookRequest(__FUNCTION__, $request);
        
        $merchant = auth()->user();
        $merchant->data_requests()->create([
            'payload' => $request->all()
        ]);

        return json_resp([], 'Success');
    }

    public function customersRedact(Request $request)
    {
        $this->validateGDPRWebhookRequest(__FUNCTION__, $request);
        
        $merchant = auth()->user();
        if(isset($request->customer) && isset($request->customer->id)) {
            $customer = $merchant->customers()->firstWhere('shopify_customer_id', $request->customer->id);
            if($customer) {
                $customer->forceDelete();
            }
        }
        if(isset($request->orders_to_redact)) {
            $orders = $merchant->orders()->whereIn('shopify_order_id', $request->orders_to_redact)->get();
            foreach($orders as $order) {
                $order->items()->delete();
                $order->forceDelete();
            }
        }

        return json_resp([], 'Success');
    }

    public static function setupWebhooks(Merchant $merchant) {
        self::$shopify_api_helper = ShopifyAPIHelper::withMerchant($merchant);

        $webhooks = self::getExistingWebhooks();
        $webhooks = collect($webhooks);

        self::syncWebhook($webhooks->firstWhere('topic', 'app/uninstalled'), 'app/uninstalled', '/shopify/uninstall');
        self::syncWebhook($webhooks->firstWhere('topic', 'shop/update'), 'shop/update', '/shopify/update');
        self::syncWebhook($webhooks->firstWhere('topic', 'products/create'), 'products/create', '/shopify/product/created', self::$PRODUCT_FILTER_FIELDS);
        self::syncWebhook($webhooks->firstWhere('topic', 'products/update'), 'products/update', '/shopify/product/updated', self::$PRODUCT_FILTER_FIELDS);
        self::syncWebhook($webhooks->firstWhere('topic', 'products/delete'), 'products/delete', '/shopify/product/deleted');
        self::syncWebhook($webhooks->firstWhere('topic', 'orders/create'), 'orders/create', '/shopify/order/created', self::$ORDER_FILTER_FIELDS);
        self::syncWebhook($webhooks->firstWhere('topic', 'orders/updated'), 'orders/updated', '/shopify/order/updated', self::$ORDER_FILTER_FIELDS);
        self::syncWebhook($webhooks->firstWhere('topic', 'orders/cancelled'), 'orders/cancelled', '/shopify/order/cancelled', self::$ORDER_FILTER_FIELDS);
        self::syncWebhook($webhooks->firstWhere('topic', 'orders/delete'), 'orders/delete', '/shopify/order/deleted', self::$ORDER_FILTER_FIELDS);
        self::syncWebhook($webhooks->firstWhere('topic', 'locations/create'), 'locations/create', '/shopify/location/created');
        self::syncWebhook($webhooks->firstWhere('topic', 'locations/update'), 'locations/update', '/shopify/location/updated');
        self::syncWebhook($webhooks->firstWhere('topic', 'locations/delete'), 'locations/delete', '/shopify/location/deleted');
        self::syncWebhook($webhooks->firstWhere('topic', 'app_subscriptions/update'), 'app_subscriptions/update', '/shopify/subscription/updated');
        // self::syncWebhook($webhooks->firstWhere('topic', 'customers/update'), 'customers/update', '/shopify/customer/updated');
    }

    private function validateGDPRWebhookRequest($webhook_type, Request $request) {
        $validation_array = [];

        switch($webhook_type) {
            case 'shopRedact':
                $validation_array = [
                    'shop_id'          => 'required',
                    'shop_domain'      => 'required'
                ];
                break;

            case 'customersRequest':
                $validation_array = [
                    'shop_id'          => 'required',
                    'shop_domain'      => 'required',
                    'orders_requested' => 'sometimes|array',
                    'customer.id'      => 'sometimes|numeric',
                    'customer.email'   => 'required|email',
                    'customer.phone'   => 'sometimes',
                    'data_request'     => 'sometimes|array'
                ];
                break;

            case 'customersRedact':
                $validation_array = [
                    'shop_id'          => 'required',
                    'shop_domain'      => 'required',
                    'customer.id'      => 'sometimes|numeric',
                    'customer.email'   => 'required|email',
                    'customer.phone'   => 'sometimes',
                    'orders_to_redact' => 'sometimes|array'
                ];
                break;

            default:
                abort(401, 'Unauthorized.');
        }

        $request->validate($validation_array);

        $merchant = auth()->user();

        if($request->shop_domain != $merchant->subdomain) {
            abort(401, 'Unauthorized.');
        }
    }

    /**
     *
     * Create a Shopify webhook
     *
     * @param object|null $existing_webhook An existing webhook created in shop, null if not existed
     * @param string $topic Shopify defined webhook topic/subject in their Docs
     * @param string $api_relative_path API relative path for webhook event to revoke (eg. /shopify/product)
     * @return void
     *
     */
    private static function syncWebhook($existing_webhook, $topic, $api_relative_path, $filter_fields = null) {
        $webhook_name = ucwords(Str::replace('/', ' ', $topic));
        $added_slash_path = Str::start($api_relative_path, '/');
        $webhook_handler_address = config('app.url') . '/api' . $added_slash_path;

        if(!$existing_webhook) {
            self::$shopify_api_helper->createWebhook($topic, $webhook_handler_address, $filter_fields, 'Failed to create ' . $webhook_name . ' webhook');
        }
        else {
            // skip update if api version and webhook handler address are same
            if($existing_webhook->api_version == config('services.shopify.api_version')
                && $existing_webhook->address == $webhook_handler_address
                && self::hasSameFilterFields($existing_webhook->fields, $filter_fields)) {
                return;
            }

            self::$shopify_api_helper->updateWebhook($existing_webhook->id, $webhook_handler_address, $filter_fields, 'Failed to update ' . $webhook_name . ' webhook');
        }
    }

    private static function getExistingWebhooks() {
        return self::$shopify_api_helper->getWebhooks();
    }

    private static function hasSameFilterFields($existing, $current) {
        if(!$current) $current = [];
        return array_diff($existing, $current) == array_diff($current, $existing);
    }
}
