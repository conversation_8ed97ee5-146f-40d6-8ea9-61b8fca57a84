<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ShopifyAPIHelper;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class ThemeController extends Controller
{
    public $APP_BLOCK_TEMPLATES = ['product'];

    public function index()
    {
        $merchant = auth()->user();
        $shopify_api_helper = new ShopifyAPIHelper($merchant->subdomain, $merchant->access_token);
        $shopify_themes = $shopify_api_helper->getThemes();

        $themes = array_map(function($theme) {
            return [
                'id' => $theme->id,
                'name' => $theme->name,
                'is_published' => $theme->role === 'main'
            ];
        }, $shopify_themes);

        return json_resp($themes);
    }

    public function show($theme_id)
    {
        $merchant = auth()->user();
        $shopify_api_helper = new ShopifyAPIHelper($merchant->subdomain, $merchant->access_token);
        $theme = $shopify_api_helper->getTheme($theme_id);

        $assets = $shopify_api_helper->getThemeAssets($theme->id);

        $template_json_files = array_filter($assets, function($file) {
            return null !== Arr::first($this->APP_BLOCK_TEMPLATES, fn($template) => $file->key == "templates/{$template}.json");
        });

        $is_theme_supported = false;
        foreach($template_json_files as $template_json_file)
        {
            $asset = $shopify_api_helper->getSingleAsset($theme->id, $template_json_file->key);
            $json_content_arr = json_decode($asset->value, true);
            $main = Arr::first($json_content_arr['sections'], fn($section, $id) => $id === 'main' || Str::startsWith('main-', $section['type']));
            if($main) {
                $section = Arr::first($assets, fn($asset) => $asset->key === "sections/{$main['type']}.liquid");
                if($section) {
                    $main_section = $shopify_api_helper->getSingleAsset($theme->id, $section->key);
                    preg_match("/\{\%\s+schema\s+\%\}([\s\S]*?)\{\%\s+endschema\s+\%\}/m", $main_section->value, $matches);
                    $schema = json_decode($matches[1], true);
                    if($schema && $schema['blocks']) {
                        $is_theme_supported = null !== Arr::first($schema['blocks'], fn($block) => $block['type'] === '@app');
                    }
                }
            }
        }

        return json_resp([
            'id' => $theme->id,
            'name' => $theme->name,
            'is_supported' => $is_theme_supported,
            'is_published' => $theme->role === 'main'
        ]);
    }
}