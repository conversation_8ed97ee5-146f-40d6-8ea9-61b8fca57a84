<?php

namespace App\Http\Controllers;

use App\Models\Transfer;
use App\Models\Product;
use App\Models\InventoryBatch;
use App\Models\BatchItem;
use App\Models\InventoryAction;
use App\Models\InventoryHistory;
use App\Models\Location;
use App\Enums\ShopifyGraphQLEnum;
use App\Traits\HasShopifyAuth;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Jobs\HandleSyncShopifyProduct;
use Illuminate\Http\Request;

class TransferController extends Controller
{
    use HasShopifyAuth;

    protected static $shop;
    protected static $access_token;

    /**
     * Get all transfers for the authenticated merchant
     */
    public function index(Request $request)
    {
        $merchant = auth()->user();
        
        $transfers = Transfer::where('merchant_id', $merchant->id)
            ->with(['sourceBatch.location', 'destinationBatch.location', 'product']);

        // Filter by draft status if specified
        if ($request->has('is_draft')) {
            $transfers->where('is_draft', $request->boolean('is_draft'));
        }

        // Filter by status if specified
        if ($request->has('status')) {
            $transfers->where('status', $request->status);
        }

        $transfers = $transfers->orderBy('created_at', 'desc')->paginate(15);

        return json_resp($transfers);
    }

    /**
     * Create a new transfer
     */
    public function store(Request $request)
    {
        $merchant = auth()->user();

        self::$shop = $merchant->subdomain;
        self::$access_token = $merchant->access_token;

        $request->validate([
            'source_batch_id' => 'required|exists:inventory_batches,id',
            'destination_batch_id' => 'required|exists:inventory_batches,id',
            'quantity' => 'required|numeric|min:1',
            'product_id' => 'required|exists:products,id',
            'is_draft' => 'boolean',
            'source_quantity_adjustment' => 'boolean',
            'destination_quantity_adjustment' => 'boolean',
            'notes' => 'string|nullable',
        ]);

        $source_batch = InventoryBatch::find($request->source_batch_id);
        $destination_batch = InventoryBatch::find($request->destination_batch_id);
        $product = Product::find($request->product_id);

        $source_batch_item = $source_batch->items()->where('product_id', $product->id)->first();
        $destination_batch_item = $destination_batch->items()->where('product_id', $product->id)->first();

        if (!$source_batch_item) {
            return json_resp([], 'Source batch does not have the product.', 400);
        }

        if (!$destination_batch_item) {
            return json_resp([], 'Destination batch does not have the product.', 400);
        }

        if ($source_batch_item->quantity < $request->quantity) {
            return json_resp([], 'Source batch does not have enough quantity.', 400);
        }

        $is_draft = $request->input('is_draft', false);

        // Create transfer record
        $transfer = Transfer::create([
            'merchant_id' => $merchant->id,
            'source_batch_id' => $request->source_batch_id,
            'destination_batch_id' => $request->destination_batch_id,
            'product_id' => $request->product_id,
            'quantity' => $request->quantity,
            'source_quantity_adjustment' => $request->input('source_quantity_adjustment', true),
            'destination_quantity_adjustment' => $request->input('destination_quantity_adjustment', true),
            'is_draft' => $is_draft,
            'status' => $is_draft ? Transfer::STATUS_PENDING : Transfer::STATUS_COMPLETED,
            'executed_at' => $is_draft ? null : now(),
            'notes' => $request->input('notes'),
        ]);

        // If it's a draft, just save the transfer record and return
        if ($is_draft) {
            return json_resp($transfer, 'Transfer saved as draft successfully');
        }

        // Execute the actual transfer
        $source_batch_item->quantity -= $request->quantity;
        $destination_batch_item->quantity += $request->quantity;

        $source_batch_item->save();
        $destination_batch_item->save();

        $this->createInventoryHistoryEntry($source_batch_item, -$request->quantity);
        $this->createInventoryHistoryEntry($destination_batch_item, $request->quantity);

        if ($request->input('source_quantity_adjustment', true)) {
            $this->syncShopifyInventory($product, $source_batch->location, -$request->quantity);
        }

        if ($request->input('destination_quantity_adjustment', true)) {
            $this->syncShopifyInventory($product, $destination_batch->location, $request->quantity);
        }

        HandleSyncShopifyProduct::dispatch($product->parent_id, $merchant);

        return json_resp($transfer, 'Transfer completed successfully');
    }

    /**
     * Get a specific transfer
     */
    public function show(Transfer $transfer)
    {
        $merchant = auth()->user();
        
        if ($transfer->merchant_id !== $merchant->id) {
            return json_resp([], 'Transfer not found', 404);
        }

        return json_resp($transfer);
    }

    /**
     * Update a transfer (only drafts can be updated)
     */
    public function update(Request $request, Transfer $transfer)
    {
        $merchant = auth()->user();
        
        if ($transfer->merchant_id !== $merchant->id) {
            return json_resp([], 'Transfer not found', 404);
        }

        if (!$transfer->isDraft()) {
            return json_resp([], 'Only draft transfers can be updated', 400);
        }

        $request->validate([
            'source_batch_id' => 'sometimes|exists:inventory_batches,id',
            'destination_batch_id' => 'sometimes|exists:inventory_batches,id',
            'quantity' => 'sometimes|numeric|min:1',
            'product_id' => 'sometimes|exists:products,id',
            'source_quantity_adjustment' => 'sometimes|boolean',
            'destination_quantity_adjustment' => 'sometimes|boolean',
            'notes' => 'sometimes|string|nullable',
        ]);

        $transfer->update($request->only([
            'source_batch_id',
            'destination_batch_id',
            'quantity',
            'product_id',
            'source_quantity_adjustment',
            'destination_quantity_adjustment',
            'notes',
        ]));

        return json_resp($transfer, 'Transfer updated successfully');
    }

    /**
     * Execute a draft transfer
     */
    public function execute(Transfer $transfer)
    {
        $merchant = auth()->user();
        
        if ($transfer->merchant_id !== $merchant->id) {
            return json_resp([], 'Transfer not found', 404);
        }

        if (!$transfer->isDraft()) {
            return json_resp([], 'Transfer has already been executed', 400);
        }

        self::$shop = $merchant->subdomain;
        self::$access_token = $merchant->access_token;

        $source_batch = InventoryBatch::find($transfer->source_batch_id);
        $destination_batch = InventoryBatch::find($transfer->destination_batch_id);
        $product = Product::find($transfer->product_id);

        $source_batch_item = $source_batch->items()->where('product_id', $product->id)->first();
        $destination_batch_item = $destination_batch->items()->where('product_id', $product->id)->first();

        if (!$source_batch_item) {
            return json_resp([], 'Source batch does not have the product.', 400);
        }

        if (!$destination_batch_item) {
            return json_resp([], 'Destination batch does not have the product.', 400);
        }

        if ($source_batch_item->quantity < $transfer->quantity) {
            return json_resp([], 'Source batch does not have enough quantity.', 400);
        }

        // Execute the transfer
        $source_batch_item->quantity -= $transfer->quantity;
        $destination_batch_item->quantity += $transfer->quantity;

        $source_batch_item->save();
        $destination_batch_item->save();

        $this->createInventoryHistoryEntry($source_batch_item, -$transfer->quantity);
        $this->createInventoryHistoryEntry($destination_batch_item, $transfer->quantity);
        
        if ($transfer->source_quantity_adjustment) {
            $this->syncShopifyInventory($product, $source_batch->location, -$transfer->quantity);
        }

        if ($transfer->destination_quantity_adjustment) {
            $this->syncShopifyInventory($product, $destination_batch->location, $transfer->quantity);
        }

        // Mark transfer as completed
        $transfer->markAsCompleted();

        HandleSyncShopifyProduct::dispatch($product->parent_id, $merchant);

        return json_resp($transfer, 'Transfer executed successfully');
    }

    /**
     * Delete a transfer
     */
    public function destroy(Transfer $transfer)
    {
        $merchant = auth()->user();
        
        if ($transfer->merchant_id !== $merchant->id) {
            return json_resp([], 'Transfer not found', 404);
        }

        if ($transfer->isCompleted()) {
            return json_resp([], 'Completed transfers cannot be deleted', 400);
        }

        $transfer->delete();

        return json_resp([], 'Transfer deleted successfully');
    }

    /**
     * Create inventory history entry
     */
    private function createInventoryHistoryEntry($batch_item, $adjustment, $action_slug = 'batch-transfered')
    {
        $merchant = auth()->user();
        $action = InventoryAction::where('slug', $action_slug)->first();

        if (!$action) {
            $action = InventoryAction::create([
                'name' => ucfirst($action_slug),
                'slug' => $action_slug,
                'trigger_by' => 'manual'
            ]);
        }

        InventoryHistory::create([
            'merchant_id' => $merchant->id,
            'action_id' => $action->id,
            'batch_id' => $batch_item->inventory_batch_id,
            'adjustment' => $adjustment,
            'result_quantity' => $batch_item->quantity,
            'location_quantity' => $batch_item->quantity,
        ]);
    }

    /**
     * Sync inventory with Shopify
     */
    private function syncShopifyInventory(Product $product, Location $location, $adjustment_quantity)
    {
        if ($adjustment_quantity != 0) {
            if ($product->locations()->whereKey($location->id)->exists()) {
                $shopify_helper = new ShopifyAPIHelper(self::$shop, self::$access_token);
                $admin_graphql_inventory_item_id = $shopify_helper->getProductVariantInventoryItemID($product->shopify_variant_id);
                // adjust quantity for shopify inventory item
                $shopify_helper->adjustInventoryLevel($admin_graphql_inventory_item_id, $location->shopify_location_id, $adjustment_quantity, ShopifyGraphQLEnum::INVENTORY_ON_HAND_CORRECTION);
            }
        }
    }
}
