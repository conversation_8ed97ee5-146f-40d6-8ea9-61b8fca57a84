<?php

namespace App\Http\Helpers;

use App\Http\Stores\ShopifyRateLimiterStore;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\HandlerStack;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\GuzzleRateLimiterMiddleware\RateLimiterMiddleware;

class APIHelper
{
    public static function request($method, $url, $headers = ['headers' => ['Content-Type' => 'application/json']], $error_msg = null, $is_get_response_headers = false, $abort_on_fail = true)
    {
        try {
            $stack = HandlerStack::create();
            $stack->push(RateLimiterMiddleware::perSecond(2, new ShopifyRateLimiterStore));
            $client = new Client([
                'handler' => $stack,
            ]);
            $response = $client->request($method, $url, $headers);
            $responseData = json_decode($response->getBody());
            if ($is_get_response_headers) {
                $responseData->response_headers = $response->getHeaders();
            }

            if (isset($responseData->errors)) {
                Log::info('Request Error', [
                    'error' => $responseData->errors,
                    'method' => $method,
                    'url' => $url
                ]);
            }

            return $responseData;
        } catch (ClientException $e) {
            // Catch all 400 level errors.
            $StatusCode = $e->getResponse()->getStatusCode();
            if ($StatusCode >= 400) {
                $response_error = $e->getMessage();
                Log::info('Request Error', [
                    'error' => $response_error,
                    'method' => $method,
                    'url' => $url
                ]);
                $error = $error_msg ?: $response_error;
                // abort(400, $error);
            }
            if ($abort_on_fail) {
                abort(400, $error ?? 'Unknown error occurred.');
            }
        }
    }
}
