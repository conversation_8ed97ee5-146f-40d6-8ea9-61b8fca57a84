<?php

namespace App\Http\Helpers;

use App\Models\Product;
use App\Models\Discount;
use App\Models\Merchant;
use App\Enums\ProductStatus;
use App\Enums\BatchItemStatus;
use App\Enums\DiscountType;
use App\Enums\PlanEnum;
use App\Models\BatchItem;
use App\Services\ShopifyService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class DiscountHelper
{
    public static function applyDiscountForProducts(Collection $products)
    {
        $first_product = $products->first();
        if ($first_product) {
            $merchant = $first_product->merchant()->withTrashed()->first();

            // get uncached value
            $merchant_setting = $merchant->setting('configs', false);

            $products->each(function (Product $product) use ($merchant_setting, $merchant) {

                // check is discount enabled
                if (!$product->trashed() && $merchant_setting['plan'] != PlanEnum::FREE && ($merchant_setting['is_apply_discount'] || $merchant_setting['is_apply_slow_moving'])) {
                    $discount_payload = null;

                    if ($merchant_setting['is_apply_discount']) {
                        foreach ($merchant_setting['batch_discounts'] as $batch_discount) {
                            $discount_type = Arr::get($batch_discount, 'type', DiscountType::EXPIRY);
                            if ($discount_type == DiscountType::BEST_BEFORE) {
                                $batch_item = $product->batch_items()->expiringSoon()->filterBestBeforeDate('to', $batch_discount['days_before_expire'])->first();
                                $best_before = $batch_item ? $batch_item->date : null;
                                if ($batch_item && $best_before) {
                                    $discount_payload = [
                                        'batch_item' => BatchItem::find($batch_item->id),
                                        'type' => $discount_type,
                                        'discount_rate' => $batch_discount['discount_rate'],
                                        'discount_period' => null,
                                        'track_period' => null,
                                        'track_unit' => null
                                    ];
                                    break;
                                }
                            } else {
                                $batch_item = $product->batch_items()->expiringSoon()->filterExpireDate('to', $batch_discount['days_before_expire'])->first();
                                if ($batch_item && $batch_item->expire_at) {
                                    $discount_payload = [
                                        'batch_item' => $batch_item,
                                        'type' => $discount_type,
                                        'discount_rate' => $batch_discount['discount_rate'],
                                        'discount_period' => null,
                                        'track_period' => null,
                                        'track_unit' => null
                                    ];
                                    break;
                                }
                            }
                        }
                    }

                    if ($merchant_setting['is_apply_slow_moving'] && !$discount_payload) {
                        foreach ($merchant_setting['slow_moving_discounts'] as $slow_moving_discount) {
                            $batch_item = $product->batch_items()->expiringSoon()
                                ->isSlowMoving($slow_moving_discount['track_period'], $slow_moving_discount['unit'], $slow_moving_discount['sales_qty'])->first();
                            if ($batch_item) {
                                $discount_payload = [
                                    'batch_item' => $batch_item,
                                    'type' => DiscountType::SLOW_MOVING,
                                    'discount_rate' => $slow_moving_discount['discount_rate'],
                                    'discount_period' => $slow_moving_discount['discount_period'],
                                    'track_period' => $slow_moving_discount['track_period'],
                                    'track_unit' => $slow_moving_discount['unit']
                                ];
                                break;
                            }

                            // if there is any discount running, check discount_end_at and create payload if discount_end_at not over
                            $active_discount = $product->discounts()->withType('slow_moving')->where('discount_period', $slow_moving_discount['discount_period'])->where('status', '!=', 'inactive')->first();
                            if (
                                $active_discount && $active_discount->discount_end_at &&
                                Carbon::now()->lessThanOrEqualTo(new Carbon($active_discount->discount_end_at))
                            ) {
                                $discount_item = $active_discount->batch_item;
                                if ($discount_item->status == BatchItemStatus::IN_STOCK || $discount_item->status == BatchItemStatus::DISCOUNTED) {
                                    $discount_payload = [
                                        'batch_item' => $active_discount->batch_item,
                                        'type' => DiscountType::SLOW_MOVING,
                                        'discount_rate' => $slow_moving_discount['discount_rate'],
                                        'discount_period' => $slow_moving_discount['discount_period'],
                                        'track_period' => $slow_moving_discount['track_period'],
                                        'track_unit' => $slow_moving_discount['unit']
                                    ];
                                }
                                break;
                            }
                        }
                    }

                    if ($discount_payload) {
                        $batch_item_to_discount = $discount_payload['batch_item'];

                        // check if there is any discount created
                        $discount = $batch_item_to_discount->discounts()->withType($discount_payload['type'])->first();

                        $discount_end_at = null;
                        if ($discount_payload['discount_period']) {
                            if ($discount) {
                                $discount_end_at = $discount->discount_end_at ? (
                                    Carbon::now()->lessThanOrEqualTo(new Carbon($discount->discount_end_at))
                                    ? $discount->discount_end_at
                                    : (new Carbon($discount->discount_end_at))->addDays($discount_payload['discount_period'])
                                ) : Carbon::today()->addDays($discount_payload['discount_period']);
                            } else {
                                $discount_end_at = Carbon::today()->addDays($discount_payload['discount_period']);
                            }
                        }

                        if ($discount) {
                            // if discount conditions match, skip this
                            if (
                                $discount->discount_rate == $discount_payload['discount_rate'] &&
                                $discount->discount_period == $discount_payload['discount_period'] &&
                                $discount->discount_end_at == $discount_end_at && $discount->status != 'inactive'
                            ) {
                                return;
                            }
                        }

                        $discount_status = $discount ? $discount->status : 'pending';
                        $discount = $discount ?: new Discount;

                        // calculate discounted price
                        $discounted_price = $product->original_price - ($product->original_price * $discount_payload['discount_rate'] / 100.0);

                        if ($product->price != $discounted_price || $discount_status == 'inactive' || $discount->discount_end_at != $discount_end_at) {
                            $discount->fill([
                                'batch_item_id' => $batch_item_to_discount->id,
                                'product_id' => $batch_item_to_discount->product_id,
                                'type' => $discount_payload['type'],
                                'discount_rate' => $discount_payload['discount_rate'],
                                'discounted_price' => $discounted_price,
                                'discount_period' => $discount_payload['discount_period'],
                                'discount_end_at' => $discount_end_at,
                                'track_period' => $discount_payload['track_period'],
                                'track_unit' => $discount_payload['track_unit'],
                                'status' => 'pending'
                            ])->save();

                            if (isset($merchant_setting['is_auto_update_discount']) && $merchant_setting['is_auto_update_discount']) {
                                $discount->activate();
                            }
                        }
                    } else {
                        if ($product->status == ProductStatus::DISCOUNTED) {
                            // revert product price
                            if (!$merchant->trashed()) {
                                (new ShopifyService)
                                    ->setMerchant($merchant)
                                    ->setProduct($product)
                                    ->updateShopifyProductPrices();
                            }
                            $product->price = $product->original_price;
                            $product->status = ProductStatus::ACTIVE;
                            $product->save();
                        }

                        // no batch to discount, revert all any existing discounted batch items status to in_stock
                        foreach ($product->batch_items as $revert_item) {
                            foreach ($revert_item->discounts()->where('status', '!=', 'inactive')->get() as $revert_discount) {
                                $revert_discount->deactivate();
                            }
                            if ($revert_item->status == BatchItemStatus::DISCOUNTED) {
                                $revert_item->status = BatchItemStatus::IN_STOCK;
                                $revert_item->save();
                            }
                        }
                    }
                } else {
                    // discount disabled or product deleted - if product is discounted, revert price
                    if ($product->status == ProductStatus::DISCOUNTED) {
                        // revert product price
                        if (!$merchant->trashed()) {
                            (new ShopifyService)
                                ->setMerchant($merchant)
                                ->setProduct($product)
                                ->updateShopifyProductPrices();
                        }
                        $product->status = ProductStatus::ACTIVE;
                        $product->price = $product->original_price;
                        $product->save();
                    } else if ($product->trashed()) {
                        $product->price = $product->original_price;
                        $product->save();
                    }

                    // revert all batch items status
                    foreach ($product->batch_items()->statusIn([BatchItemStatus::DISCOUNTED, BatchItemStatus::OUT_OF_STOCK, BatchItemStatus::EXPIRED])->get() as $revert_item) {
                        foreach ($revert_item->discounts()->where('status', '!=', 'inactive')->get() as $revert_discount) {
                            $revert_discount->deactivate();
                        }
                        if ($revert_item->status == BatchItemStatus::DISCOUNTED) {
                            $revert_item->status = BatchItemStatus::IN_STOCK;
                            $revert_item->save();
                            if ($product->trashed()) {
                                $revert_item->batch->checkForLatestStatus();
                            }
                        }
                    }
                }
            });
        }
    }

    public static function revertProductDiscountedPrice(Merchant $merchant, Product $product)
    {
        if ($product && $product->status == ProductStatus::DISCOUNTED) {
            (new ShopifyService)
                ->setMerchant($merchant)
                ->setProduct($product)
                ->updateShopifyProductPrices();

            $product->status = ProductStatus::ACTIVE;
            $product->price = $product->original_price;
            $product->save();
        }

        if ($product->discounts()->exists()) {
            $product->discounts()->delete();
        }
    }
}
