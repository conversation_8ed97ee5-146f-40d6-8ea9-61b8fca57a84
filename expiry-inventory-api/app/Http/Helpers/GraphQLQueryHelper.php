<?php

namespace App\Http\Helpers;

use App\Enums\ShopifyGraphQLEnum;
use App\Models\Product;

class GraphQLQueryHelper
{
    public static function getProductIDsQuery()
    {
        $query = <<<GRAPHQL
        query getProducts(\$first: Int!, \$after: String) {
            products(first: \$first, after: \$after) {
                nodes {
                    id
                }
                pageInfo {
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
        GRAPHQL;
        return $query;
    }

    public static function getProductIDsVariables($cursor = null)
    {
        $product_page_size = ShopifyGraphQLEnum::PRODUCT_PAGE_SIZE;
        $variables = [
            'first' => $product_page_size,
        ];
        if ($cursor) {
            $variables['after'] = "$cursor";
        }
        return $variables;
    }

    public static function getProductQuery()
    {
        $query = <<<GRAPHQL
        query getProducts(\$gid: ID!, \$variant_first: Int!, \$variant_after: String)
        {
            product(id: \$gid) {
                id
                title
                status
                featuredImage {
                    url
                }
                tags
                variantsCount {
                    count
                }
                variants(first: \$variant_first, after: \$variant_after) {
                    nodes {
                        title
                        id
                        sku
                        barcode
                        price
                        compareAtPrice
                        image {
                            url
                        }
                        inventoryItem {
                            tracked
                            inventoryLevels(first: 100) {
                                nodes {
                                    location {
                                        id
                                        legacyResourceId
                                        name
                                    }
                                    quantities(names: "on_hand") {
                                        quantity
                                    }
                                }
                            }
                        }
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                }
            }
        }
        GRAPHQL;
        return $query;
    }

    public static function getProductVariables($id, $variant_cursor = null)
    {
        $gid = generate_shopify_graphql_id($id, ShopifyGraphQLEnum::PRODUCT);
        $variant_page_size = ShopifyGraphQLEnum::PRODUCT_VARIANT_PAGE_SIZE;
        $variables = [
            'gid' => "$gid",
            'variant_first' => $variant_page_size,
        ];
        if ($variant_cursor) {
            $variables['variant_after'] = "$variant_cursor";
        }
        return $variables;
    }

    public static function getProductStoreUrlQuery($product_id)
    {
        $gid = generate_shopify_graphql_id($product_id, ShopifyGraphQLEnum::PRODUCT);
        $query = <<<GRAPHQL
            {
            product(id:"$gid") {    
                onlineStorePreviewUrl
                onlineStoreUrl
            }    
        }
        GRAPHQL;
        return $query;
    }

    public static function getProductVariantQuery($variant_id)
    {
        $gid = generate_shopify_graphql_id($variant_id, ShopifyGraphQLEnum::PRODUCT_VARIANT);
        $query = <<<GRAPHQL
            {
                productVariant(id:"$gid") {
                    title
                    id
                    sku
                    barcode
                    price
                    compareAtPrice
                    image {
                        url
                    }
                    product {
                        id
                        title
                        status
                        featuredImage {
                            url
                        }
                    }
                    inventoryItem {
                        tracked
                        inventoryLevels(first: 100) {
                            nodes {
                                location {
                                    id
                                    legacyResourceId
                                    name
                                }
                                quantities(names: "on_hand") {
                                    quantity
                                }
                            }
                        }
                    }
                }
            }
        GRAPHQL;
        return $query;
    }

    public static function getProductVariantInventoryItemIDQuery($variant_id)
    {
        $gid = generate_shopify_graphql_id($variant_id, ShopifyGraphQLEnum::PRODUCT_VARIANT);
        $query = <<<GRAPHQL
            {
                productVariant(id:"$gid") {
                    inventoryItem {
                        id
                    }
                }
            }
        GRAPHQL;
        return $query;
    }

    public static function getUpdateProductVariantPricesMutationQuery()
    {
        /**
         * 2024-04 productVariantUpdate (deprecated)
         * 2025-01 productVariantsBulkUpdate 
         */
        $query = <<<QUERY
       mutation UpdateProductVariantPrices (\$productId: ID!, \$variants: [ProductVariantsBulkInput!]!) {
        productVariantsBulkUpdate(productId: \$productId, variants: \$variants) {
                userErrors {
                    field
                    message
                }
                productVariants {
                    id
                }
        }
        }
QUERY;
        return $query;
    }

    public static function getUpdateProductVariantPricesMutationVariables(Product $product, $price, $compare_at_price = null)
    {
        /**
         * 2024-04 productVariantUpdate (input: ProductVariantInput!)
         * 2025-01 productVariantsBulkUpdate ($productId: ID!, $variants: [ProductVariantsBulkInput!]!)
         */
        $product_id = $product->parent_id;
        $variant_id = $product->shopify_variant_id;

        $variables = [
            'productId' => generate_shopify_graphql_id($product_id, ShopifyGraphQLEnum::PRODUCT),
            'variants' => [
                [
                    'id' => generate_shopify_graphql_id($variant_id, ShopifyGraphQLEnum::PRODUCT_VARIANT),
                    'price' => $price
                ]
            ]
        ];

        if (!is_null($compare_at_price)) {
            $variables['variants'][0]['compareAtPrice'] = $compare_at_price;
        }


        return $variables;
    }

    public static function getProductVariantPriceListsQuery($variant_id)
    {
        $query = <<<GRAPHQL
            {
                priceLists(first: 100) {
                    nodes {
                        id
                        prices(first: 100, originType: FIXED, query: "variant_id:$variant_id") {
                            nodes {
                                compareAtPrice {
                                    amount
                                    currencyCode
                                }
                                price {
                                    amount
                                    currencyCode
                                }
                            }
                        }
                    }
                }
            }
        GRAPHQL;
        return $query;
    }

    public static function getUpdateProductVariantPriceListsMutationQuery()
    {
        $query = <<<QUERY
        mutation UpdatePriceListFixedPrices (\$priceListId: ID!, \$pricesToAdd: [PriceListPriceInput!]!, \$variantIdsToDelete: [ID!]!) {
            priceListFixedPricesUpdate(priceListId: \$priceListId, pricesToAdd: \$pricesToAdd, variantIdsToDelete: \$variantIdsToDelete) {
                    userErrors {
                        field
                        message
                        code
                    }
                    priceList {
                        id
                    }
            }
        }
QUERY;
        return $query;
    }

    public static function getUpdateProductVariantPriceListsMutationVariables(string $priceListId, Product $product, array $price_data, $compare_price_data)
    {
        $variant_id = $product->shopify_variant_id;

        $pricesData = [
            'price' => [
                'amount' => $price_data['amount'],
                'currencyCode' => $price_data['currencyCode'],
            ],
            'compareAtPrice' => null,
            'variantId' => generate_shopify_graphql_id($variant_id, ShopifyGraphQLEnum::PRODUCT_VARIANT),
        ];
        if ($compare_price_data) {
            $pricesData['compareAtPrice'] = [
                'amount' => $compare_price_data['amount'],
                'currencyCode' => $compare_price_data['currencyCode'],
            ];
        }

        $variables = [
            'priceListId' => $priceListId,
            'pricesToAdd' => [
                $pricesData
            ],
            'variantIdsToDelete' => [],
        ];

        return $variables;
    }

    public static function getAdjustInventoryQuantityMutationQuery()
    {
        $query = <<<QUERY
        mutation AdjustInventoryQuantities (\$input: InventoryAdjustQuantitiesInput!) {
            inventoryAdjustQuantities(input: \$input) {
                userErrors {
                    field
                    message
                }
                inventoryAdjustmentGroup {
                    id
                    reason
                }
            }
        }
        QUERY;
        return $query;
    }

    public static function getAdjustInventoryQuantityMutationVariables($admin_graphql_inventory_item_id, $location_id, $adjust_quantity, $reason, $inventory_name)
    {
        return [
            'name' => $inventory_name,
            'reason' => $reason,
            'changes' => [
                [
                    'delta' => intval($adjust_quantity),
                    'inventoryItemId' => $admin_graphql_inventory_item_id,
                    'locationId' => generate_shopify_graphql_id($location_id, ShopifyGraphQLEnum::LOCATION),
                ]
            ]
        ];
    }

    public static function getCurrentAppSubscriptionsQuery()
    {
        $query = <<<QUERY
        {
            currentAppInstallation {
                activeSubscriptions {
                    id
                    name
                    currentPeriodEnd
                    status
                    test
                    lineItems {
                        id
                        plan {
                            pricingDetails {
                                ... on AppRecurringPricing {
                                    __typename
                                    interval
                                }
                            }
                        }
                    }
                }
            }
        }
        QUERY;
        return $query;
    }

    public static function getCurrentAppSubscriptionsVariables($admin_graphql_charge_id)
    {
        return [
            'id' => $admin_graphql_charge_id
        ];
    }

    public static function getCreateRecurringChargeMutationQuery()
    {
        $query = <<<QUERY
        mutation AppSubscriptionCreate(\$name: String!, \$test: Boolean, \$lineItems: [AppSubscriptionLineItemInput!]!, \$returnUrl: URL!) {
            appSubscriptionCreate(name: \$name, test: \$test, returnUrl: \$returnUrl, lineItems: \$lineItems) {
                userErrors {
                    field
                    message
                }
                appSubscription {
                    id
                }
                confirmationUrl
            }
        }
        QUERY;
        return $query;
    }

    public static function getCreateRecurringChargeMutationVariables($plan_name, $price, $url, $billing_period, $test)
    {
        return [
            'name' => $plan_name,
            'returnUrl' => $url,
            'test' => !!$test,
            'lineItems' => [
                [
                    'plan' => [
                        'appRecurringPricingDetails' => [
                            'price' => [
                                'amount' => $price,
                                'currencyCode' => 'USD'
                            ],
                            'interval' => $billing_period == 'month' ? 'EVERY_30_DAYS' : 'ANNUAL'
                        ]
                    ]
                ]
            ],
        ];
    }
}
