<?php

namespace App\Http\Helpers;

use App\Enums\ShopifyGraphQLEnum;
use App\Models\Merchant;
use App\Traits\HasShopifyAuth;
use App\Http\Helpers\APIHelper;
use App\Models\Product;
use Illuminate\Support\Facades\Log;

class ShopifyAPIHelper extends APIHelper
{
    use HasShopifyAuth;

    public function __construct($subdomain, $access_token)
    {
        self::$shop = $subdomain;
        self::$access_token = $access_token;
    }

    public static function withAuth($subdomain, $access_token)
    {
        $instance = new self($subdomain, $access_token);
        return $instance;
    }

    public static function withMerchant(Merchant $merchant)
    {
        $instance = new self($merchant->subdomain, $merchant->access_token);
        return $instance;
    }

    public function sendGraphQLRequest($query, $variables = [], $error_message = null)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ],
            'json' => [
                'query' => $query
            ]
        ];
        if (count($variables) > 0) {
            $requestContent['json']['variables'] = $variables;
        }
        $response = self::request('POST', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/graphql.json', $requestContent, $error_message);
        return $response;
    }

    public function getShopDetails($shop, $access_token)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => $access_token
            ]
        ];
        $response = self::request('GET', 'https://' . $shop . '/admin/api/' . config('services.shopify.api_version') . '/shop.json', $requestContent, 'Failed to get shop details');
        return $response->shop;
    }

    public function getAccessScopes()
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        $response = self::request('GET', 'https://' . self::$shop . '/admin/oauth/access_scopes.json', $requestContent, 'Failed to get access scopes');
        return $response->access_scopes;
    }

    public function getProductsIDs($cursor)
    {
        $query = GraphQLQueryHelper::getProductIDsQuery();
        $variables = GraphQLQueryHelper::getProductIDsVariables($cursor);
        $response = $this->sendGraphQLRequest($query, $variables, 'Failed to get products IDs');
        if (isset($response->data)) {
            return $response->data;
        }
        return $response;
    }

    public function getProduct($id, $variant_cursor = null)
    {
        $query = GraphQLQueryHelper::getProductQuery();
        $variables = GraphQLQueryHelper::getProductVariables($id, $variant_cursor);
        $response = $this->sendGraphQLRequest($query, $variables, 'Failed to get products');
        if (isset($response->data)) {
            return $response->data->product;
        }
        return $response;
    }

    public function getProductStoreURL($id)
    {
        $query = GraphQLQueryHelper::getProductStoreUrlQuery($id);
        $response = $this->sendGraphQLRequest($query, [], 'Failed to get product store url');
        if (isset($response->data)) {
            return $response->data->product;
        }
        return $response;
    }

    public function getProductVariant($variant_id)
    {
        $query = GraphQLQueryHelper::getProductVariantQuery($variant_id);
        $response = $this->sendGraphQLRequest($query, [], 'Failed to get product variant details');
        if (isset($response->data)) {
            return $response->data->productVariant;
        }
        return $response;
    }

    public function getProductVariantInventoryItemID($variant_id)
    {
        $query = GraphQLQueryHelper::getProductVariantInventoryItemIDQuery($variant_id);
        $response = $this->sendGraphQLRequest($query, [], 'Failed to get product variant inventory item ID');
        if (isset($response->data)) {
            return $response->data->productVariant->inventoryItem->id;
        }
        return $response;
    }

    public function updateProductVariantPrices(Product $product, $price, $compare_at_price = null)
    {
        $query = GraphQLQueryHelper::getUpdateProductVariantPricesMutationQuery();
        $variables = GraphQLQueryHelper::getUpdateProductVariantPricesMutationVariables($product, $price, $compare_at_price);
        $response = $this->sendGraphQLRequest($query, $variables, 'Failed to update product variant prices');
        if (isset($response->data)) {
            return $response->data->productVariantsBulkUpdate->productVariants;
        }
        return $response;
    }

    public function getProductVariantPriceLists($variant_id)
    {
        $query = GraphQLQueryHelper::getProductVariantPriceListsQuery($variant_id);
        $response = $this->sendGraphQLRequest($query, [], 'Failed to get product variant price lists');
        if (isset($response->data)) {
            return $response->data->priceLists->nodes;
        }
        return $response;
    }

    public function updateProductVariantPriceLists(string $priceListId, Product $product, $price, $compare_at_price)
    {
        $query = GraphQLQueryHelper::getUpdateProductVariantPriceListsMutationQuery();
        $variables = GraphQLQueryHelper::getUpdateProductVariantPriceListsMutationVariables($priceListId, $product, $price, $compare_at_price);
        $response = $this->sendGraphQLRequest($query, $variables, 'Failed to update product variant price lists');
        if (isset($response->data)) {
            return $response->data->priceListFixedPricesUpdate->priceList;
        }
        return $response;
    }

    public function getShopMetafields($key)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/metafields.json?namespace=' . config('services.shopify.metafield_namespace') . '&key=' . $key, $requestContent, 'Failed to retrieve shop metafields');
        return $response->metafields;
    }

    public function createOrUpdateShopMetafield($key, $value, $type, $skip_if_same_value = true)
    {
        if ($value !== null) {
            $metafields = $this->getShopMetafields($key);
            if (count($metafields) > 0) {
                $shop_metafield = $metafields[0];
                if ($shop_metafield->value == $value && $skip_if_same_value) {
                    return $shop_metafield;
                }
                return $this->updateShopMetafields($shop_metafield->id, $value, $type);
            } else {
                return $this->createShopMetafields($key, $value, $type);
            }
        }
    }

    public function createShopMetafields($key, $value, $type)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ],
            'json' => [
                'metafield' => [
                    'namespace' => config('services.shopify.metafield_namespace'),
                    'key' => $key,
                    'value' => $value,
                    'type' => $type
                ]
            ]
        ];
        $response = self::request('POST', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/metafields.json', $requestContent, 'Failed to create shop metafields');
        return $response->metafield;
    }

    public function updateShopMetafields($metafield_id, $value, $type)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ],
            'json' => [
                'metafield' => [
                    'id' => $metafield_id,
                    'value' => $value,
                    'type' => $type
                ]
            ]
        ];
        $response = self::request('PUT', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/metafields/' . $metafield_id . '.json', $requestContent, 'Failed to update shop metafields');
        return $response->metafield;
    }

    public function getVariantMetafields($variant_id, $key = null)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        try {
            $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/variants/' . $variant_id . '/metafields.json' . ($key ? '?namespace=' . config('services.shopify.metafield_namespace') . '&key=' . $key : ''), $requestContent, 'Failed to retrieve variant metafields');
            return $response->metafields;
        } catch (\Exception $e) {
            info($e->getMessage());
            return [];
        }
    }

    public function createOrUpdateVariantMetafield($variant_id, $key, $value, $type, $skip_if_same_value = true)
    {
        if ($value !== null) {
            $metafields = $this->getVariantMetafields($variant_id, $key);
            if (count($metafields) > 0) {
                $variant_metafield = $metafields[0];
                if ($variant_metafield->value == $value && $skip_if_same_value) {
                    return $variant_metafield;
                }
                return $this->updateVariantMetafields($variant_metafield->id, $variant_id, $value, $type);
            } else {
                return $this->createVariantMetafields($variant_id, $key, $value, $type);
            }
        }
    }

    public function createVariantMetafields($variant_id, $key, $value, $type)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ],
            'json' => [
                'metafield' => [
                    'namespace' => config('services.shopify.metafield_namespace'),
                    'key' => $key,
                    'value' => $value,
                    'type' => $type
                ]
            ]
        ];
        $response = self::request('POST', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/variants/' . $variant_id . '/metafields.json', $requestContent, 'Failed to create variant metafields');
        return $response->metafield;
    }

    public function updateVariantMetafields($metafield_id, $variant_id, $value, $type)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ],
            'json' => [
                'metafield' => [
                    'id' => $metafield_id,
                    'value' => $value,
                    'type' => $type
                ]
            ]
        ];
        $response = self::request('PUT', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/variants/' . $variant_id . '/metafields/' . $metafield_id . '.json', $requestContent, 'Failed to update variant metafields');
        return $response->metafield;
    }

    public function deleteVariantMetafields($variant_id, $key)
    {
        $metafields = $this->getVariantMetafields($variant_id, $key);
        if ($metafields && count($metafields) > 0) {
            $metafield_id = $metafields[0]->id;
            $this->deleteVariantMetafieldById($variant_id, $metafield_id);
        }
    }

    public function deleteVariantMetafieldById($variant_id, $metafield_id)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        self::request('DELETE', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/variants/' . $variant_id . '/metafields/' . $metafield_id . '.json', $requestContent, 'Failed to delete variant metafields');
    }

    public function adjustInventoryLevel($admin_graphql_inventory_item_id, $location_id, $adjustment_quantity, $reason, $inventory_name = ShopifyGraphQLEnum::INVENTORY_STATE_AVAILABLE)
    {
        $query = GraphQLQueryHelper::getAdjustInventoryQuantityMutationQuery();
        $variables = GraphQLQueryHelper::getAdjustInventoryQuantityMutationVariables($admin_graphql_inventory_item_id, $location_id, $adjustment_quantity, $reason, $inventory_name);
        $response = $this->sendGraphQLRequest($query, ['input' => $variables], 'Failed to adjust inventory level');
    }

    public function getOrder($order_id, $fields = [])
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        if (count($fields) > 0) {
            $fields = '?fields=' . implode(',', $fields);
        } else {
            $fields = '';
        }
        $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/orders/' . $order_id . '.json' . $fields, $requestContent, 'Failed to get order');
        return $response->order;
    }

    public function getLocations()
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/locations.json', $requestContent, 'Failed to get locations');
        return $response->locations;
    }

    public function getCurrentAppSubscriptions($admin_graphql_charge_id = null)
    {
        $query = GraphQLQueryHelper::getCurrentAppSubscriptionsQuery();
        $variables = GraphQLQueryHelper::getCurrentAppSubscriptionsVariables($admin_graphql_charge_id);
        $response = $this->sendGraphQLRequest($query, $variables, 'Failed to get recurring charge');
        return $response->data;
    }

    public function createRecurringCharge($plan_name, $price, $url, $billing_period = 'month', $test = null)
    {
        $query = GraphQLQueryHelper::getCreateRecurringChargeMutationQuery();
        $variables = GraphQLQueryHelper::getCreateRecurringChargeMutationVariables($plan_name, $price, $url, $billing_period, $test);
        $response = $this->sendGraphQLRequest($query, $variables, 'Failed to create recurring charge');
        return $response->data;
    }

    public function deleteRecurringCharge($charge_id)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        self::request('DELETE', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/recurring_application_charges/' . $charge_id . '.json', $requestContent, 'Failed to delete recurring charge');
    }

    public function getThemes()
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/themes.json', $requestContent, 'Failed to get themes');
        return $response->themes;
    }

    public function getTheme($theme_id)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/themes/' . $theme_id . '.json', $requestContent, 'Failed to get theme or theme not found');
        return $response->theme;
    }

    public function getThemeAssets($theme_id)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/themes/' . $theme_id . '/assets.json', $requestContent, 'Failed to get themes');
        return $response->assets;
    }

    public function getSingleAsset($theme_id, $asset_key)
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/themes/' . $theme_id . '/assets.json?asset[key]=' . $asset_key, $requestContent, 'Failed to get themes');
        return $response->asset;
    }

    public function getWebhooks()
    {
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ]
        ];
        $response = self::request('GET', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/webhooks.json', $requestContent, 'Failed to get webhooks');
        return $response->webhooks;
    }

    /**
     *
     * Create a Shopify webhook
     *
     * @param string $topic Shopify defined webhook topic/subject in their Docs
     * @param string $api_path API path for webhook event to revoke (eg. https://{app_url}/api/shopify/product)
     * @param string $fields (Optional) Filter fields to return by webhook
     * @param string $error_message (Optional) Error message to output when error occurred
     * @return void
     *
     */
    public function createWebhook($topic, $api_path, $fields = null, $error_message = null)
    {
        $webhook_creation_data = [
            'topic' => $topic,
            'address' => $api_path,
            'format' => 'json'
        ];
        if ($fields) {
            $webhook_creation_data['fields'] = $fields;
        }
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ],
            'json' => [
                'webhook' => $webhook_creation_data
            ]
        ];
        self::request('POST', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/webhooks.json', $requestContent, $error_message ?: 'Failed to create webhooks', false, false);
    }

    /**
     *
     * Update an existing webhook
     *
     * @param string $webhook_id ID for existing webhook to be updated
     * @param string $api_path API path for webhook event to revoke (eg. https://{app_url}/api/shopify/product)
     * @param string $fields (Optional) Filter fields to return by webhook
     * @param string $error_message (Optional) Error message to output when error occurred
     * @return void
     *
     */
    public function updateWebhook($webhook_id, $api_path, $fields = null, $error_message = null)
    {
        $webhook_update_data = [
            'id' => $webhook_id,
            'address' => $api_path
        ];
        if ($fields) {
            $webhook_update_data['fields'] = $fields;
        }
        $requestContent = [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => self::$access_token
            ],
            'json' => [
                'webhook' => $webhook_update_data
            ]
        ];
        self::request('PUT', 'https://' . self::$shop . '/admin/api/' . config('services.shopify.api_version') . '/webhooks/' . $webhook_id . '.json', $requestContent, $error_message ?: 'Failed to update webhooks', false, false);
    }
}
