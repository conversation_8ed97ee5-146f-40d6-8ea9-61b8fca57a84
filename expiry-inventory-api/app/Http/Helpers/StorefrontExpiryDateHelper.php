<?php

namespace App\Http\Helpers;

use App\Enums\PlanEnum;
use App\Models\Product;
use App\Models\Merchant;
use App\Http\Helpers\ShopifyAPIHelper;
use Illuminate\Support\Collection;

class StorefrontExpiryDateHelper
{
    public static function updateStorefrontExpiryDate(Collection $products)
    {
        $first_product = $products->first();
        if ($first_product) {
            $merchant = $first_product->merchant()->withTrashed()->first();
            if (!$merchant->trashed()) {
                $merchant_setting = $merchant->setting();
                $shopifyHelper = ShopifyAPIHelper::withMerchant($merchant);

                $products->whereNull('deleted_at')->each(function (Product $product) use ($merchant_setting, $shopifyHelper) {
                    if ($merchant_setting['plan'] != PlanEnum::FREE) {
                        $nearest_expiry_batch_item = $product->batch_items()->expiringSoon()->orderNearestAuto()->first();
                        $existingMetafields = collect($shopifyHelper->getVariantMetafields($product->shopify_variant_id) ?? []);
                        $expiryDateMetafield = $existingMetafields->firstWhere('key', 'expire_at');
                        if ($nearest_expiry_batch_item && $nearest_expiry_batch_item->expire_at) {
                            if ($expiryDateMetafield) {
                                $shopifyHelper->updateVariantMetafields($expiryDateMetafield->id, $product->shopify_variant_id, $nearest_expiry_batch_item->expire_at, 'date');
                            } else {
                                $shopifyHelper->createVariantMetafields($product->shopify_variant_id, 'expire_at', $nearest_expiry_batch_item->expire_at, 'date');
                            }
                        } else {
                            if ($expiryDateMetafield) {
                                $shopifyHelper->deleteVariantMetafieldById(
                                    $product->shopify_variant_id,
                                    $expiryDateMetafield->id
                                );
                            }
                        }

                        $bestBeforeMetafield = $existingMetafields->firstWhere('key', 'best_before');
                        if ($nearest_expiry_batch_item && $nearest_expiry_batch_item->date) {
                            if ($bestBeforeMetafield) {
                                $shopifyHelper->updateVariantMetafields($bestBeforeMetafield->id, $product->shopify_variant_id, $nearest_expiry_batch_item->date, 'date');
                            } else {
                                $shopifyHelper->createVariantMetafields($product->shopify_variant_id, 'best_before', $nearest_expiry_batch_item->date, 'date');
                            }
                        } else {
                            if ($bestBeforeMetafield) {
                                $shopifyHelper->deleteVariantMetafieldById(
                                    $product->shopify_variant_id,
                                    $bestBeforeMetafield->id
                                );
                            }
                        }

                        $batchQuantityMetafield = $existingMetafields->firstWhere('key', 'batch_quantity');
                        if ($nearest_expiry_batch_item) {
                            if ($batchQuantityMetafield) {
                                $shopifyHelper->updateVariantMetafields($batchQuantityMetafield->id, $product->shopify_variant_id, $nearest_expiry_batch_item->quantity, 'number_integer');
                            } else {
                                $shopifyHelper->createVariantMetafields($product->shopify_variant_id, 'batch_quantity', $nearest_expiry_batch_item->quantity, 'number_integer');
                            }
                        } else {
                            if ($batchQuantityMetafield) {
                                $shopifyHelper->deleteVariantMetafieldById(
                                    $product->shopify_variant_id,
                                    $batchQuantityMetafield->id
                                );
                            }
                        }
                    }
                });
            }
        }
    }

    public static function removeStorefrontExpiryDate(Merchant $merchant, Product $product)
    {
        $shopifyHelper = ShopifyAPIHelper::withMerchant($merchant);

        $existingMetafields = collect($shopifyHelper->getVariantMetafields($product->shopify_variant_id) ?? []);
        
        $expiryDateMetafield = $existingMetafields->firstWhere('key', 'expire_at');
        if ($expiryDateMetafield) {
            $shopifyHelper->deleteVariantMetafieldById(
                $product->shopify_variant_id,
                $expiryDateMetafield->id
            );
        }

        $bestBeforeMetafield = $existingMetafields->firstWhere('key', 'best_before');
        if ($bestBeforeMetafield) {
            $shopifyHelper->deleteVariantMetafieldById(
                $product->shopify_variant_id,
                $bestBeforeMetafield->id
            );
        }

        $batchQuantityMetafield = $existingMetafields->firstWhere('key', 'batch_quantity');
        if ($batchQuantityMetafield) {
            $shopifyHelper->deleteVariantMetafieldById(
                $product->shopify_variant_id,
                $batchQuantityMetafield->id
            );
        }
    }
}