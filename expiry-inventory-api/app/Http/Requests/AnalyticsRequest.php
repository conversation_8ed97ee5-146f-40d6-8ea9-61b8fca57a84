<?php

namespace App\Http\Requests;

use App\Enums\AnalyticsExpiryTypeEnum;
use Illuminate\Foundation\Http\FormRequest;

class AnalyticsRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'limit' => ['sometimes', 'numeric'],
            'page' => ['sometimes', 'numeric'],
            'type' => ['sometimes', 'in:' . implode(',', AnalyticsExpiryTypeEnum::all())],
            'days' => ['sometimes', 'numeric'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
