<?php

namespace App\Http\Requests;

use App\Enums\ProductStatus;
use App\Enums\ProductFilterEnum;
use App\Enums\ProductOrderByEnum;
use App\Enums\OrderDirectionEnum;
use App\Enums\ProductInventoryStatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class ProductIndexRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'limit' => ['sometimes', 'numeric'],
            'location' => ['sometimes', 'exists:locations,id', 'nullable'],
            'filter' => ['sometimes', 'in:' . implode(',', ProductFilterEnum::all()), 'nullable'],
            'status' => ['sometimes', 'in:' . implode(',', ProductStatus::all()), 'nullable'],
            'inventory_status' => ['sometimes', 'in:' . implode(',', ProductInventoryStatusEnum::all())],
            'with_unaccounted_quantity' => ['sometimes', 'boolean'],
            'search' => ['sometimes', 'string', 'nullable'],
            'page' => ['sometimes', 'numeric'],
            'order_by' => ['sometimes', 'in:' . implode(',', ProductOrderByEnum::all()), 'nullable'],
            'order_direction' => ['sometimes', 'in:' . implode(',', OrderDirectionEnum::all()), 'nullable'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
