<?php

namespace App\Http\Stores;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Spatie\GuzzleRateLimiterMiddleware\Store;
use Illuminate\Support\Facades\Cache;

class ShopifyRateLimiterStore implements Store
{
    public function get(): array
    {
        return Cache::get('shopify-rate-limiter', []);
    }

    public function push(int $timestamp, int $limit)
    {
        $existingTimestamps = $this->get();
        // consider past minute limiting as old
        $oldTimestamp = Carbon::now()->subMinutes(1)->getPreciseTimestamp(3);
        $current = Arr::where($existingTimestamps, fn ($existing_timestamp) => $existing_timestamp >= $oldTimestamp);
        Cache::put('shopify-rate-limiter', array_merge($current, [$timestamp]));
    }
}