<?php

namespace App\Imports;

use App\Enums\ImportEnum;
use App\Mail\ImportCompletedMail;
use App\Models\ImportHistory;
use App\Models\Merchant;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Events\AfterImport;

abstract class Import implements WithHeadingRow, WithEvents
{
    use Importable;
    use RegistersEventListeners;

    protected $merchant_id;
    protected $import_history_id;

    public function __construct(Merchant $merchant, ImportHistory $import_history)
    {
        $this->merchant_id = $merchant->id;
        $this->import_history_id = $import_history->id;
    }

    public function registerEvents(): array
    {
        return [
            AfterImport::class => function(AfterImport $event) {
                if($this->import_history_id) {
                    $history = ImportHistory::findOrFail($this->import_history_id);
                    if($history) {
                        $history->status = "success";
                        $history->save();

                        $this->sendImportCompletedEmail($history);
                    }
                }
            },
        ];
    }

    protected function sendImportCompletedEmail(ImportHistory $import_history)
    {
        $merchant = Merchant::findOrFail($this->merchant_id);
        $merchant->load('user');
        if($merchant) {
            $emails = $merchant->getNotifcationEmails();
            $this->sendImportedEmail($merchant, $import_history, $emails);
        }
    }

    protected function sendImportedEmail(Merchant $merchant, ImportHistory $import_history, $emails)
    {
        $target = null;
        $url = null;
        switch($import_history->model)
        {
            case ImportEnum::INVENTORY_BATCH:
                $target = Str::replace('_', ' ', $import_history->model);
                $url = 'https://' . $merchant->subdomain . '/admin/apps/' . config('services.shopify.key') . '/imports/' . $import_history->id;
                break;
        }

        if($target && $merchant->user) {
            Log::info('ImportEmailNotify', ['merchant' => $merchant->subdomain, 'import' => $import_history->id, 'email' => $emails]);
            foreach ($emails as $email) {
                if ($email && strlen($email) > 0) {
                    Mail::to($email)->send(new ImportCompletedMail($merchant->user->name, $target, $url));
                }
            }
        }
    }
}