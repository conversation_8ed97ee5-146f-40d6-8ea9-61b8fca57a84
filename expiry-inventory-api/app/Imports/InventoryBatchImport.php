<?php

namespace App\Imports;

use App\Enums\PlanEnum;
use App\Http\Controllers\InventoryBatchController;
use App\Models\ImportHistory;
use App\Models\InventoryBatch;
use App\Models\Merchant;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class InventoryBatchImport extends Import implements ToArray, ShouldQueue, WithChunkReading
{
    public function array(array $rows)
    {
        $import_history = ImportHistory::findOrFail($this->import_history_id);
        $merchant = Merchant::findOrFail($this->merchant_id);
        $locations = $merchant->locations()->get();
        $inventoryBatchController = new InventoryBatchController;
        $inventoryBatchController->setShopifyAuth($merchant->subdomain, $merchant->access_token);
        $inventoryBatchController->import_id = $import_history->id;

        $merchantSettings = $merchant->setting();
        $meta_presets = $merchantSettings['plan'] == PlanEnum::FREE ? [] : Arr::get($merchantSettings, 'meta_presets', []);

        $mapped_meta_presets = array_map(function ($preset) {
            return [
                'name' => $preset,
                'slug' => Str::slug($preset, '_')
            ];
        }, $meta_presets);

        $default_batch_name = 'Batch Import #' . $this->import_history_id;

        $rows_count = 0;
        foreach ($rows as $row) {
            $location = $locations->where('shopify_location_id', $row['location'])->first();
            if (!$location) {
                Log::error("ImportError", ["message" => "Location not found", "row" => $row]);
                continue;
            }

            $product = $location->products()->withTrashed(false)->where('sku', $row['variant_sku'])->first();
            if (!$product) {
                Log::error("ImportError", ["message" => "Product not found", "row" => $row]);
                continue;
            }

            $received_at = Arr::get($row, 'date_received_dmy');
            $received_at = $received_at && $received_at != '' ? $this->tryParseDate($received_at, ['d/m/Y', 'm/d/Y']) : null;
            $expire_date = Arr::get($row, 'expire_date_dmy');
            $expire_date = $expire_date && $expire_date != '' ? $this->tryParseDate($expire_date, ['d/m/Y', 'm/d/Y']) : null;
            $best_before_dates = Arr::get($row, 'best_before_date_dmy');
            $best_before_dates = $best_before_dates && $best_before_dates != '' ? $this->parseBestBeforeDates($best_before_dates) : [];

            $batch = [
                'name' => Arr::get($row, 'batch_name', $default_batch_name),
                'inventory_batch_id' => ($row['inventory_batch_id'] != '' ? $row['inventory_batch_id'] : null),
                'merchant_id' => $merchant->id,
                'location_id' => $location->id,
                'is_sync_quantity' => false, // make sync quantity default to false because mostly merchants just want to import inventory and not update Shopify
                'received_at' => $received_at,
                'lot_number' => $row['lot_batch_number'],
                'bin_location' => $row['bin_location'],
                'barcode' => $row['barcode'],
                'invoice_number' => $row['invoice_number'],
                'description' => $row['description'],
                'items' => []
            ];

            $meta = [];
            foreach ($mapped_meta_presets as $meta_preset) {
                // import included with preset name and value
                $meta_value = Arr::get($row, $meta_preset['slug']);
                if ($meta_value && strlen($meta_value)) {
                    // save meta value
                    $meta[] = [
                        'name' => $meta_preset['name'],
                        'value' => $meta_value
                    ];
                }
            }

            if (count($meta)) $batch['meta'] = $meta;

            $batch['items'] = [
                [
                    'product_id' => $product->id,
                    'quantity' => $row['quantity'] != '' ? $row['quantity'] : $product->pivot->on_hand,
                    'expire_at' => $expire_date,
                    'dates' => $best_before_dates,
                ]
            ];
            $inventory_batch = null;
            try {
                if ($batch['inventory_batch_id']) {
                    $inventory_batch = $merchant->inventory_batches()->find($batch['inventory_batch_id']);
                    if ($inventory_batch) {
                        $inventory_batch->fill(Arr::only($batch, [
                            'name',
                            'received_at',
                            'lot_number',
                            'bin_location',
                            'barcode',
                            'invoice_number',
                            'description',
                            'is_sync_quantity',
                            'meta'
                        ]))->save();
                        $inventoryBatchController->syncBatchProducts($inventory_batch, json_decode(json_encode($batch)), 'update');
                    }
                }

                if (!$inventory_batch) {
                    // create batch if inventory batch ID is blank or existing batch not found
                    $inventory_batch = InventoryBatch::create(Arr::only($batch, [
                        'merchant_id',
                        'location_id',
                        'name',
                        'received_at',
                        'lot_number',
                        'bin_location',
                        'barcode',
                        'invoice_number',
                        'description',
                        'is_sync_quantity',
                        'meta'
                    ]));
                    $inventoryBatchController->syncBatchProducts($inventory_batch, json_decode(json_encode($batch)));
                }

                $rows_count++;
            } catch (\Exception $e) {
                Log::error("ImportError", ["exception" => $e, "row" => $row]);
            }
        }

        $import_history->update([
            'rows_count' => $import_history->rows_count + $rows_count
        ]);
    }

    public function chunkSize(): int
    {
        return 25;
    }

    private function parseBestBeforeDates($semicolon_separated_string)
    {
        $dates = explode(';', $semicolon_separated_string);
        return array_map(fn ($date) => $this->tryParseDate($date, ['d/m/Y', 'm/d/Y']), $dates);
    }

    private function tryParseDate($date_string, $formats = [])
    {
        try {
            // try parse with number date
            $date = Carbon::parse(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($date_string));
            if ($date) {
                return $date->format('Y-m-d');
            }
        } catch (\Throwable $th) {
            Log::error("ParseExcelDateError", ["exception" => $th]);
        }

        foreach ($formats as $format) {
            try {
                $date = Carbon::createFromFormat($format, $date_string);
                if ($date) {
                    return $date->format('Y-m-d');
                }
            } catch (\Throwable $th) {
                Log::error("ParseDateFormatError", ["exception" => $th]);
            }
        }
        return null;
    }
}
