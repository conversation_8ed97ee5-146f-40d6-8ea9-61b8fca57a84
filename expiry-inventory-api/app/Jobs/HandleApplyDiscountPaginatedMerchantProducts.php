<?php

namespace App\Jobs;

use App\Http\Helpers\DiscountHelper;
use App\Models\Merchant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class HandleApplyDiscountPaginatedMerchantProducts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Merchant $merchant;
    public $page;
    public $pageSize = 50;

    public $timeout = 600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant, $page = 1)
    {
        $this->merchant = $merchant;
        $this->page = $page;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('HandleApplyDiscountPaginatedMerchantProducts', ['merchant' => $this->merchant->subdomain, 'page' => $this->page]);

        try {
            $paginatedProducts = $this->merchant->products()->paginate($this->pageSize, ['*'], 'page', $this->page);
            $products_list = $paginatedProducts->items();

            DiscountHelper::applyDiscountForProducts(collect($products_list));

            HandleApplyDiscountPaginatedMerchantProducts::dispatchIf($paginatedProducts->hasMorePages(), $this->merchant, $this->page + 1);
        }
        catch(\Exception $e) {
            Log::error($e);
            Log::error("Exception when apply products discount for merchant {$this->merchant->subdomain} : {$this->page}");
        }
    }
}
