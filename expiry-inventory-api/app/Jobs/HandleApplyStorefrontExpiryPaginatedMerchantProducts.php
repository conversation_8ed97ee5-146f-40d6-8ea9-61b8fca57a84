<?php

namespace App\Jobs;

use App\Http\Helpers\DiscountHelper;
use App\Http\Helpers\StorefrontExpiryDateHelper;
use App\Models\Merchant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class HandleApplyStorefrontExpiryPaginatedMerchantProducts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Merchant $merchant;
    public $page;
    public $pageSize = 30;

    public $timeout = 600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant, $page = 1)
    {
        $this->merchant = $merchant;
        $this->page = $page;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('HandleApplyStorefrontExpiryPaginatedMerchantProducts', ['merchant' => $this->merchant->subdomain, 'page' => $this->page]);

        try {
            $paginatedProducts = $this->merchant->products()->whereNull('products.deleted_at')->paginate($this->pageSize, ['*'], 'page', $this->page);
            $products_list = $paginatedProducts->items();

            StorefrontExpiryDateHelper::updateStorefrontExpiryDate(collect($products_list));

            HandleApplyStorefrontExpiryPaginatedMerchantProducts::dispatchIf($paginatedProducts->hasMorePages(), $this->merchant, $this->page + 1)->delay(10);
        }
        catch(\Exception $e) {
            Log::error($e);
            Log::error("Exception when apply storefront expiry update for merchant {$this->merchant->subdomain} : {$this->page}");
        }
    }
}
