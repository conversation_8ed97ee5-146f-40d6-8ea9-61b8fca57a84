<?php

namespace App\Jobs;

use App\Http\Helpers\DiscountHelper;
use App\Http\Helpers\StorefrontExpiryDateHelper;
use App\Models\Merchant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class HandleClearMerchantBatchesData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Merchant $merchant;
    public $page;
    protected $limit = 50;

    public $timeout = 600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant, $page = 1)
    {
        $this->merchant = $merchant;
        $this->page = $page;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $merchant = $this->merchant;
        Log::info('HandleClearMerchantBatchesData', ['merchant' => $merchant->subdomain, 'page' => $this->page]);

        try {
            $inventory_batches_paginator = $merchant->inventory_batches()->paginate($this->limit);

            $has_next_page = $inventory_batches_paginator->hasMorePages();

            foreach ($inventory_batches_paginator->items() as $inventory_batch) {
                foreach ($inventory_batch->items as $batch_item) {
                    DiscountHelper::revertProductDiscountedPrice($merchant, $batch_item->product);
                    try {
                        StorefrontExpiryDateHelper::removeStorefrontExpiryDate($merchant, $batch_item->product);
                    }
                    catch (\Exception $e) {
                        Log::error($e);
                    }
                    foreach ($batch_item->dates as $date) {
                        $date->delete();
                    }
                    $batch_item->deleteQuietly();
                }
                foreach ($inventory_batch->inventory_histories as $history) {
                    $history->delete();
                }
                $inventory_batch->deleteQuietly();
            }

            HandleClearMerchantBatchesData::dispatchIf($has_next_page, $merchant, $this->page + 1);
        } catch (\Exception $e) {
            Log::error($e);
            Log::error("Exception when clear batches data for merchant {$merchant->subdomain} : {$this->page}");
        }
    }
}
