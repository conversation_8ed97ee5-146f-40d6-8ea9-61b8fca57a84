<?php

namespace App\Jobs;

use App\Mail\BatchOutOfStockMail;
use App\Models\InventoryBatch;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class HandleItemOutOfStock implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public InventoryBatch $inventory_batch;

    public $timeout = 600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(InventoryBatch $inventory_batch)
    {
        $this->inventory_batch = $inventory_batch;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $inventory_batch = $this->inventory_batch;
        $inventory_batch->loadMissing(['merchant.user', 'items.product']);

        $merchant = $inventory_batch->merchant;

        $merchant_setting = $inventory_batch->merchant->setting();

        if(Arr::get($merchant_setting, 'is_notify_batch_out_of_stock')) {
            try {
                $emails = $merchant->getNotifcationEmails();
                $details = [
                    'date' => Carbon::now()->format('M d, Y'),
                    'app_key' => config('services.shopify.key'),
                    'merchant_subdomain' => $merchant->subdomain,
                    'merchant_name' => $merchant->user->name,
                    'out_of_stock_batches' => [$inventory_batch]
                ];

                foreach ($emails as $email) {
                    if ($email && strlen($email) > 0) {
                        Mail::to($email)->send(new BatchOutOfStockMail($details));
                    }
                }
                Log::info('Emailed batch out of stock notification', ['email' => $emails]);
            }
            catch(\Exception $e) {
                Log::info('BatchesOutOfStockEmailError', ['error' => $e]);
            }
        }
    }
}
