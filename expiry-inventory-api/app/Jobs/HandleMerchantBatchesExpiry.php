<?php

namespace App\Jobs;

use App\Enums\ProductStatus;
use App\Enums\ShopifyGraphQLEnum;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Mail\BatchExpiredMail;
use App\Mail\BatchExpiringSoonMail;
use App\Models\InventoryAction;
use App\Models\InventoryHistory;
use App\Models\Merchant;
use App\Services\ShopifyService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class HandleMerchantBatchesExpiry implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Merchant $merchant;

    public $timeout = 600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant)
    {
        $this->merchant = $merchant;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $merchant = $this->merchant;

        Log::info('HandleMerchantBatchesExpiry', ['merchant' => $merchant->subdomain]);

        $merchant_setting = $merchant->setting();

        $batches = $merchant->inventory_batches()
            ->with('location', 'items.product')
            ->whereHas('items', function ($query) use ($merchant_setting) {
                $query->expiringSoon()
                    ->dateIsExpired($merchant_setting['is_remove_when_expired'] ? $merchant_setting['days_before_expire'] : 0);
            })
            ->get();
        Log::info('ExpiringBatches', [$batches]);

        $shopifyHelper = ShopifyAPIHelper::withMerchant($merchant);

        foreach ($batches as $batch) {
            foreach ($batch->items as $batch_item) {
                try {
                    $product = $batch_item->product;
                    if ($batch_item->status === 'discounted') {
                        // revert product price
                        if (!$merchant->trashed()) {
                            (new ShopifyService)
                                ->setMerchant($merchant)
                                ->setProduct($product)
                                ->updateShopifyProductPrices();
                        }
                        $product->price = $product->original_price;
                        $product->status = ProductStatus::ACTIVE;
                        $product->save();
                    }
                    $batch_item->status = 'expired';
                    $batch_item->save();

                    foreach ($batch_item->discounts()->active()->get() as $revert_discount) {
                        $revert_discount->deactivate();
                    }

                    $batch = $batch_item->batch;

                    // adjust only if want to remove stock when expired
                    if (!$merchant->trashed() && $merchant_setting['is_remove_when_expired']) {
                        $location = $batch->location()->first();

                        if ($product->locations()->whereKey($location->id)->exists()) {
                            $admin_graphql_inventory_item_id = $shopifyHelper->getProductVariantInventoryItemID($product->shopify_variant_id);

                            // adjust quantity for shopify inventory item
                            $shopifyHelper->adjustInventoryLevel($admin_graphql_inventory_item_id, $location->shopify_location_id, -($batch_item->quantity), ShopifyGraphQLEnum::INVENTORY_ON_HAND_CORRECTION);
                        }
                    }
                    $batch_action = InventoryAction::getBySlug('batch-marked-expired');
                    InventoryHistory::createWithBatchItem([
                        'merchant_id' => $batch->merchant_id,
                        'action_id' => $batch_action->id,
                        'batch_id' => $batch_item->inventory_batch_id,
                        'adjustment' => -($batch_item->quantity)
                    ], $batch_item);

                    $batch->checkForLatestStatus();
                } catch (\Exception $e) {
                    Log::info('BatchesExpiredError', ['error' => $e]);
                }
            }
        }

        // send email notification
        if ($merchant_setting['is_email_notification'] && count($batches) > 0) {
            try {
                $emails = $merchant->getNotifcationEmails();
                $details = [
                    'date' => Carbon::now()->format('M d, Y'),
                    'app_key' => config('services.shopify.key'),
                    'merchant_subdomain' => $merchant->subdomain,
                    'merchant_name' => $merchant->user->name,
                    'expired_batches' => $batches->toArray()
                ];

                foreach ($emails as $email) {
                    if ($email && strlen($email) > 0) {
                        Mail::to($email)->send(new BatchExpiredMail($details));
                    }
                }

                Log::info('Emailed batch expiration notification', ['email' => $emails]);
            } catch (\Exception $e) {
                Log::info('BatchesExpiredEmailError', ['error' => $e]);
            }
        }

        // send batch expiring soon notification
        if (Arr::get($merchant_setting, 'is_notify_batch_expiration')) {
            $batch_expiration_periods = Arr::get($merchant_setting, 'notify_batch_expiration');
            $batch_expiration_periods = !is_array($batch_expiration_periods) ? [$batch_expiration_periods] : $batch_expiration_periods;
            foreach ($batch_expiration_periods as $batch_expiration_period) {
                if (Arr::get($batch_expiration_period, 'period') && Arr::get($batch_expiration_period, 'unit')) {
                    $expiration_date = Carbon::now()->add(Arr::get($batch_expiration_period, 'unit'), Arr::get($batch_expiration_period, 'period'));
                    $expiring_batches = $merchant->inventory_batches()
                        ->with('location', 'items.product')
                        ->whereHas('items', function ($query) use ($expiration_date) {
                            $query->expiringSoon()
                                ->where('expire_at', $expiration_date->format('Y-m-d'));
                        })
                        ->get();
                    if ($expiring_batches->count()) {
                        try {
                            $emails = $merchant->getNotifcationEmails();
                            $details = [
                                'date' => Carbon::now()->format('M d, Y'),
                                'app_key' => config('services.shopify.key'),
                                'period' => Arr::get($batch_expiration_period, 'period'),
                                'unit' => Arr::get($batch_expiration_period, 'unit'),
                                'merchant_subdomain' => $merchant->subdomain,
                                'merchant_name' => $merchant->user->name,
                                'expiring_batches' => $expiring_batches->toArray()
                            ];

                            foreach ($emails as $email) {
                                if ($email && strlen($email) > 0) {
                                    Mail::to($email)->send(new BatchExpiringSoonMail($details));
                                }
                            }

                            Log::info('Emailed batch expiring soon notification', ['email' => $emails]);
                        } catch (\Exception $e) {
                            Log::info('BatchesExpiringSoonEmailError', ['error' => $e]);
                        }
                    }
                }
            }
        }
    }
}
