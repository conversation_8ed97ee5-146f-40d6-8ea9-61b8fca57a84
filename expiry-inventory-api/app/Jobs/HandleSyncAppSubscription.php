<?php

namespace App\Jobs;

use App\Http\Controllers\PlanController;

use App\Models\Merchant;

;
use App\Http\Helpers\ShopifyAPIHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

use App\Enums\PlanEnum;
use App\Enums\SubscriptionStatus;
use Carbon\Carbon;
use Illuminate\Support\Arr;


class HandleSyncAppSubscription implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public Merchant $merchant;
    public $timeout = 120;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant)
    {

        $this->merchant = $merchant;

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $merchant = $this->merchant;
            Log::info('HandleSyncAppSubscription', ['merchant' => $merchant->subdomain]);
            $merchantSettings = $merchant->setting();

            // skip subscription check if merchant currently in trial pro plan
            if ($merchantSettings['plan'] == PlanEnum::TRIAL_PRO) {
                return;
            }

            $recurringCharge = ShopifyAPIHelper::withMerchant($merchant)->getCurrentAppSubscriptions();
            $recurringCharge = Arr::first($recurringCharge->currentAppInstallation->activeSubscriptions);
            if (!$recurringCharge) {
                // check subscription , if exist then cancel active subscription
                $merchant->subscriptions()->where('status', 'active')->update(['status' => SubscriptionStatus::CANCELLED, 'canceled_at' => Carbon::now()]);
                $merchantSettings['plan'] = PlanEnum::FREE;
                $merchant->saveSetting($merchantSettings);
                return;
            }

            // sync merchant status daily, get installation status and subscription status

            $subscription = (new PlanController)->updateSubscription($recurringCharge, $merchant);


        } catch (\Exception $e) {

            if ($e->getMessage() === 'Failed to get recurring charge') {
                Log::info('Failed to get recurring charge, this merchant has uninstall app', ['merchant' => $this->merchant->subdomain]);
                $this->merchant->update(['uninstalled_at' => Carbon::now()]);
            }

            Log::error($e);

        }
    }
}
