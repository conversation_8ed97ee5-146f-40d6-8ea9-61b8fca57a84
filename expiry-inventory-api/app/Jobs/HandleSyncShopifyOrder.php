<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\Merchant;
use App\Http\Controllers\OrderController;
use App\Exceptions\ExpiryInventoryException;
use App\Http\Helpers\ShopifyAPIHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HandleSyncShopifyOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $shopify_order;
    public Merchant $merchant;
    public $sync_action;

    public $timeout = 120;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shopify_order, Merchant $merchant, $type)
    {
        $this->shopify_order = $shopify_order;
        $this->merchant = $merchant;
        $this->sync_action = $type;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $sync_action = $this->sync_action;
        switch($sync_action)
        {
            case 'create':
            case 'update':
            case 'cancel':
            case 'delete':
                $sync_action .= 'Order';
                $this->{$sync_action}();
                break;
        }
    }

    private function createOrder()
    {
        if(!$this->checkLimit()) {
            return;
        }
        $this->createOrUpdateOrder();
    }

    private function updateOrder()
    {
        if(!$this->checkLimit()) {
            return;
        }
        $this->createOrUpdateOrder();
    }

    private function cancelOrder()
    {
        if(!$this->checkLimit()) {
            return;
        }

        $shopify_order = $this->shopify_order;
        $merchant = $this->merchant;

        $orderController = new OrderController;

        $customer = $orderController->getOrCreateCustomer($shopify_order->customer, $merchant);
        $order = $orderController->syncOrderDetailsFromShopify($shopify_order, $merchant, $customer);

        if($order) {
            foreach($order->items as $order_item) {
                // revert all item assignment
                $orderController->revertItemAssignment($order_item, $order_item->getTotalQuantityAssigned(), $merchant);
            }
            $order->checkAssigned();
        }
    }

    private function deleteOrder()
    {
        $shopify_order = $this->shopify_order;
        $merchant = $this->merchant;

        $order = Order::firstWhere([
            'shopify_order_id' => $shopify_order->id,
            'merchant_id'      => $merchant->id
        ]);
        if($order) {
            $order->delete();
        }
    }

    private function checkLimit()
    {
        try {
            $this->merchant->checkOrdersLimit(is_numeric($this->shopify_order) ? $this->shopify_order : $this->shopify_order->id);
            return true;
        } catch(ExpiryInventoryException $e) {
        }
        return false;
    }

    private function createOrUpdateOrder()
    {
        $merchant = $this->merchant;
        $api = ShopifyAPIHelper::withMerchant($merchant);

        // $this->shopify_order is Shopify Order ID
        $shopify_order = $api->getOrder($this->shopify_order, [
            'id',
            'order_number',
            'customer',
            'line_items',
            'current_total_price',
            'billing_address',
            'shipping_address',
            'note',
            'financial_status',
            'fulfillment_status',
            'fulfillments',
            'refunds'
        ]);
        $shopify_order = json_decode(json_encode($shopify_order), FALSE);

        $orderController = new OrderController;

        $customer = $orderController->getOrCreateCustomer($shopify_order->customer, $merchant);
        $order = $orderController->syncOrderDetailsFromShopify($shopify_order, $merchant, $customer);

        if($order) {
            $orderController->syncOrderItemsFromShopify(
                $order,
                $merchant,
                json_decode(json_encode($shopify_order->line_items), true),
                isset($shopify_order->fulfillments) ? json_decode(json_encode($shopify_order->fulfillments), true) : [],
                isset($shopify_order->refunds) ? json_decode(json_encode($shopify_order->refunds), true) : [],
            );
            $order->checkAssigned();
        }
    }
}
