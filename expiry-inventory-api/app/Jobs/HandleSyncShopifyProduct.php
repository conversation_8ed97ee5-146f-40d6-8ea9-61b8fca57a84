<?php

namespace App\Jobs;

use App\Enums\ProductStatus;
use App\Enums\ShopifyGraphQLEnum;
use App\Models\Location;
use App\Models\Product;
use App\Models\Merchant;
use App\Http\Helpers\ShopifyAPIHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class HandleSyncShopifyProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $params;
    public $shopify_product;
    public $variant_cursor;
    public Merchant $merchant;

    public $timeout = 120;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shopify_product, Merchant $merchant, $variant_cursor = null)
    {
        $this->shopify_product = $shopify_product;
        $this->merchant = $merchant;
        $this->variant_cursor = $variant_cursor;
        $this->params = func_get_args();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->handleGraphql();
    }

    public function handleGraphql()
    {
        $merchant = $this->merchant;
        $api = ShopifyAPIHelper::withMerchant($merchant);

        // $this->shopify_product is Shopify Product ID
        $shopify_product = $api->getProduct($this->shopify_product, $this->variant_cursor);
        if (isset($shopify_product->errors) && Arr::first($shopify_product->errors, fn($error) => $error->message == "Throttled")) {
            $this->dispatch(...$this->params)->onConnection($this->connection)->onQueue($this->queue)->delay(5);
            return;
        }

        if (isset($shopify_product) && isset($shopify_product->variants)) {
            foreach ($shopify_product->variants->nodes as $variant) {
                $product = Product::firstOrNew([
                    'merchant_id' => $merchant->id,
                    'shopify_variant_id' => extract_shopify_integer_id($variant->id),
                    'parent_id' => extract_shopify_integer_id($shopify_product->id)
                ]);
                $product->fill([
                    'name' => $variant->title == "Default Title" ? $shopify_product->title : $variant->title,
                    'parent_id' => extract_shopify_integer_id($shopify_product->id),
                    'parent_name' => $shopify_product->title,
                    'quantity' => array_sum(Arr::pluck($variant->inventoryItem->inventoryLevels->nodes, 'quantities.0.quantity')),
                    'sku' => $variant->sku,
                    'barcode' => $variant->barcode,
                    'price' => $variant->price,
                    'status' => $product->status === ProductStatus::DISCOUNTED ? ProductStatus::DISCOUNTED : Str::lower($shopify_product->status),
                    'is_excluded' => in_array('zesty_exclude', $shopify_product->tags),
                    'original_price' => $product->status != ProductStatus::DISCOUNTED ? $variant->price : ($product->original_price ?: $variant->price),
                    'compare_price' => $product->status != ProductStatus::DISCOUNTED ? $variant->compareAtPrice : ($product->compare_price ?: null),
                    'is_inventory_managed' => $variant->inventoryItem->tracked,
                    'image_url' => $variant->title == "Default Title" ? ($shopify_product->featuredImage ? $shopify_product->featuredImage->url : null) : ($variant->image ? $variant->image->url : null)
                ]);
                $product->save();

                if ($variant->inventoryItem->tracked) {
                    $this->syncProductLocationsInventory($product, $variant->inventoryItem);
                } else {
                    $product->locations()->detach();
                }
            }

            // check for any product removed and delete from DB if any

            $products = $merchant->products()->where('parent_id', extract_shopify_integer_id($shopify_product->id))->get();

            $ids = Arr::pluck($shopify_product->variants->nodes, 'id');
            $ids = array_map(function ($id) {
                return extract_shopify_integer_id($id);
            }, $ids);
            $del_products = $products->filter(function ($p) use ($ids) {
                return !in_array($p->shopify_variant_id, $ids);
            });
            foreach ($del_products as $del_product) {
                $del_product->status = ProductStatus::DELETED;
                $del_product->save();
                $del_product->delete();
            }

            if (isset($shopify_product->variantsCount) && $shopify_product->variantsCount->count > ShopifyGraphQLEnum::PRODUCT_VARIANT_PAGE_SIZE) {
                $has_next_page = $shopify_product->variants->pageInfo->hasNextPage;
                $endCursor = $shopify_product->variants->pageInfo->endCursor;
                HandleSyncShopifyProduct::dispatchIf($has_next_page && isset($endCursor), $this->shopify_product, $this->merchant, $endCursor);
            }
        }
    }

    private function syncProductLocationsInventory(Product $product, $inventoryItem)
    {
        $existing_product_locations = $product->locations;
        foreach ($inventoryItem->inventoryLevels->nodes as $inventory_level) {
            $shopify_location = $inventory_level->location;
            $shopify_location_id = isset($shopify_location->legacyResourceId) ? $shopify_location->legacyResourceId : extract_shopify_integer_id($shopify_location->id);
            $existing = $existing_product_locations->firstWhere('shopify_location_id', $shopify_location_id);
            if (!$existing) {
                $location = Location::firstOrCreate(
                    ['merchant_id' => $product->merchant_id, 'shopify_location_id' => $shopify_location_id],
                    ['name' => $shopify_location->name]
                );
                if (isset($inventory_level->quantities) && count($inventory_level->quantities)) {
                    if ($inventory_level->quantities[0]->quantity > -1) {
                        $product->locations()->attach($location->id, [
                            'on_hand' => $inventory_level->quantities[0]->quantity
                        ]);
                    }
                }
            }
        }

        foreach ($existing_product_locations as $location) {
            $remained = Arr::first($inventoryItem->inventoryLevels->nodes, function ($inventory_level) use ($location) {
                $shopify_location = $inventory_level->location;
                $shopify_location_id = isset($shopify_location->legacyResourceId) ? $shopify_location->legacyResourceId : extract_shopify_integer_id($shopify_location->id);
                return $location->shopify_location_id == $shopify_location_id;
            });
            if (!$remained || !count($remained->quantities) || $remained->quantities[0]->quantity < 0) {
                if ($product->locations()->whereKey($location->id)->exists()) {
                    $product->locations()->detach($location->id);
                }
            } else {
                $product->locations()->updateExistingPivot($location->id, [
                    'on_hand' => $remained->quantities[0]->quantity
                ]);
            }
        }
    }
}
