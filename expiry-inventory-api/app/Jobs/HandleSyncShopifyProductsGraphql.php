<?php

namespace App\Jobs;

use App\Http\Helpers\ShopifyAPIHelper;
use App\Models\Merchant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class HandleSyncShopifyProductsGraphql implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $params;
    public Merchant $merchant;
    public ?Merchant $nextMerchant;
    public $cursor;

    public $timeout = 600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant, $cursor = null, ?Merchant $nextMerchant = null)
    {
        $this->merchant = $merchant;
        $this->cursor = $cursor;
        $this->nextMerchant = $nextMerchant;
        $this->params = func_get_args();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('HandleSyncShopifyProductsGraphql', ['merchant' => $this->merchant->subdomain, 'endCursor' => $this->cursor]);

        try {

            $shopify_products_response = ShopifyAPIHelper::withMerchant($this->merchant)->getProductsIDs($this->cursor);
            if (isset($shopify_products_response->errors) && Arr::first($shopify_products_response->errors, fn($error) => $error->message == "Throttled")) {
                $this->dispatch(...$this->params)->onConnection($this->connection)->onQueue($this->queue)->delay(5);
                return;
            }

            $products_list = $shopify_products_response->products;
            $has_next_page = $products_list->pageInfo->hasNextPage;
            $endCursor = $products_list->pageInfo->endCursor;

            foreach ($products_list->nodes as $index => $_product) {
                HandleSyncShopifyProduct::dispatch(extract_shopify_integer_id($_product->id), $this->merchant)->delay(floor($index / 5));
            }

            HandleSyncShopifyProductsGraphql::dispatchIf($has_next_page, $this->merchant, $endCursor, $this->nextMerchant)->delay(10);

            // cursor for next merchant if SyncShopifyProductsGraphql is comleted for the current merchant
            if ($this->nextMerchant && !$has_next_page) {
                $nextMerchant = Merchant::where('id', '>', $this->nextMerchant->id)->orderBy('id', 'asc')->first();
                HandleSyncShopifyProductsGraphql::dispatchIf(!!$this->nextMerchant, $this->nextMerchant, null, $nextMerchant)->delay(10);
            }
        } catch (\Exception $e) {
            Log::error($e);
            Log::error("Exception when sync products for merchant {$this->merchant->subdomain} : {$this->cursor}");
        }
    }
}
