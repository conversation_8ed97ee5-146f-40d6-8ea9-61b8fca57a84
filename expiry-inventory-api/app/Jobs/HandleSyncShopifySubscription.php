<?php

namespace App\Jobs;

use App\Http\Controllers\PlanController;
use App\Models\Merchant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HandleSyncShopifySubscription implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $shopify_charge_id;
    public Merchant $merchant;

    public $timeout = 120;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shopify_charge_id, Merchant $merchant)
    {
        $this->shopify_charge_id = $shopify_charge_id;
        $this->merchant = $merchant;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $shopify_charge_id = $this->shopify_charge_id;
        $merchant = $this->merchant;

        (new PlanController)->syncShopifyPlan($shopify_charge_id, $merchant, false);
    }
}
