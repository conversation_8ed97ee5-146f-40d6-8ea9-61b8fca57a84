<?php

namespace App\Jobs;

use App\Http\Controllers\ShopifyWebhookController;
use App\Models\Merchant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class HandleSyncShopifyWebhooks implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Merchant $merchant;

    public $timeout = 600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant)
    {
        $this->merchant = $merchant;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('HandleSyncShopifyWebhooks', ['merchant' => $this->merchant->subdomain]);

        try {
            if(!$this->merchant->trashed()) {
                ShopifyWebhookController::setupWebhooks($this->merchant);
            }
        }
        catch(\Exception $e) {
            Log::error("Exception when sync shopify webhooks for merchant {$this->merchant->subdomain}");
        }
    }
}
