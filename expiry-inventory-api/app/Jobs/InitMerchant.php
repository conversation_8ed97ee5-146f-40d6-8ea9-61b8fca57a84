<?php

namespace App\Jobs;

use App\Http\Controllers\LocationController;
use App\Http\Controllers\ShopifyWebhookController;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Models\Merchant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InitMerchant implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Merchant $merchant;

    public $timeout = 3600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant)
    {
        $this->merchant = $merchant;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        ShopifyWebhookController::setupWebhooks($this->merchant);

        $merchant_setting = $this->merchant->setting();
        $show_expire_at = isset($merchant_setting['is_show_storefront_expire_at']) ? $merchant_setting['is_show_storefront_expire_at'] : false;
        ShopifyAPIHelper::withMerchant($this->merchant)->createOrUpdateShopMetafield(
            'show_expire_at',
            $show_expire_at,
            'boolean'
        );

        (new LocationController)->syncShopifyLocations($this->merchant);
        HandleSyncShopifyProductsGraphql::dispatch($this->merchant);
    }
}
