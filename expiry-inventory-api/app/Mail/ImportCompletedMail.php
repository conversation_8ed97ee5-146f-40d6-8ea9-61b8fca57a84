<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ImportCompletedMail extends Mailable
{
    use Queueable, SerializesModels;

    public $merchant_name;
    public $target;
    public $url;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($merchant_name, $target, $url)
    {
        $this->merchant_name = $merchant_name;
        $this->target = $target;
        $this->url = $url;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Import Completed Notification - ' . config('app.name'))
                    ->view('emails.import-completed');
    }
}
