<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class VerificationCodeMail extends Mailable
{
    use Queueable, SerializesModels;

    public $merchant_name;
    public $code;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($merchant_name, $code)
    {
        $this->merchant_name = $merchant_name;
        $this->code = $code;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Verification Code - ' . config('app.name'))
                    ->view('emails.verification-code');
    }
}
