<?php

namespace App\Models;

use App\Enums\BatchItemStatus;
use App\Models\Product;
use App\Models\Discount;
use App\Models\InventoryBatch;
use App\Models\OrderItemAssignment;
use App\Traits\CanDeleteWithoutEvent;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;


class BatchItem extends Model
{
    use CanDeleteWithoutEvent;
    use SoftDeletes;

    protected $table = 'inventory_batch_products';
    protected $fillable = [
        'inventory_batch_id',
        'product_id',
        'quantity',
        'expire_at',
        'status'
    ];
    public $timestamps = false;

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($batch_item) {
            if (!$batch_item->isExpired()) {
                if ($batch_item->quantity <= 0) {
                    $batch_item->status = BatchItemStatus::OUT_OF_STOCK;
                }
            } else {
                $batch_item->status = BatchItemStatus::EXPIRED;
            }
        });

        static::updating(function ($batch_item) {
            $batch_item->checkForLatestStatus();
        });
    }

    public function checkForLatestStatus()
    {
        if ($this->isExpired()) {
            $this->status = BatchItemStatus::EXPIRED;
        } else {
            if ($this->quantity > 0) {
                if ($this->status != BatchItemStatus::DISCOUNTED && $this->status != BatchItemStatus::EXPIRED) {
                    $this->status = BatchItemStatus::IN_STOCK;
                }
            } else {
                $this->status = BatchItemStatus::OUT_OF_STOCK;
            }
        }
    }

    public function product()
    {
        return $this->belongsTo(Product::class)
            ->withTrashed();
    }

    public function batch()
    {
        return $this->belongsTo(InventoryBatch::class, 'inventory_batch_id')->withTrashed();
    }

    public function assignments()
    {
        return $this->hasMany(OrderItemAssignment::class, 'batch_item_id');
    }

    public function order_items()
    {
        return $this->belongsToMany(OrderItem::class, 'order_products_assignments', 'batch_item_id', 'order_item_id')
            ->withPivot('quantity');
    }

    public function discounts(): HasMany
    {
        return $this->hasMany(Discount::class, 'batch_item_id');
    }

    public function dates(): HasMany
    {
        return $this->hasMany(BatchItemDate::class, 'batch_item_id');
    }

    public function isExpired()
    {
        return $this->attributes['expire_at'] ? (new Carbon($this->attributes['expire_at']) <= Carbon::now()->endOfDay()) : false;
    }

    public function scopeDateIsExpired($query, $date_offset = 0)
    {
        return $query->where('expire_at', '<=', Carbon::now()->addDays($date_offset)->endOfDay());
    }

    public function scopeStatusIn($query, $statuses)
    {
        return $query->whereIn('status', $statuses);
    }

    public function scopeInStock($query)
    {
        return $query->where('status', BatchItemStatus::IN_STOCK);
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('status', BatchItemStatus::OUT_OF_STOCK);
    }

    public function scopeDiscounted($query)
    {
        return $query->where('status', BatchItemStatus::DISCOUNTED);
    }

    public function scopeExpired($query)
    {
        return $query->where('status', BatchItemStatus::EXPIRED);
    }

    public function scopeExpiringSoon($query)
    {
        return $query->whereIn('status', [BatchItemStatus::IN_STOCK, BatchItemStatus::DISCOUNTED]);
    }

    public function scopeOrderNearestAuto($query, $selectColumns = [])
    {
        // Ensure the selectColumns array contains the required columns
        if (empty($selectColumns)) {
            $selectColumns = [
                'inventory_batch_products.id',
                'inventory_batch_products.inventory_batch_id',
                'inventory_batch_products.quantity',
                'inventory_batch_products.expire_at',
                'inventory_batch_product_dates.date'
            ];
        }

        return $query->leftJoin('inventory_batch_product_dates', 'inventory_batch_products.id', '=', 'inventory_batch_product_dates.batch_item_id')
            ->select($selectColumns)
            ->where(function ($q) {
                $q->orWhereDoesntHave('dates')
                    ->orWhereDate('inventory_batch_product_dates.date', '>', now()->format('Y-m-d'));
            })
            ->orderByRaw('CASE WHEN inventory_batch_product_dates.date IS NULL THEN 1 ELSE 0 END')
            ->orderBy('inventory_batch_product_dates.date', 'ASC')
            ->orderByRaw('CASE WHEN inventory_batch_products.expire_at IS NULL THEN 1 ELSE 0 END')
            ->orderBy('inventory_batch_products.expire_at', 'ASC');
    }

    public function scopeOrderManually($query, $manual_order_list)
    {
        $query2 = clone $query;
        $query3 = clone $query;

        // Adjust sortedRecords to match the column structure of remainingRecords
        $sortedRecords = clone $query2
            ->select(
                'inventory_batch_products.id',
                'inventory_batch_products.inventory_batch_id',
                'inventory_batch_products.quantity',
                'inventory_batch_products.expire_at',
                DB::raw('null as date'),
                DB::raw('1 as gp')
            )
            ->whereIn('inventory_batch_id', $manual_order_list);

        // Use scopeOrderNearestAuto for remainingRecords and ensure it has a similar structure
        $remainingRecords = $query3
            ->orderNearestAuto([
                'inventory_batch_products.id',
                'inventory_batch_products.inventory_batch_id',
                'inventory_batch_products.quantity',
                'inventory_batch_products.expire_at',
                'inventory_batch_product_dates.date',
                DB::raw('2 as gp')
            ])
            ->whereNotIn('inventory_batch_id', $manual_order_list);
        $q = $sortedRecords->union($remainingRecords)
            ->orderByRaw("gp, FIELD(inventory_batch_id, " . implode(',', $manual_order_list) . ")");

        return $q;
    }

    public function scopeFilterExpireDate($query, $type = 'from', $value = 30, $unit = 'days')
    {
        $today = Carbon::now();
        if ($type == 'from') {
            $until = $today->copy()->sub($unit, $value);
            return $query->whereBetween('expire_at', [$until->startOfDay(), $today->endOfDay()]);
        } else if ($type == 'to') {
            $until = $today->copy()->add($unit, $value);
            return $query->whereBetween('expire_at', [$today->startOfDay(), $until->endOfDay()]);
        }
        return $query;
    }

    public function scopeFilterBestBeforeDate($query, $type = 'from', $value = 30, $unit = 'days', $doOrdering = true)
    {
        $today = Carbon::now();
        if ($type == 'from') {
            $until = $today->copy()->sub($unit, $value);
            return $query->whereHas('dates', function ($q) use ($until, $today) {
                $q->whereBetween('date', [$until->startOfDay(), $today->endOfDay()]);
            })->when($doOrdering, fn ($q) => $q->orderNearestAuto());
        } else if ($type == 'to') {
            $until = $today->copy()->add($unit, $value);
            return $query->whereHas('dates', function ($q) use ($until, $today) {
                $q->whereBetween('date', [$today->startOfDay(), $until->endOfDay()]);
            })->when($doOrdering, fn ($q) => $q->orderNearestAuto());
        }
        return $query;
    }

    public function scopeIsSlowMoving($query, $period, $unit, $min_sales_qty)
    {
        return $query->receivedDateIsAfter($period, $unit)->whereHas('assignments', function ($q) use ($min_sales_qty) {
            $q->select(\DB::raw('SUM(order_products_assignments.quantity) as total_sales'))
                ->havingRaw('total_sales IS NULL OR total_sales < ' . $min_sales_qty);
        });
    }

    public function scopeReceivedDateIsAfter($query, $value = 30, $unit = 'days')
    {
        $today = Carbon::now();
        $until = $today->copy()->subtract($unit, $value);
        return $query->whereHas('batch', function ($q) use ($until) {
            $q->where('received_at', '<', $until->endOfDay());
        });
    }

    public function getItemsAtSimilarLocation()
    {
        $location_id = $this->batch->location_id;
        return self::expiringSoon()->whereHas('batch', function ($q) use ($location_id) {
            $q->where('inventory_batches.location_id', $location_id);
        });
    }
}
