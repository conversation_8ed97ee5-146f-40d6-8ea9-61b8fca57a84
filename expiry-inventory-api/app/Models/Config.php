<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Config extends Model
{
    public static function getConfigWithUuidAndKey($uuid, $key) {
        // Backward-compatible mapper: route uuid+key lookups to the new tables
        // default_* -> settings(key)
        // merchants_{id} -> merchant_configs(merchant_id, key)
        if (str_starts_with($uuid, 'default_')) {
            return self::getDefaultConfig($key);
        }
        if (str_starts_with($uuid, 'merchants_')) {
            $merchant_id = (int) str_replace('merchants_', '', $uuid);
            return self::getMerchantConfig($merchant_id, $key);
        }
        return null;
    }

    public static function getDefaultConfig($config_type) {
        $key = $config_type;

        $setting = Setting::query()->where('key', $key)->first();
        if (!$setting) {
            return null;
        }

        return $setting->value;
    }

    public static function getMerchantConfig($merchant_id, $config_type, $cache = true) {
        $key = $config_type;

        $config = MerchantConfig::query()
            ->where('merchant_id', $merchant_id)
            ->where('key', $key)
            ->first();
        if (!$config) {
            return null;
        }

        return $config->value;
    }

    public static function setMerchantConfig($merchant_id, $value, $config_type) {
        $key = $config_type;

        $config = MerchantConfig::updateOrCreate(
            ['merchant_id' => $merchant_id, 'key' => $key],
            ['value' => $value]
        );
    }

    public static function deleteConfig($id, $keys = []) {
        $query = MerchantConfig::where('merchant_id', $id);
        if (!empty($keys)) {
            $query->whereIn('key', $keys);
        }
        foreach ($query->get() as $config) {
            $config->delete();
        }
    }
}
