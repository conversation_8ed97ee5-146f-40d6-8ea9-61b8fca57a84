<?php

namespace App\Models;

use BaoPham\DynamoDb\DynamoDbModel;

class DDB extends DynamoDbModel
{
    public $incrementing = false;
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        $this->setTable(config('dynamodb.table'));
        $this->setPrefix();

        parent::__construct($attributes);
    }

    public function setPrefix()
    {
        if(!$this->table) {
            abort(500,'$table must be set at dynamodb model.');
        }

        $connection = config('dynamodb.default');

        $prefix = config("dynamodb.connections.{$connection}.prefix");

        if($prefix) {
            $this->table = $prefix . $this->table;
        }
    }
}
