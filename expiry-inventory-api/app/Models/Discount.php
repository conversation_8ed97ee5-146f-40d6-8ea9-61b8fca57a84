<?php

namespace App\Models;

use App\Enums\BatchItemStatus;
use App\Enums\ProductStatus;
use App\Http\Helpers\ShopifyAPIHelper;
use App\Models\BatchItem;
use App\Services\ShopifyService;
use Illuminate\Database\Eloquent\Model;

class Discount extends Model
{
    protected $fillable = [
        'batch_item_id',
        'product_id',
        'type',
        'discount_rate',
        'discounted_price',
        'discount_period',
        'discount_end_at',
        'track_period',
        'track_unit',
        'status',
    ];

    public function batch_item()
    {
        return $this->belongsTo(BatchItem::class, 'batch_item_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeWithType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function activate()
    {
        $batch_item = $this->batch_item;
        $product = $this->product;

        if ($product && $batch_item) {
            $merchant = $product->merchant;
            if ($merchant) {
                $this->status = 'active';
                $this->save();

                (new ShopifyService)
                    ->setMerchant($merchant)
                    ->setProduct($product)
                    ->setDiscount($this)
                    ->updateShopifyProductPrices();

                // set current product price & status to discounted
                $product->price = $this->discounted_price;
                $product->status = ProductStatus::DISCOUNTED;
                $product->save();

                // update batch item status and populate latest batch status
                $batch_item->status = BatchItemStatus::DISCOUNTED;
                $batch_item->save();

                $batch_item->batch->checkForLatestStatus();

                // revert all any existing discounted batch items status to in_stock
                foreach ($product->batch_items()->discounted()->get() as $revert_item) {
                    foreach ($revert_item->discounts()->where('id', '!=', $this->id)->where('status', '!=', 'inactive')->get() as $revert_discount) {
                        $revert_discount->deactivate();
                    }
                    if ($revert_item->id != $batch_item->id) {
                        $revert_item->status = BatchItemStatus::IN_STOCK;
                        $revert_item->save();
                    }
                }
            }
        }
    }

    public function deactivate()
    {
        $this->status = 'inactive';
        $this->save();
    }
}
