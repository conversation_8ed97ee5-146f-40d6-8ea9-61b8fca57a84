<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ImportHistory extends Model
{
    use SoftDeletes;
    
    protected $table = 'imports';

    public $guarded = [];

    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    public function inventory_histories()
    {
        return $this->hasMany(InventoryHistory::class);
    }
}
