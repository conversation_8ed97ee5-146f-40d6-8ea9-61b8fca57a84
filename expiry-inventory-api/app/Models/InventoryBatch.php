<?php

namespace App\Models;

use App\Models\Location;
use App\Models\BatchItem;
use App\Models\InventoryHistory;
use App\Models\OrderItemAssignment;
use App\Models\InventoryBatchVerification;
use App\Traits\CanDeleteWithoutEvent;
use App\Traits\HasDDBData;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InventoryBatch extends Model
{
    use CanDeleteWithoutEvent;
    use SoftDeletes;
    use HasDDBData {
        HasDDBData::_fill as fill;
    }

    protected $fillable = [
        'merchant_id',
        'location_id',
        'name',
        'received_at',
        'lot_number',
        'bin_location',
        'barcode',
        'invoice_number',
        'description',
        'status',
        'is_sync_quantity'
    ];

    protected $fill_values = ['meta'];

    protected $appends = ['meta'];

    public function fill(array $attributes)
    {
        return $this->_fill($attributes);
    }

    public function setMetaAttribute($value)
    {
        $this->saveModuleDetails($this->getTable(), 'meta', $value);
    }

    public function getMetaAttribute()
    {
        $meta = $this->getModuleDetails($this->getTable(), 'meta');
        return is_string($meta) ? json_decode($meta, true) : $meta;
    }

    public function merchant() {
        return $this->belongsTo(Merchant::class);
    }

    public function location() {
        return $this->belongsTo(Location::class);
    }

    public function items() {
        return $this->hasMany(BatchItem::class);
    }

    public function verifications() {
        return $this->hasMany(InventoryBatchVerification::class, 'inventory_batch_id');
    }

    public function inventory_histories() {
        return $this->hasMany(InventoryHistory::class, 'batch_id');
    }

    public function checkForLatestStatus() {
        $items_status = $this->items()->get()->pluck('status');
        if($items_status->contains('discounted')) {
            $this->status = 'discounted';
            $this->save();
        }
        else if($items_status->contains('in_stock')) {
            $this->status = 'in_stock';
            $this->save();
        }
        else if($items_status->contains('expired')) {
            $this->status = 'expired';
            $this->save();
        }
        else if($items_status->contains('out_of_stock')) {
            $this->status = 'out_of_stock';
            $this->save();
        }
    }

    public function hasAssignedToOrderItem() {
        $batch_item_ids = $this->items->pluck('id');
        foreach($batch_item_ids as $item_id) {
            if(OrderItemAssignment::where("batch_item_id", $item_id)->exists()) {
                return true;
            }
        }
        return false;
    }
}
