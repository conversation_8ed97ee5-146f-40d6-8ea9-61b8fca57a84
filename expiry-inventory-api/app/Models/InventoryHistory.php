<?php

namespace App\Models;

use App\Models\Order;
use App\Models\Merchant;
use App\Models\BatchItem;
use App\Models\InventoryBatch;
use App\Models\InventoryAction;

use Illuminate\Database\Eloquent\Model;

class InventoryHistory extends Model
{
    protected $fillable = [
        'merchant_id',
        'action_id',
        'batch_id',
        'import_id',
        'order_id',
        'adjustment',
        'result_quantity',
        'location_quantity',
        'adjusted_by',
    ];
    protected $with = [
        'batch',
        'order',
        'action'
    ];

    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    public function action()
    {
        return $this->belongsTo(InventoryAction::class);
    }

    public function batch()
    {
        return $this->belongsTo(InventoryBatch::class, 'batch_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public static function createWithBatchItem($create_payload, BatchItem $batch_item, $import_history_id = null)
    {
        $create_payload['result_quantity'] = $batch_item->quantity;
        $create_payload['import_id'] = $import_history_id;
        // get total quantity of product in batch in the same location
        $create_payload['location_quantity'] = $batch_item->getItemsAtSimilarLocation()->sum('quantity');
        self::create($create_payload);
    }
}
