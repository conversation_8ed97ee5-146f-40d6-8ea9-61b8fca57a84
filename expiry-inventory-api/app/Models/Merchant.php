<?php

namespace App\Models;

use App\Enums\PlanEnum;
use App\Exceptions\ExpiryInventoryException;
use App\Models\User;
use App\Models\Order;
use App\Models\Config;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Location;
use App\Models\Subscription;
use App\Models\InventoryBatch;
use App\Models\InventoryHistory;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class Merchant extends Authenticatable
{
    use SoftDeletes;
    const DELETED_AT = 'uninstalled_at';

    protected $fillable = [
        'user_id',
        'subdomain',
        'access_token',
        'uninstalled_at'
    ];

    protected $hidden = [
        'password'
    ];

    public function user() {
        return $this->belongsTo(User::class);
    }

    public function locations() {
        return $this->hasMany(Location::class);
    }

    public function customers() {
        return $this->hasMany(Customer::class);
    }

    public function products() {
        return $this->hasMany(Product::class)
            ->withTrashed();
    }

    public function discounts(): HasManyThrough
    {
        return $this->hasManyThrough(Discount::class, Product::class);
    }

    public function inventory_batches() {
        return $this->hasMany(InventoryBatch::class);
    }

    public function inventory_histories() {
        return $this->hasMany(InventoryHistory::class);
    }

    public function import_histories() {
        return $this->hasMany(ImportHistory::class);
    }

    public function export_histories() {
        return $this->hasMany(ExportHistory::class);
    }

    public function orders() {
        return $this->hasMany(Order::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function data_requests()
    {
        return $this->hasMany(DataRequest::class);
    }

    public function verifications()
    {
        return $this->hasMany(UserVerify::class);
    }

    public function checkBatchLimit($items) {
        $free_plan_setting = Config::getDefaultConfig('plan_settings')[PlanEnum::FREE];
        $grouped_product_ids = $this->products()->has('batch_items', '>', 0)->get()->unique('parent_id')->pluck('parent_id');
        foreach($items as $item) {
            $product = $this->products()->find($item['product_id']);
            if($grouped_product_ids->search($product->parent_id) === false) {
                if($grouped_product_ids->count() + 1 >= $free_plan_setting['batch_products_limit']) {
                    throw new ExpiryInventoryException('Exceeded batch products limit', 'batch_limit_exceeded');
                }
                else {
                    $grouped_product_ids->push($product->parent_id);
                }
            }
        }
    }

    public function checkOrdersLimit($shopify_order_id) {
        $merchantSettings = $this->setting();
        if($merchantSettings['plan'] != PlanEnum::FREE) {
            return true;
        }
        if($this->orders()->where('shopify_order_id', $shopify_order_id)->exists()) {
            return true;
        }
        $free_plan_setting = Config::getDefaultConfig('plan_settings')[PlanEnum::FREE];
        if($this->orders()->whereBetween('created_at', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])->count() >= $free_plan_setting['orders_limit_per_month']) {
            throw new ExpiryInventoryException('Exceeded monthly orders limit', 'order_limit_exceeded');
        }
    }

    public function initSetting() {
        $setting = $this->setting('configs');
        if(!$setting) {
            $this->saveSetting(Config::getDefaultConfig('configs'));
        }

        $print_setting = $this->setting('print_settings');
        if(!$print_setting) {
            $this->saveSetting(Config::getDefaultConfig('print_settings'), 'print_settings');
        }
    }

    public function setting($type = 'configs', $cache = true) {
        $setting = Config::getMerchantConfig($this->id, $type, $cache);
        if(!$setting) {
            $setting = Config::getDefaultConfig($type);
            $this->saveSetting($setting, $type);
        }
        return $setting;
    }

    public function getNotifcationEmails()
    {
        $setting = $this->setting();
        $email = Arr::get($setting, 'notification_email');
        if ($email && is_string($email) && strlen($email) > 0) {
            if (!Str::contains($email, ';')) {
                return [$email];
            }
            // semicolon delimited string
            $email = array_values(array_map(function ($e) {
                return trim($e);
            }, explode(';', $email)));
            $email = array_filter($email, function ($e) {
                return is_string($e) && strlen($e) > 0;
            });
            if (count($email) > 0) {
                return $email;
            }
        }
        return [$this->getShopEmail()];
    }

    public function getShopEmail()
    {
        $setting = $this->setting();
        return Arr::get($setting, 'shop.email', $this->user->email);
    }

    public function getNewVerificationCode($email = null)
    {
        $existing_verifications = $this->verifications()->expired(false)->used(false)->get();
        if ($existing_verifications->count()) {
            // expires all existing verifications
            $this->verifications()->whereIn('id', $existing_verifications->pluck('id'))->update(['expire_at' => now()]);
        }
        $new_verification = $this->verifications()->create([
            'email' => $email ?: $this->getShopEmail()
        ]);
        return $new_verification->token;
    }

    public function isPasswordSetupRequired()
    {
        return is_null($this->password);
    }

    public function checkPassword($password)
    {
        return Hash::check($password, $this->password);
    }

    public function saveSetting($setting, $type = 'configs') {
        Config::setMerchantConfig($this->id, $setting, $type);
    }
}
