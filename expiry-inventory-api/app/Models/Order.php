<?php

namespace App\Models;

use App\Models\Customer;
use App\Models\OrderItem;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use SoftDeletes;

    protected $casts = [
        'billing_address' => 'json',
        'shipping_address' => 'json'
    ];

    protected $fillable = [
        'merchant_id',
        'shopify_order_id',
        'customer_id',
        'order_number',
        'amount',
        'billing_address',
        'shipping_address',
        'note',
        'status',
        'fulfillment_status',
        'batch_assign_status'
    ];

    protected static function boot() {
        parent::boot();
        static::deleting(function($order) {
            foreach($order->items as $item) {
                $item->delete();
            }
        });
    }

    public function customer() {
        return $this->belongsTo(Customer::class);
    }

    public function items() {
        return $this->hasMany(OrderItem::class);
    }

    public function checkAssigned() {
        $fully_assigned = true;
        if(!$this->items()->exists()) {
            $fully_assigned = false;
        }
        else {
            foreach($this->items()->get() as $order_item) {
                if($order_item->getTotalQuantityAssigned() < $order_item->quantity) {
                    $fully_assigned = false;
                    break;
                }
            }
        }
        $this->batch_assign_status = $fully_assigned ? 'assigned' : 'unassigned';
        $this->save();
    }
}
