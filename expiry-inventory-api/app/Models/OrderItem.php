<?php

namespace App\Models;

use App\Models\Order;
use App\Models\Product;
use App\Models\InventoryAction;
use App\Models\InventoryHistory;
use App\Models\OrderItemAssignment;

use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    protected $table = 'order_products';
    protected $fillable = [
        'order_id',
        'product_id',
        'line_item_id',
        'name',
        'quantity',
        'price',
    ];
    public $timestamps = false;

    protected static function boot() {
        parent::boot();
        static::deleting(function($order_item) {
            foreach($order_item->batch_items as $batch_item_assigned) {
                $batch_item_assigned->quantity += $batch_item_assigned->pivot->quantity;
                $batch_item_assigned->save();

                $batch_updated_action = InventoryAction::getBySlug('batch-updated');
                InventoryHistory::createWithBatchItem([
                    'merchant_id' => $batch_item_assigned->batch->merchant_id,
                    'action_id'   => $batch_updated_action->id,
                    'batch_id'    => $batch_item_assigned->inventory_batch_id,
                    'adjustment'  => $batch_item_assigned->pivot->quantity
                ], $batch_item_assigned);
            }
            $order_item->batch_items()->detach();
        });
    }

    public function product() {
        return $this->belongsTo(Product::class)
            ->withTrashed();
    }

    public function order() {
        return $this->belongsTo(Order::class);
    }

    public function assignments() {
        return $this->hasMany(OrderItemAssignment::class, 'order_item_id');
    }

    public function batch_items() {
        return $this->belongsToMany(BatchItem::class, 'order_products_assignments', 'order_item_id', 'batch_item_id')
            ->withPivot(['id', 'quantity'])
            ->withTrashed();
    }

    public function getTotalQuantityAssigned() {
        return $this->product ? $this->batch_items->sum('pivot.quantity') : $this->quantity;
    }
}
