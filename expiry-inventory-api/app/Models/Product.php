<?php

namespace App\Models;

use App\Enums\BatchItemStatus;
use App\Http\Helpers\DiscountHelper;

use App\Models\Merchant;
use App\Models\Discount;
use App\Models\BatchItem;
use App\Models\OrderItem;
use App\Traits\HasDDBData;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends Model
{
    use HasDDBData {
        HasDDBData::_fill as fill;
    }
    use SoftDeletes;

    protected $fillable = [
        'merchant_id',
        'shopify_variant_id',
        'name',
        'parent_id',
        'parent_name',
        'sku',
        'barcode',
        'quantity',
        'is_inventory_managed',
        'image_url',
        'price',
        'original_price',
        'compare_price',
        'status',
        'is_excluded'
    ];

    protected $appends = [
        'expired_quantity',
        'available_quantity',
        'display_name',
        'nearest_expire_date'
    ];

    protected $fill_values = ['priceLists'];

    public function fill(array $attributes)
    {
        return $this->_fill($attributes);
    }

    public function setPriceListsAttribute($value)
    {
        $this->saveModuleDetails($this->getTable(), 'priceLists', $value);
    }

    public function getPriceListsAttribute()
    {
        $priceLists = $this->getModuleDetails($this->getTable(), 'priceLists');
        return is_string($priceLists) ? json_decode($priceLists, true) : $priceLists;
    }

    public function deletePriceLists()
    {
        $uuid = $this->getTable() . '_' . $this->id;
        $this->deleteModuleDetails($uuid, ['priceLists']);
    }

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($product) {
            DiscountHelper::applyDiscountForProducts(collect([$product]));
        });
    }

    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    public function locations()
    {
        return $this->belongsToMany(Location::class, 'product_location')
            ->withPivot('on_hand');
    }

    public function batch_items()
    {
        return $this->hasMany(BatchItem::class);
    }

    public function order_items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function discounts(): HasMany
    {
        return $this->hasMany(Discount::class);
    }

    public function getExpiredQuantityAttribute()
    {
        return $this->batch_items()->expired()->sum('quantity');
    }

    public function getAvailableQuantityAttribute()
    {
        return $this->batch_items()->where('status', '!=', BatchItemStatus::EXPIRED)->sum('quantity');
    }

    public function getDisplayNameAttribute()
    {
        return $this->attributes['parent_name'] . ($this->attributes['name'] != $this->attributes['parent_name'] ? ' (' . $this->attributes['name'] . ')' : '');
    }

    public function getNearestExpireDateAttribute()
    {
        $items = $this->batch_items;
        if ($items && $items->count() > 0) {
            $expiring_items = $items->whereIn('status', [BatchItemStatus::IN_STOCK, BatchItemStatus::DISCOUNTED]);
            if ($expiring_items->count() > 0) {
                return $expiring_items->min('expire_at');
            }
            $other_items = $items->whereNotIn('status', [BatchItemStatus::IN_STOCK, BatchItemStatus::DISCOUNTED])->whereNotNull('expire_at');
            return $other_items->max('expire_at') ?: null;
        }
        return null;
    }

    public function scopeDiscounted($query)
    {
        return $query->where('status', 'discounted');
    }
}
