<?php

namespace App\Models;

use App\Models\Merchant;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    protected $fillable = [
        'charge_id',
        'type',
        'billing_period',
        'test',
        'status',
        'bill_ends_at',
        'canceled_at',
    ];

    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }
}
