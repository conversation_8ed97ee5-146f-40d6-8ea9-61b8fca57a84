<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Transfer extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'merchant_id',
        'source_batch_id',
        'destination_batch_id',
        'product_id',
        'quantity',
        'source_quantity_adjustment',
        'destination_quantity_adjustment',
        'is_draft',
        'status',
        'notes',
        'executed_at',
    ];

    protected $casts = [
        'source_quantity_adjustment' => 'boolean',
        'destination_quantity_adjustment' => 'boolean',
        'is_draft' => 'boolean',
        'executed_at' => 'datetime',
    ];

    protected $with = [
        'sourceBatch',
        'destinationBatch',
        'product',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the merchant that owns the transfer.
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    /**
     * Get the source batch for the transfer.
     */
    public function sourceBatch()
    {
        return $this->belongsTo(InventoryBatch::class, 'source_batch_id');
    }

    /**
     * Get the destination batch for the transfer.
     */
    public function destinationBatch()
    {
        return $this->belongsTo(InventoryBatch::class, 'destination_batch_id');
    }

    /**
     * Get the product being transferred.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Scope a query to only include draft transfers.
     */
    public function scopeDrafts($query)
    {
        return $query->where('is_draft', true);
    }

    /**
     * Scope a query to only include non-draft transfers.
     */
    public function scopeExecuted($query)
    {
        return $query->where('is_draft', false);
    }

    /**
     * Scope a query to only include transfers with a specific status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include pending transfers.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope a query to only include completed transfers.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Mark the transfer as completed.
     */
    public function markAsCompleted()
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'executed_at' => now(),
            'is_draft' => false,
        ]);
    }

    /**
     * Mark the transfer as cancelled.
     */
    public function markAsCancelled()
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
        ]);
    }

    /**
     * Check if the transfer is a draft.
     */
    public function isDraft()
    {
        return $this->is_draft;
    }

    /**
     * Check if the transfer is completed.
     */
    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if the transfer is pending.
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }
}
