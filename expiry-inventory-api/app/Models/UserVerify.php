<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserVerify extends Model
{
    protected $table = 'user_verify';

    public $guarded = [];

    public $timestamps = false;

    public static $request_timeout_second = 30;
    public static $code_expiration_second = 1800;

    protected $fillable = [
        'merchant_id',
        'email',
        'token',
        'used_at',
        'expire_at',
        'created_at'
    ];

    public static function boot()
    {
        parent::boot();
        static::creating(function (UserVerify $user_verify) {
            if (!$user_verify->token) {
                $user_verify->token = generate_random_numeric_string(6);
            }
            if (!$user_verify->expire_at) {
                $user_verify->expire_at = now()->addSeconds(static::$code_expiration_second);
            }
            if (!$user_verify->created_at) {
                $user_verify->created_at = now();
            }
        });
    }

    public function scopeExpired($query, $is_expired = true)
    {
        return $query->where('expire_at', $is_expired ? '<=' : '>', now());
    }

    public function scopeUsed($query, $is_used = true)
    {
        if ($is_used) {
            return $query->whereNotNull('used_at');
        }
        return $query->whereNull('used_at');
    }

    public function scopeRecentlyRequested($query)
    {
        return $query->where('created_at', '>=', now()->subSeconds($this->request_timeout_second));
    }
}
