<?php

namespace App\Observers;

use App\Enums\BatchItemStatus;
use App\Jobs\ApplyDiscount;
use App\Http\Helpers\DiscountHelper;
use App\Http\Helpers\StorefrontExpiryDateHelper;
use App\Models\BatchItem;

class BatchItemObserver
{
    /**
     * Handle the BatchItem "created" event.
     *
     * @param  \App\Models\BatchItem  $batch_item
     * @return void
     */
    public function created(BatchItem $batch_item)
    {
        // apply discount
        // DiscountHelper::applyDiscountForProducts(collect([$batch_item->product]));
        ApplyDiscount::dispatch(collect([$batch_item->product]));

        StorefrontExpiryDateHelper::updateStorefrontExpiryDate(collect([$batch_item->product]));

        $batch_item->batch->checkForLatestStatus();
    }

    /**
     * Handle the BatchItem "updated" event.
     *
     * @param  \App\Models\BatchItem  $batch_item
     * @return void
     */
    public function updated(BatchItem $batch_item)
    {
        if (($batch_item->wasChanged('status') && $batch_item->status != BatchItemStatus::DISCOUNTED) || $batch_item->wasChanged('expire_at')) {
            $product = $batch_item->product;

            // DiscountHelper::applyDiscountForProducts(collect([$product]));
            ApplyDiscount::dispatch(collect([$product]));

            StorefrontExpiryDateHelper::updateStorefrontExpiryDate(collect([$product]));
        }
        else if ($batch_item->wasChanged('quantity')) {
            // dispatch update metafields also if quantity was changed
            StorefrontExpiryDateHelper::updateStorefrontExpiryDate(collect([$batch_item->product]));
        }
        $batch_item->batch->checkForLatestStatus();
    }

    /**
     * Handle the BatchItem "deleted" event.
     *
     * @param  \App\Models\BatchItem  $batch_item
     * @return void
     */
    public function deleted(BatchItem $batch_item)
    {
        // DiscountHelper::applyDiscountForProducts(collect([$batch_item->product]));
        ApplyDiscount::dispatch(collect([$batch_item->product]));

        StorefrontExpiryDateHelper::updateStorefrontExpiryDate(collect([$batch_item->product]));
        
        foreach ($batch_item->dates as $date) {
            $date->delete();
        }
    }
}
