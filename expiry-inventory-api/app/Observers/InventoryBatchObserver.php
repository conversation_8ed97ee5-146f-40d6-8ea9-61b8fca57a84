<?php

namespace App\Observers;

use App\Enums\InventoryBatchStatus;
use App\Jobs\HandleItemOutOfStock;
use App\Models\InventoryBatch;

class InventoryBatchObserver
{
    /**
     * Handle the InventoryBatch "created" event.
     *
     * @param  \App\Models\InventoryBatch  $inventory_batch
     * @return void
     */
    public function created(InventoryBatch $inventory_batch)
    {
        //
    }

    /**
     * Handle the InventoryBatch "updated" event.
     *
     * @param  \App\Models\InventoryBatch  $inventory_batch
     * @return void
     */
    public function updated(InventoryBatch $inventory_batch)
    {
        if($inventory_batch->wasChanged('status') && $inventory_batch->status == InventoryBatchStatus::OUT_OF_STOCK) {
            HandleItemOutOfStock::dispatch($inventory_batch);
        }
    }

    /**
     * Handle the InventoryBatch "deleted" event.
     *
     * @param  \App\Models\InventoryBatch  $inventory_batch
     * @return void
     */
    public function deleted(InventoryBatch $inventory_batch)
    {
        foreach ($inventory_batch->items as $batch_item) {
            foreach ($batch_item->dates as $date) {
                $date->delete();
            }
            $batch_item->delete();
        }
        foreach($inventory_batch->inventory_histories as $history) {
            $history->delete();
        }
    }

    /**
     * Handle the InventoryBatch "restored" event.
     *
     * @param  \App\Models\InventoryBatch  $inventory_batch
     * @return void
     */
    public function restored(InventoryBatch $inventory_batch)
    {
        //
    }

    /**
     * Handle the InventoryBatch "forceDeleted" event.
     *
     * @param  \App\Models\InventoryBatch $inventory_batch
     * @return void
     */
    public function forceDeleted(InventoryBatch $inventory_batch)
    {
        foreach ($inventory_batch->items as $batch_item) {
            foreach ($batch_item->dates as $date) {
                $date->delete();
            }
            $batch_item->forceDelete();
        }
        foreach($inventory_batch->inventory_histories as $history) {
            $history->forceDelete();
        }
    }
}