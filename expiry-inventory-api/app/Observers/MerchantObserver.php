<?php

namespace App\Observers;

use App\Enums\PlanEnum;
use App\Enums\SubscriptionStatus;
use App\Models\Merchant;

class MerchantObserver
{
    /**
     * Handle the Merchant "created" event.
     *
     * @param  \App\Models\Merchant  $merchant
     * @return void
     */
    public function created(Merchant $merchant)
    {
        //
    }

    /**
     * Handle the Merchant "updated" event.
     *
     * @param  \App\Models\Merchant  $merchant
     * @return void
     */
    public function updated(Merchant $merchant)
    {
        //
    }

    /**
     * Handle the Merchant "deleted" event.
     *
     * @param  \App\Models\Merchant  $merchant
     * @return void
     */
    public function deleted(Merchant $merchant)
    {
        $current_subscription = $merchant->subscriptions()->where('status', SubscriptionStatus::ACTIVE)->first();
        if($current_subscription) {
            $current_subscription->fill([
                'status' => SubscriptionStatus::CANCELLED
            ])->save();
        }
        $merchantSettings = $merchant->setting();
        $merchantSettings['plan'] = PlanEnum::FREE;
        $merchant->saveSetting($merchantSettings);
    }

    /**
     * Handle the Merchant "restored" event.
     *
     * @param  \App\Models\Merchant  $merchant
     * @return void
     */
    public function restored(Merchant $merchant)
    {
        //
    }

    /**
     * Handle the Merchant "forceDeleted" event.
     *
     * @param  \App\Models\Merchant $merchant
     * @return void
     */
    public function forceDeleted(Merchant $merchant)
    {
        $merchant->locations()->forceDelete();
        $merchant->inventory_batches()->forceDelete();
        $merchant->subscriptions()->delete();
        $merchant->orders()->forceDelete();
        $merchant->customers()->forceDelete();
        $merchant->products()->forceDelete();
        $merchant->import_histories()->delete();
        $merchant->export_histories()->delete();
        $merchant->data_requests()->delete();

        $merchant->user->delete();
    }
}
