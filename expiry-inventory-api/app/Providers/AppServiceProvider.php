<?php

namespace App\Providers;

use App\Models\Product;
use App\Models\Merchant;
use App\Models\BatchItem;
use App\Models\InventoryBatch;
use App\Models\Order;
use App\Observers\MerchantObserver;
use App\Observers\BatchItemObserver;
use App\Observers\InventoryBatchObserver;
use App\Observers\OrderObserver;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Relations\Relation;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Relation::morphMap([
            'Product' => Product::class
        ]);
        BatchItem::observe(BatchItemObserver::class);
        InventoryBatch::observe(InventoryBatchObserver::class);
        Merchant::observe(MerchantObserver::class);
        Order::observe(OrderObserver::class);
    }
}
