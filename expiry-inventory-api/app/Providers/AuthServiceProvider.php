<?php

namespace App\Providers;

use App\Models\Merchant;

use Firebase\JWT\JWT;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Auth::viaRequest('shopify-jwt', function (Request $request) {
            $session_token = $request->header('Authorization');

            if(!isset($session_token)) {
                abort(401, 'Unauthorized.');
            }

            $session_token = Str::replace('Bearer ', '', $session_token); // remove "Bearer "

            $token_arr = JWT::decode($session_token, config('services.shopify.secret'), array('HS256'));

            if(!isset($token_arr->aud)) {
                abort(401, 'Unauthorized.');
            }

            if($token_arr->aud != config('services.shopify.key')) {
                abort(401, 'Unauthorized.');
            }

            $subdomain = Str::replace('https://', '', $token_arr->dest); // remove "https://"

            return Merchant::where('subdomain', $subdomain)->firstOrFail();
        });
        Auth::viaRequest('shopify-hmac', function (Request $request) {
            $shopify_domain = $request->header('X-Shopify-Shop-Domain');
            $shopify_hmac = $request->header('X-Shopify-Hmac-Sha256');
            $data = file_get_contents('php://input');

            if(!$shopify_domain || !$shopify_hmac) {
                abort(401, 'Unauthorized.');
            }

            $calcHash = base64_encode(hash_hmac('sha256', $data, config('services.shopify.secret'), true));

            if(!hash_equals($shopify_hmac, $calcHash)) {
                abort(401, 'Unauthorized.');
            }

            return Merchant::withTrashed()->where('subdomain', $shopify_domain)->firstOrFail();
        });
        Auth::viaRequest('shopify-shop-url', function (Request $request) {
            $shopify_domain = $request->header('X-Shop-URL');
            return Merchant::where('subdomain', $shopify_domain)->firstOrFail();
        });
    }
}
