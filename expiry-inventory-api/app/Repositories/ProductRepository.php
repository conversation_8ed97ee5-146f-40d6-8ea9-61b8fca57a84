<?php

namespace App\Repositories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ProductRepository extends BaseRepository
{
    public function model()
    {
        return Product::class;
    }

    /**
     * @return Collection<Product>
     */
    public function getAllParentProductsWithBatches(array $filters): Collection
    {
        return $this->has('batch_items')
            ->when(isset($filters['merchant_id']), function (Builder $query) use ($filters) {
                $query->where('merchant_id', $filters['merchant_id']);
            })
            ->distinct('parent_id')
            ->get();
    }
}