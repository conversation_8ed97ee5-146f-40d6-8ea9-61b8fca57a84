<?php

namespace App\Services;

use App\Enums\AnalyticsExpiryTypeEnum;
use App\Enums\ProductStatus;
use App\Repositories\ProductRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class AnalyticsService
{
    protected ProductRepository $productRepository;

    public function __construct(?ProductRepository $productRepository)
    {
        $this->productRepository = $productRepository ?: app(ProductRepository::class);
    }

    /**
     * @return Collection{date: string, quantity: int | null, cost: float | null}
     */
    public function getExpiryAnalyticsDates(array $filters = []): Collection
    {
        $type = Arr::get($filters, 'type', AnalyticsExpiryTypeEnum::EXPIRED);
        $days = Arr::get($filters, 'days', 30);

        $products = $this->productRepository->with([
                'batch_items' => function ($query) use ($type, $days) {
                    if ($type == AnalyticsExpiryTypeEnum::EXPIRED) {
                        $query->expired()->filterExpireDate('from', $days);
                    } else if ($type == AnalyticsExpiryTypeEnum::EXPIRING_SOON) {
                        $query->expiringSoon()->filterExpireDate('to', $days);
                    } else if ($type == AnalyticsExpiryTypeEnum::BEST_BEFORE) {
                        $query->expiringSoon()->filterBestBeforeDate('from', $days, 'days', false);
                    } else if ($type == AnalyticsExpiryTypeEnum::UPCOMING_BEST_BEFORE) {
                        $query->expiringSoon()->filterBestBeforeDate('to', $days, 'days', false);
                    }
                },
                'batch_items.dates',
                'batch_items.product'
            ])
            ->whereHas('batch_items', function ($query) use ($type, $days) {
                if ($type == AnalyticsExpiryTypeEnum::EXPIRED) {
                    $query->expired()->filterExpireDate('from', $days);
                } else if ($type == AnalyticsExpiryTypeEnum::EXPIRING_SOON) {
                    $query->expiringSoon()->filterExpireDate('to', $days);
                } else if ($type == AnalyticsExpiryTypeEnum::BEST_BEFORE) {
                    $query->expiringSoon()->filterBestBeforeDate('from', $days, 'days', false);
                } else if ($type == AnalyticsExpiryTypeEnum::UPCOMING_BEST_BEFORE) {
                    $query->expiringSoon()->filterBestBeforeDate('to', $days, 'days', false);
                }
            })
            ->whereNotIn('products.status', [ProductStatus::DRAFT, ProductStatus::ARCHIVED, ProductStatus::DELETED])
            ->where('merchant_id', $filters['merchant_id'])
            ->get();

        return $products->flatMap(function ($product) {
            return $product->batch_items->map(function ($item) {
                $item->dates = $item->dates->map(fn ($d) => $d->date)->toArray();
                return $item;
            });
        })->groupBy(in_array($type, [AnalyticsExpiryTypeEnum::BEST_BEFORE, AnalyticsExpiryTypeEnum::UPCOMING_BEST_BEFORE]) ? 'dates' : 'expire_at')->map(function ($item, $key) {
            return [
                'date' => $key,
                'quantity' => $item->sum('quantity'),
                'cost' => $item->sum(function ($t) {
                    return $t->product->original_price * $t->quantity;
                })
            ];
        });
    }

    /**
     * @return array{data: array{}, last_page: int}
     */
    public function getAnalyticsProducts(array $filters = []): array
    {
        $type = Arr::get($filters, 'type', AnalyticsExpiryTypeEnum::EXPIRED);
        $days = Arr::get($filters, 'days', 30);
        $limit = Arr::get($filters, 'limit', 25);

        $paginated_products = $this->productRepository->with([
                'batch_items.dates',
                'batch_items' => function ($query) use ($type, $days) {
                    if ($type == AnalyticsExpiryTypeEnum::EXPIRED) {
                        $query->expired()->filterExpireDate('from', $days);
                    } else if ($type == AnalyticsExpiryTypeEnum::EXPIRING_SOON) {
                        $query->expiringSoon()->filterExpireDate('to', $days);
                    } else if ($type == AnalyticsExpiryTypeEnum::BEST_BEFORE) {
                        $query->expiringSoon()->filterBestBeforeDate('from', $days, 'days', false);
                    } else if ($type == AnalyticsExpiryTypeEnum::UPCOMING_BEST_BEFORE) {
                        $query->expiringSoon()->filterBestBeforeDate('to', $days, 'days', false);
                    } else if ($type == AnalyticsExpiryTypeEnum::DISCOUNTED) {
                        $query->discounted();
                    }
                },
                'discounts' => function ($query) {
                    $query->active();
                }
            ])
            ->whereHas('batch_items', function ($query) use ($type, $days) {
                if ($type == AnalyticsExpiryTypeEnum::EXPIRED) {
                    $query->expired()->filterExpireDate('from', $days);
                } else if ($type == AnalyticsExpiryTypeEnum::EXPIRING_SOON) {
                    $query->expiringSoon()->filterExpireDate('to', $days);
                } else if ($type == AnalyticsExpiryTypeEnum::BEST_BEFORE) {
                    $query->expiringSoon()->filterBestBeforeDate('from', $days, 'days', false);
                } else if ($type == AnalyticsExpiryTypeEnum::UPCOMING_BEST_BEFORE) {
                    $query->expiringSoon()->filterBestBeforeDate('to', $days, 'days', false);
                } else if ($type == AnalyticsExpiryTypeEnum::DISCOUNTED) {
                    $query->discounted();
                }
            })
            ->where('merchant_id', $filters['merchant_id'])
            ->whereNotIn('products.status', [ProductStatus::DRAFT, ProductStatus::ARCHIVED, ProductStatus::DELETED])
            ->paginate($limit);

        $products = array_map(function ($product) {
            return [
                'id' => $product['id'],
                'shopify_variant_id' => $product['shopify_variant_id'],
                'parent_id' => $product['parent_id'],
                'name' => $product['display_name'],
                'sku' => $product['sku'],
                'price' => $product['price'],
                'original_price' => $product['original_price'],
                'quantity' => collect($product['batch_items'])->sum('quantity'),
                'nearest_expire_date' => $product['nearest_expire_date'],
                'best_before_dates' => array_unique(Arr::pluck(Arr::collapse(Arr::pluck(Arr::get($product, 'batch_items'), 'dates')), 'date')),
                'discount' => ($product['discounts'] && count($product['discounts']) > 0) ? $product['discounts'][0] : null
            ];
        }, $paginated_products->items());

        $result = [
            'data' => $products,
            'last_page' => $paginated_products->lastPage()
        ];

        return $result;
    }
}