<?php

namespace App\Services;

use App\Models\InventoryBatch;
use App\Repositories\InventoryBatchRepository;

class InventoryBatchService
{
    protected InventoryBatchRepository $inventoryBatchRepository;

    public function __construct(InventoryBatchRepository $inventoryBatchRepository)
    {
        $this->inventoryBatchRepository = $inventoryBatchRepository;
    }

    public function findInventoryBatch(int $id): ?InventoryBatch
    {
        return $this->inventoryBatchRepository->find($id);
    }
}