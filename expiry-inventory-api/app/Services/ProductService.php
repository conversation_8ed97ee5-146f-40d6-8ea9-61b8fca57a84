<?php

namespace App\Services;

use App\Enums\ProductStatus;
use App\Enums\BatchItemStatus;
use App\Enums\ProductInventoryStatusEnum;
use App\Enums\ProductOrderByEnum;
use App\Models\Product;
use App\Models\BatchItem;
use App\Repositories\ProductRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ProductService
{
    protected ProductRepository $productRepository;

    public function __construct(ProductRepository $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    public function getAllPaginatedProducts(array $filters = []): LengthAwarePaginator
    {
        $query = $this->getProductsQuery($filters);
        $limit = Arr::get($filters, 'limit', 20);
        $page = Arr::get($filters, 'page', 20);
        return $query->paginate($limit, ['*'], 'paginate', $page);
    }

    public function getProductsQuery(array $options)
    {
        $options = $options ?: [];

        $query = $this->productRepository->with([
            'batch_items' => function ($query) use ($options) {
                $inventoryStatus = Arr::get($options, 'inventory_status');
                if ($inventoryStatus == ProductInventoryStatusEnum::EXPIRED) {
                    $query->where('inventory_batch_products.status', BatchItemStatus::EXPIRED);
                } else if ($inventoryStatus == ProductInventoryStatusEnum::PAST_BEST_BEFORE) {
                    $query->where('inventory_batch_products.status', '!=', BatchItemStatus::OUT_OF_STOCK)
                        ->whereHas('dates', function ($q) {
                            $q->whereDate('date', '<=', today());
                        });
                } else {
                    $query->whereIn('inventory_batch_products.status', [BatchItemStatus::IN_STOCK, BatchItemStatus::DISCOUNTED]);
                }
                $query->whereHas('batch', function (Builder $query) use ($options) {
                    $query->whereNull('inventory_batches.deleted_at')
                        ->when(isset($options['location']), function (Builder $query) use ($options) {
                            $query->where('inventory_batches.location_id', $options['location']);
                        });
                });
            },
            'batch_items.batch',
            'batch_items.dates'
        ]);

        $query->where('products.is_excluded', false);

        if (isset($options['merchant_id'])) {
            $query->where('products.merchant_id', $options['merchant_id']);
        }

        if (isset($options['status'])) {
            $query->whereStatus($options['status']);
        } else {
            $query->whereNotIn('products.status', [ProductStatus::DRAFT, ProductStatus::ARCHIVED, ProductStatus::DELETED]);
        }

        if (isset($options['search'])) {
            $query->where(function (Builder $q) use ($options) {
                $q->orWhereRaw('LOWER(products.parent_name) LIKE "%' . $options['search'] . '%"')
                    ->orWhereRaw('LOWER(products.name) LIKE "%' . $options['search'] . '%"')
                    ->orWhereRaw('LOWER(products.sku) LIKE "%' . $options['search'] . '%"')
                    ->orWhereRaw('LOWER(products.barcode) LIKE "%' . $options['search'] . '%"');
            });
        }

        if (isset($options['location'])) {
            $query->whereRelation('locations', 'locations.id', '=', $options['location']);
        }

        // Apply filter conditions regardless of location (fixed the logic)
        if (isset($options['filter']) && !empty($options['filter'])) {
            if ($options['filter'] === 'with_batches') {
                $query->has('batch_items', '>', 0);
            } else if ($options['filter'] === 'without_batches') {
                $query->has('batch_items', '<=', 0);
            } else if ($options['filter'] === 'discounted') {
                $query->where('products.status', 'discounted');
            }
        }

        if (isset($options['inventory_status'])) {
            if ($options['inventory_status'] === ProductInventoryStatusEnum::IN_STOCK) {
                $query->whereHas('locations', fn($q) => $q->where('product_location.on_hand', '>', 0));
            } else if ($options['inventory_status'] === ProductInventoryStatusEnum::EXPIRED) {
                $query->whereRelation('batch_items', 'status', BatchItemStatus::EXPIRED);
            } else if ($options['inventory_status'] === ProductInventoryStatusEnum::PAST_BEST_BEFORE) {
                $query->whereHas('batch_items', function ($query) {
                    $query->where('inventory_batch_products.status', '!=', BatchItemStatus::OUT_OF_STOCK)
                        ->whereHas('dates', function ($q) {
                            $q->whereDate('date', '<=', today());
                        });
                });
            }
        }

        if (isset($options['with_unaccounted_quantity']) && isset($options['location'])) {
            if ($options['with_unaccounted_quantity'] == true) {
                $query->join('product_location', 'products.id', '=', 'product_location.product_id')
                    ->leftJoin('inventory_batch_products', function ($join) {
                        $join->on('products.id', '=', 'inventory_batch_products.product_id')
                            ->whereIn('inventory_batch_products.status', [BatchItemStatus::IN_STOCK, BatchItemStatus::DISCOUNTED])
                            ->whereNull('inventory_batch_products.deleted_at'); // Exclude soft-deleted batches
                    })
                    ->leftJoin('inventory_batches', function ($join) use ($options) {
                        $join->on('inventory_batch_products.inventory_batch_id', '=', 'inventory_batches.id')
                            ->whereNull('inventory_batches.deleted_at'); // Exclude soft-deleted batches
                    })
                    ->select('products.*', 'product_location.on_hand as on_hand', DB::raw('COALESCE(SUM(CASE WHEN inventory_batches.location_id = ' . $options['location'] . ' THEN inventory_batch_products.quantity ELSE 0 END), 0) as total_batched_quantity'))
                    ->where('product_location.location_id', $options['location'])
                    ->groupBy('products.id', 'on_hand')
                    ->havingRaw('on_hand > total_batched_quantity');
            }
        }

        $orderBy = Arr::get($options, 'order_by', ProductOrderByEnum::PARENT_NAME);
        $orderBy = strlen($orderBy) ? $orderBy : ProductOrderByEnum::PARENT_NAME;
        $order_direction = Arr::get($options, 'order_direction', 'desc');
        $order_direction = strlen($order_direction) ? $order_direction : 'desc';
        if ($orderBy == 'expiry_date') {
            $query->orderBy(
                BatchItem::select('expire_at')
                    ->whereColumn('inventory_batch_products.product_id', 'products.id')
                    ->latest()
                    ->take(1),
                $order_direction
            );
        } else {
            $query->orderBy($orderBy, $order_direction);
        }

        return $query;
    }

    /**
     * @return Collection<Product>
     */
    public function getAllParentProductsWithBatches(array $filters = []): Collection
    {
        return $this->productRepository->getAllParentProductsWithBatches($filters);
    }

    public function findProduct(int $id): ?Product
    {
        return $this->productRepository->find($id);
    }
}
