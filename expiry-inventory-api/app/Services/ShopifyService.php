<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Discount;
use App\Models\Merchant;
use App\Http\Helpers\ShopifyAPIHelper;
use Illuminate\Support\Arr;

class ShopifyService
{
    protected Merchant $merchant;
    protected Product $product;
    protected ?Discount $discount = null;

    public function __construct()
    {}

    public function setMerchant(Merchant $merchant)
    {
        $this->merchant = $merchant;
        return $this;
    }

    public function setProduct(Product $product)
    {
        $this->product = $product;
        return $this;
    }

    public function setDiscount($discount)
    {
        $this->discount = $discount;
        return $this;
    }

    public function updateShopifyProductPrices()
    {
        if (!$this->discount) {
            // revert any discount
            $this->updateProductPrice(
                $this->product,
                $this->product->original_price,
                $this->product->compare_price,
            );

            $storedPriceLists = $this->product->priceLists;
            if (!empty($storedPriceLists)) {
                // foreach update product PriceLists
                foreach ($storedPriceLists as $priceList) {
                    $this->updateProductPriceList(
                        Arr::get($priceList, 'priceListId'),
                        $this->product,
                        Arr::get($priceList, 'price'),
                        Arr::get($priceList, 'compareAtPrice'),
                    );
                }

                $this->product->deletePriceLists();
            }
            return;
        }

        $comparePrice = null;
        if (!$this->product->compare_price || $this->product->compare_price <= 0) {
            $comparePrice = $this->product->original_price;
        }

        // apply discount
        $this->updateProductPrice(
            $this->product,
            $this->discount->discounted_price,
            $comparePrice,
        );

        /**
         * Price Lists payload
         * [
         *  [
         *      id
         *      prices [
         *          nodes [
         *              [
         *                  compareAtPrice [
         *                      amount
         *                      currencyCode
         *                  ] | null
         *                  price [
         *                      amount
         *                      currencyCode
         *                  ]
         *              ]
         *          ]
         *      ]
         *  ]
         * ]
         */
        $priceLists = $this->getProductPriceLists($this->product);
        $updatedPriceLists = [];

        $storedPriceLists = collect($this->product->priceLists ?: [])->keyBy('priceListId');

        $discountRate = $this->discount->discount_rate;
        foreach ($priceLists as $priceList) {
            $priceListId = $priceList->id;
            if (empty($priceListId)) {
                info("ShopifyService@updateShopifyProductPrices", [
                    'product' => $this->product,
                    'priceList' => $priceList,
                ]);
                continue;
            }
            $productPrices = $priceList->prices->nodes;

            // update with existing price lists stored with product
            if ($existingPriceList = $storedPriceLists->get($priceListId)) {
                $newCompareAtPriceData = $existingPriceList['compareAtPrice'];

                if (!$newCompareAtPriceData) {
                    $newCompareAtPriceData = $existingPriceList['price'];
                }

                $newPriceData = $existingPriceList['price'];
                $newPriceData['amount'] = $newPriceData['amount'] - ($newPriceData['amount'] * $discountRate / 100.0);

                $this->updateProductPriceList(
                    $priceListId,
                    $this->product,
                    $newPriceData,
                    $newCompareAtPriceData,
                );

                // push to updated list for restore purchase
                $updatedPriceLists[] = $existingPriceList;
                continue;
            }

            // apply discount to PriceLists
            foreach ($productPrices as $productPrice) {
                $priceDataObj = $productPrice->price;
                $compareAtPriceDataObj = $productPrice->compareAtPrice;

                $priceData = [
                    'amount' => $priceDataObj->amount,
                    'currencyCode' => $priceDataObj->currencyCode,
                ];
                $newPriceData = $priceData;

                $compareAtPriceData = null;
                $newCompareAtPriceData = [];
                if ($compareAtPriceDataObj) {
                    $compareAtPriceData = [
                        'amount' => $compareAtPriceDataObj->amount,
                        'currencyCode' => $compareAtPriceDataObj->currencyCode,
                    ];
                    $newCompareAtPriceData = $compareAtPriceData;
                }
                else {
                    $newCompareAtPriceData = $priceData;
                }

                $newPriceData['amount'] = $newPriceData['amount'] - ($newPriceData['amount'] * $discountRate / 100.0);

                $this->updateProductPriceList(
                    $priceListId,
                    $this->product,
                    $newPriceData,
                    $newCompareAtPriceData,
                );

                // push to updated list for future restore
                $updatedPriceLists[] = [
                    'priceListId' => $priceListId,
                    'price' => $priceData,
                    'compareAtPrice' => $compareAtPriceData,
                ];
            }
        }

        // update product->priceLists DDB
        $this->product->priceLists = $updatedPriceLists;
    }

    public function updateProductPrice(Product $product, $price, $compare_price)
    {
        $shopifyHelper = ShopifyAPIHelper::withMerchant($this->merchant);
        $shopifyHelper->updateProductVariantPrices($product, $price, $compare_price);
    }

    public function getProductPriceLists(Product $product)
    {
        $shopifyHelper = ShopifyAPIHelper::withMerchant($this->merchant);
        return $shopifyHelper->getProductVariantPriceLists($product->shopify_variant_id);
    }

    public function updateProductPriceList(string $priceListId, Product $product, array $price, $compareAt)
    {
        $shopifyHelper = ShopifyAPIHelper::withMerchant($this->merchant);
        $shopifyHelper->updateProductVariantPriceLists($priceListId, $product, $price, $compareAt);
    }
}