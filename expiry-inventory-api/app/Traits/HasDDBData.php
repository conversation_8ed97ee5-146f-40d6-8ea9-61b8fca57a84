<?php

namespace App\Traits;

use App\Models\DDBModuleDetail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

trait HasDDBData
{
    public function _fill(array $attributes, $id = null)
    {
        foreach ($this->fill_values as $a) {
            if(array_key_exists($a, $attributes)) {
                $this->saveModuleDetails($this->getTable(), $a, $attributes[$a], $id);

                unset($attributes[$a]);
            }
        }

        return parent::fill($attributes);
    }

    protected function saveModuleDetails($table, $key, $value, $id = null) {
        $id = $id ?: ($this->id ?: $this->getNextId($table));

        $uuid = $table .'_' . $id;
        $details = DDBModuleDetail::find(['uuid' => $uuid, 'key' => $key]);

        if(!$details) {
            $details = new DDBModuleDetail();
            $details->uuid = $uuid;
            $details->key = $key;
        }

        $details->value = $value;
        $details->save();

        $this->setCache($key, $id, $value);
    }

    protected function getNextId($table) {
        DB::statement('SET information_schema_stats_expiry = 0;');
        $id = DB::select("SHOW TABLE STATUS LIKE '".$table."'");

        return $id[0]->Auto_increment;
    }

    protected function getModuleDetails($model, $key) {
        $value = $this->getCache($key, $this->id);

        if($value) {
            return $value;
        }

        $uuid = $model .'_' . $this->id;
        $details = DDBModuleDetail::find(['uuid' => $uuid, 'key' => $key]);

        if(!$details) {
            return null;
        }

        $this->setCache($key, $this->id, $details->value);

        return $details->value;
    }

    protected function deleteModuleDetails($uuid, $keys = []) {
        $query = DDBModuleDetail::where('uuid', $uuid);
        if(!empty($keys)) {
            $query->whereIn('key', $keys);
        }
        foreach($query->get() as $detail) {
            $detail->delete();
        }
    }

    protected function setCache($key, $id, $value) {
        cache([$this->getTable() . "_{$id}_{$key}" => $value], now()->addMinutes(10));
    }

    protected function getCache($key, $id) {
        if (Cache::has($this->getTable() . "_{$id}_{$key}")) {
            return cache($this->getTable() . "_{$id}_{$key}");
        }
        return null;
    }

    protected function deleteCache($id) {
        foreach($this->fill_values as $key) {
            if (Cache::has($this->getTable() . "_{$id}_{$key}")) {
                Cache::forget($this->getTable() . "_{$id}_{$key}");
            }
        }
    }
}
