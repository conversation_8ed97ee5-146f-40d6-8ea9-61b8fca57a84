<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Schema;

if (! function_exists('json_resp')) {
    /*
     *
     * {
     *      "data"          : [],
     *      "message"       : null,
     *      "code_status"   : 200
     * }
     *
     * */
    function json_resp($data = [], $message = null, $status = 200) {
        return response()->json([
            'data'          => $data,
            'message'       => $message,
            'code_status'   => $status
        ],$status);
    }
}

if (! function_exists('file_log')) {
    /*
     *
     * {
     *      "path"          : "system.log",
     *      "message"       : null,
     *      "title"         : null
     * }
     *
     * */
    function file_log($path = 'system.log', $message = null, $title = null) {
        $path = 'logs/'.$path;

        $folders = explode('/', $path);
        array_pop($folders);
        $folders = implode('/', $folders);
        $folders = storage_path($folders);

        if($folders && !File::isDirectory($folders)){
            File::makeDirectory($folders, 0740, true, true);
        }

        if($title) {
            file_put_contents(storage_path($path),
                $title."\r\n",
                FILE_APPEND | LOCK_EX
            );

            file_put_contents(storage_path($path),
                "==============\r\n",
                FILE_APPEND | LOCK_EX
            );
        }

        if(is_array($message)) {
            file_put_contents(storage_path($path),
                print_r($message, true)."\r\n",
                FILE_APPEND | LOCK_EX
            );
        }
        elseif(is_object($message)) {
            file_put_contents(storage_path($path),
                print_r(objectToArray($message), true)."\r\n",
                FILE_APPEND | LOCK_EX
            );
        }
        else {
            file_put_contents(storage_path($path),
                $message."\r\n",
                FILE_APPEND | LOCK_EX
            );
        }
    }
}

if (! function_exists('objectToArray')) {
    function objectToArray($object) {
        return json_decode(json_encode($object), true);
    }
}

if (! function_exists('array_overlap')) {
    function array_overlap(array $array1, array $array2) {
        $array = array_merge($array1, $array2);

        return count($array) !== count(array_flip($array));
    }
}

if(!function_exists('generate_random_numeric_string')) {
    function generate_random_numeric_string(int $length)
    {
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

if(!function_exists('parse_shopify_response_links')) {
    function parse_shopify_response_links($headers) {
        if(!$headers) {
            return [];
        }
        $links = (Arr::get($headers, 'Link') ?: Arr::get($headers, 'link')) ?: [];
        if(!count($links)) {
            return [];
        }
        $available_links = [];
        $links = explode(',', $links[0]);
        foreach ($links as $link) {
            if (preg_match('/<(.*)>;\srel=\\"(.*)\\"/', $link, $matches)) {
                $available_links[$matches[2]] = $matches[1];
            }
        }
        return $available_links;
    }
}
if(!function_exists('extract_shopify_integer_id')) {
    function extract_shopify_integer_id($gid)
    {
        // Split the GID string by '/'
        $parts = explode('/', $gid);
        // Get the last part which contains the integer ID
        $integerId = end($parts);
        return $integerId;
    }
}

if(!function_exists('generate_shopify_graphql_id')) {
    function generate_shopify_graphql_id($id, $resourceName)
    {
        return "gid://shopify/{$resourceName}/{$id}";
    }
}

if(!function_exists('dropColumnIfExists')) {
    function dropColumnIfExists($table, $column) {
        if (Schema::hasColumn($table, $column)) //check the column
        {
            Schema::table($table, function (Blueprint $table) use($column)
            {
                $table->dropColumn($column); //drop it
            });
        }
    }
}