<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'shopify' => [
        'key' => env('SHOPIFY_API_KEY'),
        'secret' => env('SHOPIFY_API_SECRET'),
        'access_scopes' => [
            'read_content',
            'write_content',
            'read_themes',
            'write_themes',
            'read_script_tags',
            'write_script_tags',
            'read_orders',
            'read_inventory',
            'write_inventory',
            'read_locations',
            'read_products',
            'write_products'
        ],
        'api_version' => env('SHOPIFY_API_VERSION'),
        'app_url' => env('SHOPIFY_APP_URL'),
        'metafield_namespace' => env('SHOPIFY_METAFIELD_NAMESPACE'),
        'enc_passphrase' => env('ENCRYPTION_PASSPHRASE'),
    ]
];
