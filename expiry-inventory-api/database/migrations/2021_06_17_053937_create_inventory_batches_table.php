<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInventoryBatchesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('inventory_batches', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('merchant_id');
            $table->bigInteger('location_id');
            $table->string('name')->nullable();
            $table->date('received_at')->nullable();
            $table->string('batch_number')->nullable();
            $table->string('barcode')->nullable();
            $table->string('invoice_number')->nullable();
            $table->text('description')->nullable();
            $table->string('status')->default('in_stock');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('inventory_batches');
    }
}
