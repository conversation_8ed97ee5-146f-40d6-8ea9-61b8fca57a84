<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('merchant_id');
            $table->bigInteger('shopify_order_id');
            $table->bigInteger('customer_id');
            $table->string('order_number');
            $table->decimal('amount', 10, 2);
            $table->string('status');
            $table->string('fulfillment_status');
            $table->string('batch_assign_status')->default('unassigned');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
