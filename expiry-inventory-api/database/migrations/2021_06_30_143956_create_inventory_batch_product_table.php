<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInventoryBatchProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('inventory_batch_products', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('inventory_batch_id');
            $table->bigInteger('product_id');
            $table->integer('quantity');
            $table->date('expire_at')->nullable();
            $table->string('status')->default('in_stock');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('inventory_batch_products');
    }
}
