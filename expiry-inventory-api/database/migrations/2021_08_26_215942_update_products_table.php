<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $columns = Schema::getColumnListing('products');
            if(array_search('price', $columns) === false) $table->decimal('price', 10, 2)->after('sku');
            if(array_search('original_price', $columns) === false) $table->decimal('original_price', 10, 2)->nullable()->after('price');
            if(array_search('status', $columns) === false) $table->enum('status', ['active', 'discounted'])->default('active')->after('original_price');
        });

        Schema::table('inventory_batches', function (Blueprint $table) {
            $columns = Schema::getColumnListing('inventory_batches');
            if(array_search('lot_number', $columns) === false) $table->string('lot_number')->nullable()->after('received_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('price');
            $table->dropColumn('original_price');
            $table->dropColumn('status');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('lot_number');
        });
    }
}
