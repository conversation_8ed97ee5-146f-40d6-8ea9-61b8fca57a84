<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDiscountsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('batch_item_id');
            $table->unsignedBigInteger('product_id');
            $table->enum('type', ['expiry', 'slow_moving']);
            $table->decimal('discount_rate', 10, 2);
            $table->decimal('discounted_price', 10, 2);
            $table->integer('discount_period')->nullable();
            $table->timestamp('discount_end_at')->nullable();
            $table->boolean('is_active')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('discounts');
    }
}
