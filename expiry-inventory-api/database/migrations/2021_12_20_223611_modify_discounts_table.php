<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyDiscountsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('discounts', function (Blueprint $table) {
            $columns = Schema::getColumnListing('discounts');
            if(array_search('track_period', $columns) === false) $table->integer('track_period')->nullable()->after('discount_end_at');
            if(array_search('track_unit', $columns) === false) $table->enum('track_unit', ['days', 'months'])->nullable()->after('track_period');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('discounts', function (Blueprint $table) {
            $table->dropColumn('track_period');
            $table->dropColumn('track_unit');
        });
    }
}
