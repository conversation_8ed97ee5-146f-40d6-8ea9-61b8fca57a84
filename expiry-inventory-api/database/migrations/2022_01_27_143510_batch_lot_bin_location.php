<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class BatchLotBinLocation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inventory_batches', function (Blueprint $table) {
            $table->renameColumn('batch_number', 'bin_location');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inventory_batches', function (Blueprint $table) {
            $table->renameColumn('bin_location', 'batch_number');
        });
    }
}
