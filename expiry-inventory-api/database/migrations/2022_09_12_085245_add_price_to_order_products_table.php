<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPriceToOrderProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_products', function (Blueprint $table) {
            $columns = Schema::getColumnListing('order_products');
            if(array_search('price', $columns) === false) $table->decimal('price', 10, 2)->after('quantity');
            $table->unsignedBigInteger('order_id')->change();
            $table->unsignedBigInteger('product_id')->change();
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
        });

        Schema::table('orders', function (Blueprint $table) {
            $columns = Schema::getColumnListing('orders');
            if(array_search('billing_address', $columns) === false) $table->json('billing_address')->nullable()->after('amount');
            if(array_search('shipping_address', $columns) === false) $table->json('shipping_address')->nullable()->after('billing_address');
            if(array_search('note', $columns) === false) $table->text('note')->nullable()->after('shipping_address');
        });

        Schema::table('inventory_batch_products', function (Blueprint $table) {
            $table->unsignedBigInteger('inventory_batch_id')->change();
            $table->unsignedBigInteger('product_id')->change();
            $table->foreign('inventory_batch_id')->references('id')->on('inventory_batches')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });

        Schema::table('discounts', function (Blueprint $table) {
            $table->foreign('batch_item_id')->references('id')->on('inventory_batch_products')->onDelete('cascade');
        });

        Schema::table('inventory_histories', function (Blueprint $table) {
            $table->unsignedBigInteger('merchant_id')->change();
            $table->unsignedBigInteger('action_id')->change();
            $table->unsignedBigInteger('batch_id')->change();
            $table->foreign('batch_id')->references('id')->on('inventory_batches')->onDelete('cascade');
        });

        Schema::table('inventory_batches', function (Blueprint $table) {
            $table->unsignedBigInteger('merchant_id')->change();
            $table->unsignedBigInteger('location_id')->change();
        });

        Schema::table('locations', function (Blueprint $table) {
            $table->unsignedBigInteger('merchant_id')->change();
        });

        Schema::table('merchants', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->change();
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->change();
            $table->unsignedBigInteger('merchant_id')->change();
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->unsignedBigInteger('merchant_id')->change();
            $table->unsignedBigInteger('customer_id')->change();
        });

        Schema::table('products', function (Blueprint $table) {
            $table->unsignedBigInteger('merchant_id')->change();
            $table->unsignedBigInteger('parent_id')->change();
        });

        Schema::table('order_products_assignments', function (Blueprint $table) {
            $table->foreign('order_item_id')->references('id')->on('order_products')->onDelete('cascade');
            $table->foreign('batch_item_id')->references('id')->on('inventory_batch_products')->onDelete('cascade');
        });

        Schema::create('shopify_data_requests', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('merchant_id');
            $table->json('payload')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_products', function (Blueprint $table) {
            $table->dropColumn('price');
            $table->dropForeign('order_id');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('billing_address');
            $table->dropColumn('shipping_address');
            $table->dropColumn('note');
        });

        Schema::table('inventory_batch_products', function (Blueprint $table) {
            $table->dropForeign('inventory_batch_id');
            $table->dropForeign('product_id');
        });

        Schema::table('discounts', function (Blueprint $table) {
            $table->dropForeign('batch_item_id');
        });

        Schema::table('inventory_histories', function (Blueprint $table) {
            $table->dropForeign('inventory_batch_id');
        });

        Schema::table('order_products_assignments', function (Blueprint $table) {
            $table->dropForeign('order_item_id');
            $table->dropForeign('batch_item_id');
        });

        Schema::dropIfExists('shopify_data_requests');
    }
}
