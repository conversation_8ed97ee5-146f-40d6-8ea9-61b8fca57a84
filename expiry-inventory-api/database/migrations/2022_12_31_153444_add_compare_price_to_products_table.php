<?php

use App\Models\Merchant;
use App\Jobs\HandleSyncShopifyProductsGraphql;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddComparePriceToProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $columns = Schema::getColumnListing('products');
            if(array_search('compare_price', $columns) === false) $table->decimal('compare_price', 10, 2)->nullable()->after('original_price');
        });

        foreach(Merchant::all() as $merchant) {
            HandleSyncShopifyProductsGraphql::dispatch($merchant);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('compare_price');
        });
    }
}
