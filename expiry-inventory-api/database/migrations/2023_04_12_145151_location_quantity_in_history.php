<?php

use App\Models\InventoryHistory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class LocationQuantityInHistory extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inventory_histories', function (Blueprint $table) {
            $columns = Schema::getColumnListing('inventory_histories');
            if(array_search('location_quantity', $columns) === false) $table->integer('location_quantity')->after('result_quantity');
        });

        foreach(InventoryHistory::all() as $history) {
            $history->location_quantity = $history->result_quantity;
            $history->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inventory_histories', function (Blueprint $table) {
            $table->dropColumn('location_quantity');
        });
    }
}
