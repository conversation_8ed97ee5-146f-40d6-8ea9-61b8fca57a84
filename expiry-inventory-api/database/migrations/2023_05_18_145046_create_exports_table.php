<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if(!Schema::hasTable('exports')) {
            Schema::create('exports', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('merchant_id')->nullable();
                $table->string('model')->nullable();
                $table->string('filename');
                $table->string('path');
                $table->string('url');
                $table->timestamp('from_date')->nullable();
                $table->timestamp('to_date')->nullable();
                $table->json('options')->nullable();
                $table->string('status')->default('pending');
                $table->timestamps();
            });
        }

        Schema::table('merchants', function (Blueprint $table) {
            $columns = Schema::getColumnListing('merchants');
            if(array_search('test', $columns) === false) $table->boolean('test')->default(false)->after('access_token');
        });

        Schema::table('subscriptions', function (Blueprint $table) {
            $columns = Schema::getColumnListing('subscriptions');
            if(array_search('test', $columns) === false) $table->boolean('test')->default(false)->after('type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exports');

        Schema::table('merchants', function (Blueprint $table) {
            $columns = Schema::getColumnListing('merchants');
            if(array_search('test', $columns) !== false) $table->dropColumn('test');
        });

        Schema::table('subscriptions', function (Blueprint $table) {
            $columns = Schema::getColumnListing('subscriptions');
            if(array_search('test', $columns) !== false) $table->dropColumn('test');
        });
    }
}
