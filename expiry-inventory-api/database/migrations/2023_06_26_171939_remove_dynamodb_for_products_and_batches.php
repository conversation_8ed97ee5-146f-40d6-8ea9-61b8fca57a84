<?php

use App\Models\DDBModuleDetail;
use App\Models\InventoryBatch;
use App\Models\Product;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveDynamodbForProductsAndBatches extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inventory_batches', function(Blueprint $table) {
            $columns = Schema::getColumnListing('inventory_batches');
            if(array_search('is_sync_quantity', $columns) === false) $table->boolean('is_sync_quantity')->default(true)->after('status');
        });
        
        Schema::table('products', function(Blueprint $table) {
            $columns = Schema::getColumnListing('products');
            if(array_search('quantity', $columns) === false) $table->integer('quantity')->default(true)->after('sku');
            if(array_search('is_inventory_managed', $columns) === false) $table->boolean('is_inventory_managed')->default(true)->after('quantity');
            if(array_search('image_url', $columns) === false) $table->string('image_url')->nullable()->after('is_inventory_managed');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inventory_batches', function (Blueprint $table) {
            $columns = Schema::getColumnListing('inventory_batches');
            if(array_search('is_sync_quantity', $columns) !== false) $table->dropColumn('is_sync_quantity');
        });

        Schema::table('products', function (Blueprint $table) {
            $columns = Schema::getColumnListing('products');
            if(array_search('quantity', $columns) !== false) $table->dropColumn('quantity');
            if(array_search('is_inventory_managed', $columns) !== false) $table->dropColumn('is_inventory_managed');
            if(array_search('image_url', $columns) !== false) $table->dropColumn('image_url');
        });
    }
}
