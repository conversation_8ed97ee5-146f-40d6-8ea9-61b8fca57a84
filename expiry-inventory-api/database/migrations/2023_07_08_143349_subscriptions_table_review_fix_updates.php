<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SubscriptionsTableReviewFixUpdates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subscriptions', function(Blueprint $table) {
            $columns = Schema::getColumnListing('subscriptions');
            if(array_search('billed_at', $columns) !== false) $table->renameColumn('billed_at', 'bill_ends_at')->nullable()->change();
            if(array_search('status', $columns) !== false) $table->string('status')->default('pending')->change();
        });
        Schema::table('subscriptions', function(Blueprint $table) {
            $columns = Schema::getColumnListing('subscriptions');
            if(array_search('billing_period', $columns) === false) $table->string('billing_period')->default('month')->after('type');
            if(array_search('canceled_at', $columns) === false) $table->timestamp('canceled_at')->nullable()->after('bill_ends_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $columns = Schema::getColumnListing('subscriptions');
            if(array_search('billing_period', $columns) !== false) $table->dropColumn('billing_period');
            if(array_search('canceled_at', $columns) !== false) $table->dropColumn('canceled_at');
        });
    }
}
