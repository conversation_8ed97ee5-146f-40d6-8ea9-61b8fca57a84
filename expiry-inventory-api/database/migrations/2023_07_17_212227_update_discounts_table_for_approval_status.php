<?php

use App\Models\Discount;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateDiscountsTableForApprovalStatus extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('discounts', function (Blueprint $table) {
            $columns = Schema::getColumnListing('discounts');
            if(array_search('is_active', $columns) !== false) $table->string('is_active')->default('pending')->change();
        });
        
        Schema::table('discounts', function (Blueprint $table) {
            $columns = Schema::getColumnListing('discounts');
            if(array_search('is_active', $columns) !== false) $table->renameColumn('is_active', 'status');
        });
        
        foreach(Discount::all() as $discount) {
            if($discount->status == '1') {
                $discount->status = 'active';
                $discount->save();
            }
            else if($discount->status == '0') {
                $discount->status = 'inactive';
                $discount->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        foreach(Discount::all() as $discount) {
            if($discount->status == 'active') {
                $discount->status = '1';
                $discount->save();
            }
            else {
                $discount->status = '0';
                $discount->save();
            }
        }

        Schema::table('discounts', function (Blueprint $table) {
            $columns = Schema::getColumnListing('discounts');
            if(array_search('status', $columns) !== false) $table->boolean('status')->default(true)->change();
        });
        
        Schema::table('discounts', function (Blueprint $table) {
            $columns = Schema::getColumnListing('discounts');
            if(array_search('status', $columns) !== false) $table->renameColumn('status', 'is_active');
        });
    }
}
