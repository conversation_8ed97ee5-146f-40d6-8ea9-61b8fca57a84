<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MakeProductsImageUrlText extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function(Blueprint $table) {
            $columns = Schema::getColumnListing('products');
            if(array_search('image_url', $columns) !== false) $table->text('image_url')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function(Blueprint $table) {
            $columns = Schema::getColumnListing('products');
            if(array_search('image_url', $columns) !== false) $table->string('image_url')->nullable()->change();
        });
    }
}
