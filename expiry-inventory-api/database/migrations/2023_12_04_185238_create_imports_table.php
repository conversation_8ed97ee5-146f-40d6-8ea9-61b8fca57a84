<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateImportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if(!Schema::hasTable('imports')) {
            Schema::create('imports', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('merchant_id');
                $table->string('model')->nullable();
                $table->string('path');
                $table->unsignedInteger('rows_count')->default(0);
                $table->string('status')->default('pending');
                $table->timestamps();
            });
        }

        Schema::table('inventory_histories', function(Blueprint $table) {
            $columns = Schema::getColumnListing('inventory_histories');
            if(array_search('import_id', $columns) === false) $table->unsignedBigInteger('import_id')->nullable()->after('batch_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('imports');
        
        dropColumnIfExists('inventory_histories', 'import_id');
    }
}
