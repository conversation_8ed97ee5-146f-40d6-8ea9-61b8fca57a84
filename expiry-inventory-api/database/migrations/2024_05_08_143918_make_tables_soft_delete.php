<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MakeTablesSoftDelete extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inventory_batch_products', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('inventory_histories', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('order_products', function (Blueprint $table) {
            $table->softDeletes();
        });
        dropColumnIfExists('order_products', 'items_assigned');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        dropColumnIfExists('inventory_batch_products', 'deleted_at');
        dropColumnIfExists('inventory_histories', 'deleted_at');
        dropColumnIfExists('order_products', 'deleted_at');
        Schema::table('order_products', function (Blueprint $table) {
            $table->json('items_assigned')->nullable();
        });
    }
}
