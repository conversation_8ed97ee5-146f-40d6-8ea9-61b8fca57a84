<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPasswordToMerchantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchants', function (Blueprint $table) {
            $table->string('password')->nullable()->after('access_token');
        });

        if (!Schema::hasTable('user_verify')) {
            Schema::create('user_verify', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('merchant_id')->nullable();
                $table->string('email')->index();
                $table->string('token');
                $table->dateTime('used_at')->nullable();
                $table->dateTime('expire_at')->nullable();
                $table->dateTime('created_at')->nullable();

                $table->foreign('merchant_id')->on('merchants')->references('id')->nullOnDelete();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        dropColumnIfExists('merchants', 'password');

        Schema::dropDatabaseIfExists('user_verify');
    }
}
