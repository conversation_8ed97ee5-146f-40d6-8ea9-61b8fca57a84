<?php

use App\Models\BatchItem;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInventoryBatchProductDatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('inventory_batch_product_dates')) {
            Schema::create('inventory_batch_product_dates', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('batch_item_id');
                $table->date('date');
                $table->softDeletes();
            });

            foreach (BatchItem::withTrashed()->whereNotNull('best_before')->get() as $batch_item) {
                $batch_item->dates()->create([
                    'date' => $batch_item->best_before,
                    'deleted_at' => $batch_item->deleted_at
                ]);
            }

            dropColumnIfExists('inventory_batch_products', 'best_before');
        }

        Schema::table('discounts', function (Blueprint $table) {
            $table->string('type')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('inventory_batch_product_dates');

        Schema::table('inventory_batch_products', function (Blueprint $table) {
            $table->date('best_before')->nullable()->after('expire_at');
        });

        Schema::table('discounts', function (Blueprint $table) {
            $table->enum('type', ['expiry', 'slow_moving'])->change();
        });
    }
}
