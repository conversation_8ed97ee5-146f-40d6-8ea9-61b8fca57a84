<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProductLocationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('product_location')) {
            Schema::create('product_location', function (Blueprint $table) {
                $table->unsignedBigInteger('product_id');
                $table->unsignedBigInteger('location_id');
                $table->unsignedInteger('on_hand');

                $table->foreign('product_id')->references('id')->on('products')->cascadeOnDelete();
                $table->foreign('location_id')->references('id')->on('locations')->cascadeOnDelete();
            });
        }

        Schema::table('products', function (Blueprint $table) {
            $table->string('status')->default('active')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_location');
    }
}
