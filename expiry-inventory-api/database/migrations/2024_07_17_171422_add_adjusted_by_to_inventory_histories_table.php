<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAdjustedByToInventoryHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inventory_histories', function (Blueprint $table) {
            if (!Schema::hasColumn('inventory_histories', 'adjusted_by')) {
                $table->string('adjusted_by')->nullable()->after('action_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inventory_histories', function (Blueprint $table) {
            if (Schema::hasColumn('inventory_histories', 'adjusted_by')) {
                $table->dropColumn('adjusted_by');
            }
        });
    }
}
