<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNameToOrderProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_products', function (Blueprint $table) {
            if (!Schema::hasColumn('order_products', 'line_item_id')) {
                $table->string('line_item_id')->nullable()->after('product_id');
            }
            if (!Schema::hasColumn('order_products', 'name')) {
                $table->string('name')->nullable()->after('line_item_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_products', function (Blueprint $table) {
            if (Schema::hasColumn('order_products', 'line_item_id')) {
                $table->dropColumn('line_item_id');
            }
            if (Schema::hasColumn('order_products', 'name')) {
                $table->dropColumn('name');
            }
        });
    }
}
