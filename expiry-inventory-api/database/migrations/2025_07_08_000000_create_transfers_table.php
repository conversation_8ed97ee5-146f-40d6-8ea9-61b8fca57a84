<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transfers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('merchant_id');
            $table->unsignedBigInteger('source_batch_id');
            $table->unsignedBigInteger('destination_batch_id');
            $table->unsignedBigInteger('product_id');
            $table->integer('quantity');
            $table->boolean('source_quantity_adjustment')->default(true);
            $table->boolean('destination_quantity_adjustment')->default(true);
            $table->boolean('is_draft')->default(false);
            $table->string('status')->default('pending'); // pending, completed, cancelled
            $table->text('notes')->nullable();
            $table->timestamp('executed_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('merchant_id')->references('id')->on('merchants')->onDelete('cascade');
            $table->foreign('source_batch_id')->references('id')->on('inventory_batches')->onDelete('cascade');
            $table->foreign('destination_batch_id')->references('id')->on('inventory_batches')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');

            // Indexes for better performance
            $table->index(['merchant_id', 'is_draft']);
            $table->index(['merchant_id', 'status']);
            $table->index(['source_batch_id']);
            $table->index(['destination_batch_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transfers');
    }
}
