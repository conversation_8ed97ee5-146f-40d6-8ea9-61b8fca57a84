<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('merchant_configs')) {
            Schema::create('merchant_configs', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('merchant_id');
                $table->string('key');
                $table->json('value')->nullable();
                $table->timestamps();

                $table->unique(['merchant_id', 'key']);
                $table->foreign('merchant_id')->references('id')->on('merchants')->cascadeOnDelete();
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('merchant_configs');
    }
};

