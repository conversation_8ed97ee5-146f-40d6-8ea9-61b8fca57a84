<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('inventory_batch_verifications')) {
            Schema::create('inventory_batch_verifications', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('merchant_id');
                $table->unsignedBigInteger('inventory_batch_id');
                $table->integer('quantity');
                $table->string('adjusted_by');
                $table->timestamps();
                $table->foreign('inventory_batch_id')->references('id')->on('inventory_batches')->cascadeOnDelete();
                $table->foreign('merchant_id')->references('id')->on('merchants')->cascadeOnDelete();
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('inventory_batch_verifications');
    }
};

