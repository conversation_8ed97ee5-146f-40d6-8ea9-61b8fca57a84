<?php

namespace Database\Seeders;

use App\Models\InventoryAction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class InventoryActionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $actions = [
            ['name' => 'Batch created', 'trigger_by' => null],
            ['name' => 'Batch updated', 'trigger_by' => null],
            ['name' => 'Batch marked expired', 'trigger_by' => 'Expiry Date & Inventory Batch'],
            ['name' => 'Order created', 'trigger_by' => 'Shopify'],
            ['name' => 'Order updated', 'trigger_by' => 'Shopify'],
            ['name' => 'Batch transfered', 'trigger_by' => null]
        ];
        foreach ($actions as $action) {
            $slug = Str::slug($action['name']);
            InventoryAction::firstOrCreate(
                ['slug' => $slug],
                ['name' => $action['name'], 'trigger_by' => $action['trigger_by']]
            );
        }
    }
}
