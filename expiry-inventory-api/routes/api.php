<?php

use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ConfigController;
use App\Http\Controllers\ShopifyController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\DiscountController;
use App\Http\Controllers\ImportHistoryController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\InventoryBatchController;
use App\Http\Controllers\InventoryBatchVerificationController;
use App\Http\Controllers\MerchantController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\TransferController;
use App\Http\Controllers\ShopifyWebhookController;
use App\Http\Controllers\ThemeController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });

Route::get('/plan', [PlanController::class, 'index']);

Route::prefix('shopify')->group(function () {
    Route::post('/', [ShopifyController::class, 'install']);
    Route::post('/auth', [ShopifyController::class, 'login']);
    Route::post('/auth/callback', [ShopifyController::class, 'callback']);

    Route::middleware(['auth:shopify-hook'])->group(function () {
        Route::post('/uninstall', [ShopifyWebhookController::class, 'uninstall']);
        Route::post('/update', [ShopifyWebhookController::class, 'update']);
        Route::prefix('product')->group(function () {
            Route::post('/created', [ProductController::class, 'productCreated']);
            Route::post('/updated', [ProductController::class, 'productUpdated']);
            Route::post('/deleted', [ProductController::class, 'productDeleted']);
        });
        Route::prefix('location')->group(function () {
            Route::post('/created', [LocationController::class, 'locationCreated']);
            Route::post('/updated', [LocationController::class, 'locationUpdated']);
            Route::post('/deleted', [LocationController::class, 'locationDeleted']);
        });
        Route::prefix('order')->group(function () {
            Route::post('/created', [OrderController::class, 'orderCreated']);
            Route::post('/updated', [OrderController::class, 'orderUpdated']);
            Route::post('/cancelled', [OrderController::class, 'orderCancelled']);
            Route::post('/deleted', [OrderController::class, 'orderDeleted']);
        });
        Route::prefix('customer')->group(function () {
            Route::post('/updated', [CustomerController::class, 'customerUpdated']);
        });
        Route::prefix('subscription')->group(function () {
            Route::post('/updated', [PlanController::class, 'subscriptionUpdated']);
        });

        // GDPR mandatory webhooks
        Route::post('/delete', [ShopifyWebhookController::class, 'shopRedact']);
        Route::prefix('user')->group(function () {
            Route::post('/', [ShopifyWebhookController::class, 'customersRequest']);
            Route::post('/delete', [ShopifyWebhookController::class, 'customersRedact']);
        });
    });
});

Route::middleware(['auth:shopify'])->group(function () {
    Route::prefix('product')->group(function () {
        Route::get('/', [ProductController::class, 'index']);
        Route::get('/parent-with-batches', [ProductController::class, 'getParentProductsWithBatches']);
        Route::get('/{product_id}', [ProductController::class, 'show']);
        Route::post('/export', [ProductController::class, 'export']);
        Route::get('/preview/{product_id}', [ProductController::class, 'getPreviewUrl']);
        Route::get('/{parent_id}/variants', [ProductController::class, 'getVariants']);
        Route::get('/fetch-related-variants/{variant_id}', [ProductController::class, 'getRelatedVariants']);
    });

    Route::prefix('location')->group(function () {
        Route::get('/', [LocationController::class, 'index']);
    });

    Route::prefix('order')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::get('/shopify/{order_id}', [OrderController::class, 'showByShopifyID']);
        Route::get('/print', [OrderController::class, 'getDefaultPrintTemplate']);
        Route::get('/{order}/print', [OrderController::class, 'print']);
        Route::get('/{order}', [OrderController::class, 'show']);
        Route::post('/{order}/auto-assign', [OrderController::class, 'autoAssignBatch']);
        Route::post('/{order}/unassign', [OrderController::class, 'unassignBatch']);
        Route::post('/bulk-auto-assign', [OrderController::class, 'bulkAutoAssignBatch']);
        Route::post('/bulk-auto-assign/{type}', [OrderController::class, 'bulkAutoAssignBatchForType']);
        Route::post('/export', [OrderController::class, 'export']);
    });

    Route::prefix('inventory_batch')->group(function () {
        Route::get('/', [InventoryBatchController::class, 'index']);
        Route::get('/history', [InventoryBatchController::class, 'history']);
        Route::get('/analytics', [AnalyticsController::class, 'analyticsGetDates']);
        Route::post('/analytics/export', [AnalyticsController::class, 'exportAnalyticsDates']);
        Route::get('/analytics/products', [AnalyticsController::class, 'analyticsGetProductsList']);
        Route::post('/analytics/products/export', [AnalyticsController::class, 'exportAnalyticsProductsList']);
        Route::post('/nearest-batch', [InventoryBatchController::class, 'getNearestExpiringBatch']);
        Route::get('/{inventory_batch}', [InventoryBatchController::class, 'show']);
        Route::get('/{inventory_batch_id}/orders', [InventoryBatchController::class, 'assignedOrders']);
        Route::get('/{inventory_batch}/verifications', [InventoryBatchVerificationController::class, 'index']);
        Route::post('/{inventory_batch}/verifications', [InventoryBatchVerificationController::class, 'store']);
        Route::post('/', [InventoryBatchController::class, 'store']);
        Route::put('/{inventory_batch}', [InventoryBatchController::class, 'update']);
        Route::post('/import', [InventoryBatchController::class, 'import']);
        Route::post('/export', [InventoryBatchController::class, 'export']);
        Route::delete('/all', [InventoryBatchController::class, 'clearBatchesData']);
        Route::delete('/{inventory_batch}', [InventoryBatchController::class, 'delete']);

        Route::post('/transfer', [TransferController::class, 'store']);
    });

    Route::prefix('transfers')->group(function () {
        Route::get('/', [TransferController::class, 'index']);
        Route::post('/', [TransferController::class, 'store']);
        Route::get('/{transfer}', [TransferController::class, 'show']);
        Route::put('/{transfer}', [TransferController::class, 'update']);
        Route::post('/{transfer}/execute', [TransferController::class, 'execute']);
        Route::delete('/{transfer}', [TransferController::class, 'destroy']);
    });

    Route::prefix('import_history')->group(function () {
        Route::get('/', [ImportHistoryController::class, 'index']);
    });

    Route::prefix('merchant')->group(function () {
        Route::get('/password-check', [MerchantController::class, 'passwordSetupRequiredCheck']);
        Route::post('/password-setup', [MerchantController::class, 'passwordSetup']);
        Route::post('/send-email-verify', [MerchantController::class, 'sendEmailVerification']);
        Route::post('/verify-forget-password-token', [MerchantController::class, 'verifyForgetPasswordToken']);
        Route::post('/reset-password', [MerchantController::class, 'resetPassword']);
    });

    Route::prefix('preference')->group(function () {
        Route::get('/', [ConfigController::class, 'show']);
        Route::get('/print', [ConfigController::class, 'showPrintSettings']);
        Route::get('/expiry', [ConfigController::class, 'showExpiryTemplate']);
        Route::put('/', [ConfigController::class, 'update']);
        Route::put('/print', [ConfigController::class, 'updatePrintSettings']);
    });

    Route::prefix('plan')->group(function () {
        Route::put('/{charge_id}', [PlanController::class, 'update']);
        Route::post('/upgrade', [PlanController::class, 'upgradePlan']);
        Route::post('/downgrade', [PlanController::class, 'downgradePlan']);
    });

    Route::prefix('discounts')->group(function () {
        Route::get('/pending_count', [DiscountController::class, 'getPendingDiscountsCount']);
        Route::get('/pending', [DiscountController::class, 'getPendingDiscounts']);
        Route::post('/{discount_id}/approve', [DiscountController::class, 'approveDiscount']);
        Route::post('/{discount_id}/cancel', [DiscountController::class, 'cancelDiscount']);
    });


    Route::get('/themes', [ThemeController::class, 'index']);
    Route::get('/themes/{theme_id}', [ThemeController::class, 'show']);
});

Route::middleware(['auth:shopify-url'])->group(function () {
    Route::prefix('preference')->group(function () {
        Route::get('/print', [ConfigController::class, 'showPrintSettings']);
    });
    Route::prefix('order')->group(function () {
        Route::get('/{order}/print', [OrderController::class, 'print']);
    });
    Route::prefix('shopify')->group(function () {
        Route::post('/access_scopes', [ShopifyController::class, 'checkAccessScopes']);
    });
});
