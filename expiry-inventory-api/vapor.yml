id: 43545
name: expiry-inventory-api
environments:
    live:
        memory: 1024
        cli-memory: 512
        queue-concurrency: 50
        runtime: php-8.1:al2
        timeout: 120
        queue-memory: 1024
        queue-timeout: 300
        domain: expiryinventory-api.thelemoncode.com
        storage: expiry-inventory-public
        build:
            - 'composer install --no-dev'
            - 'php artisan optimize'
        deploy:
            - 'php artisan migrate'
    staging:
        memory: 1024
        cli-memory: 512
        runtime: php-8.1:al2
        timeout: 30
        queue-timeout: 120
        domain: expiryinventory-staging-api.thelemoncode.com
        storage: expiry-inventory-staging
        build:
            - 'composer install'
            - 'php artisan optimize'
        deploy:
            - 'php artisan migrate'
