# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/.next/
/out

/extensions/*/dist/*
shopify.app.toml
shopify.app.live.toml
shopify.app.staging.toml

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.live
.env.staging

npm-debug.log*
yarn-debug.log*
yarn-error.log*
