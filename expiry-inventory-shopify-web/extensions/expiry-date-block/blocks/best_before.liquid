{% if request.design_mode %}
  <!-- This will only render in the theme editor -->
  <span style="color:{{ block.settings.colour }}">
    {% render 'best_before', best_before_label: block.settings.best_before_label, best_before_date: '1900-01-01', date_format: block.settings.date_format %}
  </span>
{% else %}
  {% if shop.metafields.thelemoncode.show_expire_at.value == true %}
    {% render "dynamic-variant-metafield", product: product, selected_variant: product.selected_or_first_available_variant, updateHTMLSelectors : '.lemoncode-best-before-date', namespace: "thelemoncode", key: "best_before", label: block.settings.best_before_label, date_format: block.settings.date_format %}
    {% assign best_before_date = block.settings.product.selected_or_first_available_variant.metafields.thelemoncode.best_before.value %}
    <span class="lemoncode-best-before-date" style="color:{{ block.settings.colour }}">
      {% unless best_before_date == blank %}
        {% render 'best_before', best_before_label: block.settings.best_before_label, best_before_date: best_before_date, date_format: block.settings.date_format %}
      {% endunless %}
    </span>
  {% endif %}
{% endif %}

{% schema %}
{
  "name": "Best Before Date",
  "target": "section",
  "settings": [
    { "type": "product", "id": "product", "label": "Product", "autofill": true },
    { "type": "text", "id": "best_before_label", "label": "Best Before Label", "default": "Best Before Date" },
    { "type": "text", "id": "date_format", "label": "Date Format", "default": "%Y/%m/%d", "info": "Best before date format your customer will see in your store. [Build your own date format here](https://strftime.net/)" },
    { "type": "color", "id": "colour", "label": "Text Colour", "default": "#121212" }
  ]
}
{% endschema %}