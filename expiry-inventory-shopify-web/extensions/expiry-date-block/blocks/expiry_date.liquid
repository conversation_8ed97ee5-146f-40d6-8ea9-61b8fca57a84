{% if request.design_mode %}
  <!-- This will only render in the theme editor -->
  <span style="color:{{ block.settings.colour }}">
    {% render 'expiry', expiry_label: block.settings.expiry_label, expiry_date: '1900-01-01', date_format: block.settings.date_format %}
  </span>
{% else %}
  {% if shop.metafields.thelemoncode.show_expire_at.value == true %}
    {% render "dynamic-variant-metafield", product: product, selected_variant: product.selected_or_first_available_variant, updateHTMLSelectors : '.lemoncode-expiry-date', namespace: "thelemoncode", key: "expire_at", label: block.settings.expiry_label, date_format: block.settings.date_format %}
    {% assign expiry_date = block.settings.product.selected_or_first_available_variant.metafields.thelemoncode.expire_at.value %}
    <span class="lemoncode-expiry-date" style="color:{{ block.settings.colour }}">
      {% unless expiry_date == blank %}
        {% render 'expiry', expiry_label: block.settings.expiry_label, expiry_date: expiry_date, date_format: block.settings.date_format %}
      {% endunless %}
      </span>
  {% endif %}
{% endif %}

{% schema %}
{
  "name": "Expiry Date",
  "target": "section",
  "settings": [
    { "type": "product", "id": "product", "label": "Product", "autofill": true },
    { "type": "text", "id": "expiry_label", "label": "Expiry Label", "default": "Expiry Date" },
    { "type": "text", "id": "date_format", "label": "Date Format", "default": "%Y/%m/%d", "info": "Expiry date format your customer will see in your store. [Build your own date format here](https://strftime.net/)" },
    { "type": "color", "id": "colour", "label": "Text Colour", "default": "#121212" }
  ]
}
{% endschema %}

