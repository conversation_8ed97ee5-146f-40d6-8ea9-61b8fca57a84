{% comment %}
  \*****Copyrights DigiBrew (digibrew.io)*****\
{% endcomment %}
{% if updateHTMLSelectors %}
  <script>
    if(typeof variantMetafieldValues === 'undefined') {
      var variantMetafieldValues = {};
    }
  </script>
{% endif %}
{% for variant in product.variants %}
  {% if updateHTMLSelectors %}
    <script>
      if(typeof variantMetafieldValues[{{ variant.id }}] === 'undefined')
        variantMetafieldValues[{{ variant.id }}] = {}
      
      if(typeof variantMetafieldValues[{{ variant.id }}]["{{ namespace }}"] === 'undefined')
        variantMetafieldValues[{{ variant.id }}]["{{ namespace }}"] = {}
      
      if(typeof variantMetafieldValues[{{ variant.id }}]["{{ namespace }}"]["{{ key }}"] === 'undefined')
        variantMetafieldValues[{{ variant.id }}]["{{ namespace }}"]["{{ key }}"] = {}
          
      if(typeof variantMetafieldValues[{{ variant.id }}]["{{ namespace }}"]["{{ key }}"]["selectors"] === 'undefined')
        variantMetafieldValues[{{ variant.id }}]["{{ namespace }}"]["{{ key }}"]["selectors"] = [`{{ updateHTMLSelectors }}`]
      else
        variantMetafieldValues[{{ variant.id }}]["{{ namespace }}"]["{{ key }}"]["selectors"].push(`{{ updateHTMLSelectors }}`)
        
      variantMetafieldValues[{{ variant.id }}]["{{ namespace }}"]["{{ key }}"]["value"] = `{{ variant.metafields[namespace][key] }}` ? "<span>{{ label }}: {{ variant.metafields[namespace][key] | date: date_format }}</span>" : "";
    </script>
  {% endif %}
{% endfor %}

<script>
  if(!variantDynamicMetafieldScriptAlreadyAdded) {
    var variantDynamicMetafieldScriptAlreadyAdded = true;
    
    document.addEventListener('DOMContentLoaded', function() {
      function usePushState(handler) {
        // modern themes use pushstate to track variant changes without reload
        function track (fn, handler, before) {
          return function interceptor () {
            if (before) {
              handler.apply(this, arguments);
              return fn.apply(this, arguments);
            } else {
              var result = fn.apply(this, arguments);
              handler.apply(this, arguments);
              return result;
            }
          };
        }
    
        var currentVariantId = null;
        function variantHandler () {
          var selectedVariantId = window.location.search.replace(/.*variant=(\d+).*/, '$1');
          if(!selectedVariantId) return;
          if(selectedVariantId != currentVariantId) {
            currentVariantId = selectedVariantId;
            handler(selectedVariantId);
          }
        }
      
        // Assign listeners
        window.history.pushState = track(history.pushState, variantHandler);
        window.history.replaceState = track(history.replaceState, variantHandler);
        window.addEventListener('popstate', variantHandler);
      }
    
      function updateSelectorVal (el, value) {
        el.innerHTML = value;
      }
    
      usePushState(function(variantId) {
        if(typeof variantMetafieldValues[variantId] !== 'undefined') {
          for (namespace_index in variantMetafieldValues[variantId]) {
            const namespaces = variantMetafieldValues[variantId][namespace_index];
            
            for (key_index in namespaces) {
              const key = namespaces[key_index];
              for (let el of document.querySelectorAll(key.selectors[0])){
                updateSelectorVal(el, key.value);
              }
            }
          }
        }
      });
    });
  }
</script>