import React, { useState } from 'react'

import { Tile, reactExtension, useApi } from '@shopify/ui-extensions-react/point-of-sale'
import { fetchAPI } from './helpers';

const TileComponent = () => {
  const api = useApi<'pos.home.tile.render'>();
  const [refreshItems, setRefreshItems] = useState<Array<{ variantId: number | undefined; quantity: number; } | undefined>>([]);
  const { getSessionToken } = api.session;

  api.cart.subscribable.subscribe((cart) => {
    if (cart.lineItems.length) {
      const lineItems = cart.lineItems.map(({ variantId, quantity }) => ({ variantId, quantity }));
      if (JSON.stringify(refreshItems) != JSON.stringify(lineItems)) {
        getSessionToken().then((newToken) => {
          if (newToken) {
            setRefreshItems(lineItems);
            setTimeout(() => {
              fetchAPI("POST", "/inventory_batch/nearest-batch", newToken, { line_items: lineItems })
                .then((data: any) => {
                  if (data.inventory_batches && data.inventory_batches.length) {
                    const updates = data.inventory_batches.map(({ variant_id, inventory_batches }: { variant_id: string | number; inventory_batches: Array<{ id: string | number; name: string; expiry_date: string; quantity: number; }> | null; }) => {
                      if (inventory_batches) {
                        const properties: Record<string, string> = {};
                        inventory_batches.forEach((inventory_batch: any) => {
                          properties[`#${inventory_batch.id} Batch`] = inventory_batch.name ?? `Batch #${inventory_batch.id}`;
                          properties[`#${inventory_batch.id} Exp`] = inventory_batch.expiry_date;
                          properties[`#${inventory_batch.id} BestBefore`] = inventory_batch.best_before_date.join(", ");
                          properties[`#${inventory_batch.id} Qty`] = inventory_batch.quantity.toString();
                        });
                        return {
                          lineItemUuid: cart.lineItems.find(({ variantId }) => variantId == variant_id)?.uuid,
                          properties: properties
                        }
                      }
                      return null;
                    }).filter((v: any) => !!v);
                    api.cart.bulkAddLineItemProperties(updates);
                  }
                })
                .catch((error: any) => {
                  console.error(error);
                });
            }, 1000);
          }
        });
      }
    }
  });

  return (
    <Tile
      title="Zesty Expiry Inventory Tracker"
      subtitle="Inventory Batches Tracker"
      enabled
    />
  )
}

export default reactExtension('pos.home.tile.render', () => {
  return <TileComponent />
})