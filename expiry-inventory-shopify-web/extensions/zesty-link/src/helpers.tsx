const headers: HeadersInit = {
    Accept: "application/json",
    "Content-Type": "application/json"
};

const app_env = process.env.NEXT_PUBLIC_APP_ENV;
const backend_url = process.env.NEXT_PUBLIC_BACKEND_URL;

export const fetchAPI = (method: string, path: string, session_token: string, data?: any) => {
    return new Promise(async (resolve, reject) => {
        if(method === "GET") {
            if(data) {
                path += "?";
                path += Object.keys(data).map(key => key + "=" + data[key]).join("&");
                data = undefined;
            }
        }
        const request_headers = {
            ...headers,
            Authorization: `Bearer ${session_token}`,
            ...(app_env == "dev" ? { "ngrok-skip-browser-warning": "1", "User-Agent": "Bypass-Ngrok" } : {})
        };
        const request = new Request(backend_url + path, {
            method,
            body: JSON.stringify(data),
            headers: request_headers
        });
        fetch(request)
            .then(async (response_data) => {
                const response_json = await response_data.json();
                if(!response_json.code_status || (response_json.code_status && response_json.code_status > 200)) {
                    reject(response_json);
                }
                resolve(response_json.data);
            })
            .catch((error) => {
                reject({
                    message: error
                });
            });
    });
}