// next.config.js
const removeImports = require("next-remove-imports")();
module.exports = removeImports({
  experimental: { esmExternals: true },
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  async headers() {
    return [
      {
        source: '/:path*',
        has: [
          {
            type: 'query',
            key: 'shop',
            value: '(?<shop>.*).myshopify.com',
          },
        ],
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `frame-ancestors https://:shop.myshopify.com https://admin.shopify.com;`
          }
        ],
      },
    ]
  }
});