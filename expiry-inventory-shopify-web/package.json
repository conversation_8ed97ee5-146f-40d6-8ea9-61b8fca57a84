{"name": "expiry-inventory-app", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^3.3.4", "@progress/kendo-drawing": "^1.10.1", "@progress/kendo-licensing": "^1.2.0", "@progress/kendo-react-pdf": "^5.11.0", "@reduxjs/toolkit": "^1.6.0", "@shopify/app": "^3.56.3", "@shopify/app-bridge": "^3.7.3", "@shopify/app-bridge-react": "^3.7.3", "@shopify/app-bridge-utils": "^3.2.5", "@shopify/cli": "^3.56.3", "@shopify/polaris": "^10.32.0", "@shopify/polaris-icons": "^4.5.0", "@shopify/react-i18n": "^6.4.0", "@shopify/ui-extensions": "2025.1", "@shopify/ui-extensions-react": "2025.1", "@testing-library/jest-dom": "^5.12.0", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@uiw/react-textarea-code-editor": "^1.4.4", "axios": "^1.3.4", "classnames": "^2.3.1", "cookie": "^0.5.0", "crypto-js": "^4.2.0", "env-cmd": "^10.1.0", "js-cookie": "^3.0.1", "moment": "^2.29.1", "next": "^13.1.6", "next-redux-wrapper": "^8.1.0", "next-remove-imports": "^1.0.10", "next-seo": "^6.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-flatpickr": "^3.10.7", "react-hook-form": "^7.51.4", "react-liquid": "^2.0.1", "react-query": "^3.39.3", "react-redux": "^7.2.4", "react-router": "^5.2.0", "react-router-guards": "^1.0.2", "react-use": "^17.4.0", "recharts": "^2.0.9", "redux": "^4.1.0", "redux-persist": "^6.0.0", "web-vitals": "^3.4.0", "yup": "^1.4.0"}, "scripts": {"shopify": "shopify", "dev": "next dev", "build": "next build", "start": "next start", "deploy": "shopify app deploy"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"eqeqeq": "off"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/cookie": "^0.5.1", "@types/crypto-js": "^4.1.1", "@types/js-cookie": "^3.0.3", "@types/lodash": "^4.17.15", "@types/react-flatpickr": "^3.8.8", "autoprefixer": "^10.4.5", "postcss": "^8.3.11", "tailwindcss": "^2.2.17", "typescript": "4.9.5"}, "overrides": {"@types/react": "17.0.30"}}