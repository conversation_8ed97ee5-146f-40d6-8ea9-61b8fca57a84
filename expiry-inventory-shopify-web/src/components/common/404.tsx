const ErrorInformation: React.FC<{
    code?: number;
    status?: string;
    message?: string;
}> = ({
    code = 404,
    status = "Not Found",
    message = "The page you trying to access is unavailable"
}) => {
  return (
        <div
            className="text-center px-12 py-16 sm:py-20 lg:py-24 xl:py-32 flex items-center justify-center bg-cover bg-no-repeat bg-center"
        >
            <div className="max-w-md xl:max-w-lg">
                <h2 className="text-2xl md:text-4xl 2xl:text-5xl font-bold text-skin-base pt-5 xl:pt-9">
                    {code} {status}
                </h2>
                <p className="text-15px md:text-base 2xl:text-[18px] leading-7 md:leading-8 pt-4 font-medium">
                    {message}
                </p>
            </div>
        </div>
    );
};

export default ErrorInformation;
