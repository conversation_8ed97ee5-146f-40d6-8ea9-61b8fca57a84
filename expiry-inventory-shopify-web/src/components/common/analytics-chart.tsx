import moment from "moment";
import { useI18n } from '@shopify/react-i18n';
import { sumArray } from "@utils/helpers";
import AnalyticsBarChartCard from "@components/ui/cards/analytics-bar-chart-card";
import { useAnalyticsQuery } from "@data/dashboard/use-analytics.query";

interface ChartDataByDate {
    [key: string]: { quantity: number; cost: number; };
}

const AnalyticsCharts = ({ status, days, currency = "USD" }: {
    status: {
        name: string;
        value: string;
    };
    days: number;
    currency: string;
}) => {
    const [i18n] = useI18n();
    const {
        data,
        isLoading
    } = useAnalyticsQuery({
        type: status.value,
        days
    });

    const genData = data ? generateChartData(data) : [];

    function generateChartData(data_by_dates: ChartDataByDate[]) {
        let current = null;
        let data = [];
        if(status.value == "expired" || status.value == "best_before") {
            current = moment().subtract(days - 1, "days");
        }
        else if(status.value == "expiring_soon" || status.value == "upcoming_best_before") {
            current = moment().add(1, "day");
        }
        else {
            return [];
        }
        for(let day = 1; day <= days; day++) {
            //@ts-ignore
            const current_data = data_by_dates[current.format("YYYY-MM-DD")];
            data.push({
                Date: current.format("MMM D, YYYY"),
                Quantity: (current_data && current_data.quantity) ? current_data.quantity : 0,
                Cost: (current_data && current_data.cost) ? current_data.cost : 0,
            });
            current.add(1, "day");
        }
        return data;
    }

    return (
        <>
            <AnalyticsBarChartCard
                summaryTitle={`Total retail cost of products ${status.name}`}
                summaryText={i18n.formatCurrency(sumArray(genData!, "Cost"), { currency, form: 'short' })}
                chartTitle={`Retail cost of products ${status.name}`}
                yKey="Cost"
                data={genData!}
                color={status.value === "expired" ? "#EB0303" : "#008060"}
                loading={isLoading}
            />
            <AnalyticsBarChartCard
                summaryTitle={`Total quantity of products ${status.name}`}
                summaryText={sumArray(genData!, "Quantity")}
                chartTitle={`Quantity of products ${status.name}`}
                yKey="Quantity"
                data={genData!}
                color={status.value === "expired" ? "#EB0303" : "#008060"}
                loading={isLoading}
            />
        </>
    );
}

export default AnalyticsCharts;