import { ShopifyText } from "@components/ui/shopify/text";
import { Icon } from "@shopify/polaris";
import { ChevronRightMinor, TickMinor } from "@shopify/polaris-icons";
import cn from "classnames";

interface ChecklistItemProps {
    text: string;
    className?: string;
    variant?: "tick" | "chevron";
}

export default function ChecklistItem({ text, className = "", variant = "tick" }: ChecklistItemProps) {
    return (
        <div className={cn("flex mb-2", className)}>
            <span className="mr-2">
                {variant == "tick" ? <Icon source={TickMinor} color="primary" /> : <></>}
                {variant == "chevron" ? <Icon source={ChevronRightMinor} color="primary" /> : <></>}
            </span>
            <ShopifyText fontWeight="semibold" variant="bodyMd" as="p">{text}</ShopifyText>
        </div>
    );
}