import React, { useEffect } from "react";
import { TextField } from "@shopify/polaris";
import Flatpickr from "react-flatpickr";

import { formatDate } from "@utils/helpers";

interface DatePickerProps {
    date: Date | null;
    disabled?: boolean;
    onDateChange?: any;
    inputLabel?: string;
}

const DatePicker = ({ date, disabled = false, onDateChange, inputLabel = "" }: DatePickerProps) => {
    const [current_date, setCurrentDate] = React.useState<Date | null>();

    useEffect(() => {
        setCurrentDate(date);
    }, [date]);

    if(disabled) {
        return (<TextField autoComplete="off" disabled helpText="This batch has expired" type="text" label={inputLabel} value={formatDate(current_date)} />)
    }

    return (
        <Flatpickr
            onChange={changed_date => {
                onDateChange(changed_date[0]);
            }}
            render={
                ({}, ref) => {
                    return (
                        <div ref={ref}>
                            <TextField autoComplete="off" type="text" label={inputLabel} value={formatDate(current_date)} />
                        </div>
                    );
                }
            }
        />
    );
}

export default DatePicker;