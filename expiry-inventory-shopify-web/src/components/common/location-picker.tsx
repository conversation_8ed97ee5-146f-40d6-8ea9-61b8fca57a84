import React, { useState } from "react";
import { ActionList, Button, Popover } from "@shopify/polaris";
import { LocationMajor } from "@shopify/polaris-icons";
import { useUI } from "@contexts/ui.context";
import { Location } from "@ts-types/types";

const LocationPicker = () => {
    const { location: selected_location, locations, setLocation } = useUI();
    const [active, setActive] = useState(false);

    function toggleActive() {
        setActive(!active);
    }

    function locationChange(_location: Location) {
        setLocation(_location);
    }

    return (
        <Popover
            active={active}
            activator={
                <Button
                    icon={LocationMajor}
                    onClick={() => toggleActive()}
                    disclosure
                >
                    {selected_location ? selected_location.name : "Location"}
                </Button>
            }
            onClose={() => toggleActive()}
        >
            <ActionList
                items={
                    (locations && locations.length > 0) ? (
                        locations.map((location: Location) => {
                            return {
                                content: location.name,
                                onAction: () => {
                                    locationChange(location);
                                    toggleActive();
                                }
                            };
                        })
                    ) : <></>
                }
            />
        </Popover>
    )
}

export default LocationPicker;