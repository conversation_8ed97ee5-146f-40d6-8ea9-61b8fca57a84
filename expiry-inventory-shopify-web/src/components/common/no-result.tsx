import { ShopifyCard } from "@components/ui/shopify/card";
import { ShopifyText } from "@components/ui/shopify/text";
import { Button } from "@shopify/polaris";

interface NoResultProps {
    is_filtering?: number;
    onResetButtonClicked?: any;
}

function NoResult({ is_filtering, onResetButtonClicked }: NoResultProps) {
    return (
        <ShopifyCard.Section>
            <div className="text-center">
                <img className="mx-auto mb-2" src="/assets/no-result.png" alt="No result" />
                <ShopifyText variant="headingLg" as="h1">No result found</ShopifyText>
                {
                    !!is_filtering ? (
                        <div className="mt-2">
                            <Button primary onClick={() => onResetButtonClicked()}>Reset filters</Button>
                        </div>
                    ) : <></>
                }
            </div>
        </ShopifyCard.Section>
    );
}

export default NoResult;