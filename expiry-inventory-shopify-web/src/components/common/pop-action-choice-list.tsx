import { useState } from "react";
import { But<PERSON>, ChoiceList, Popover } from "@shopify/polaris";

interface PopActionChoiceListProps {
    options: {
        value: string;
        label: string;
        field: string;
        order: string;
    }[];
    data_display: string;
    buttonIcon?: any;
    current_value: string[];
    setData: any;
}

function PopActionChoiceList({ options, data_display, buttonIcon = null, current_value, setData }: PopActionChoiceListProps) {
    const [active, setActive] = useState(false);

    function toggleActive() {
        setActive(!active);
    }

    return (
        <Popover
            active={active}
            activator={
                <Button
                    onClick={toggleActive}
                    disclosure
                    {...(buttonIcon ? { icon: buttonIcon } : {})}
                >
                    {data_display}
                </Button>
            }
            onClose={toggleActive}
        >
            <div className="px-4 py-6">
                <ChoiceList
                    title="Sort by"
                    choices={options}
                    selected={current_value}
                    onChange={(value) => {
                        setData(value);
                        toggleActive();
                    }}
                />
            </div>
        </Popover>
    );
}

export default PopActionChoiceList;