import { useState } from "react";
import { ActionList, Button, Popover } from "@shopify/polaris";

interface PopActionListProps {
    options: {
        name: string;
        value: string | number;
    }[];
    contentName: "name" | "value";
    data_display: string;
    buttonIcon?: any;
    setData: any;
}

function PopActionList({options, contentName, data_display, buttonIcon = null, setData}: PopActionListProps) {
    const [active, setActive] = useState(false);

    function toggleActive() {
        setActive(!active);
    }

    return (
        <Popover
            active={active}
            activator={
                <Button
                    onClick={toggleActive}
                    disclosure
                    {...(buttonIcon ? { icon: buttonIcon } : {})}
                >
                    {/* @ts-ignore */}
                    <span className="capitalize">{data_display}</span>
                </Button>
            }
            onClose={toggleActive}
        >
            <ActionList
                //@ts-ignore
                items={
                    (options && options.length > 0)
                    ? (
                        options.map((option) => {
                            return {
                                content: <span className="capitalize">{option[contentName] as string}</span>,
                                onAction: () => {
                                    setData(option);
                                    toggleActive();
                                }
                            };
                        })
                    )
                    : []
                }
            />
        </Popover>
    );
}

export default PopActionList;