import { Icon, TextField } from "@shopify/polaris"
import { SearchMajor } from "@shopify/polaris-icons"

interface SearchProps {
    value: string;
    onChange: any;
    placeholder?: string;
    showClear?: boolean;
}

export default function Search({ value, onChange, placeholder = "Search", showClear = false }: SearchProps) {
    return (
        <TextField
            prefix={<Icon source={SearchMajor} color="base" />}
            inputMode="search"
            autoComplete="search"
            value={value}
            label=""
            onChange={onChange}
            placeholder={placeholder}
            clearButton={showClear}
            onClearButtonClick={() => onChange("")}
        />
    );
}