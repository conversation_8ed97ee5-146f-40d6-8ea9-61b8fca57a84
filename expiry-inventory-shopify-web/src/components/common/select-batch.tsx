import React, { useState, useCallback, useMemo, useEffect } from "react";
import {
  Combobox,
  Listbox,
  Icon,
  Spinner,
  InlineError,
} from "@shopify/polaris";

import { useUI } from "@contexts/ui.context";
import { InventoryBatch } from "@ts-types/types";
import { useFormContext } from "react-hook-form";
import { useInventoryBatchesQuery } from "@data/inventory_batch/use-inventory-batches.query";
import debounce from "lodash/debounce";

const SelectBatch = ({
  name,
  label,
  onObjectChange,
  value,
  filter_product_id,
  filter_location_id,
  default_select_first,
  ignore_id,
  error,
  ...rest
}: {
  name: string;
  label: string;
  onObjectChange?: (e?: InventoryBatch) => void;
  filter_product_id?: string | number;
  filter_location_id?: string | number;
  default_select_first?: boolean;
  ignore_id?: string | number;
  error?: string;
  [key: string]: any;
}) => {
  const [selectedOption, setSelectedOption] = useState<string>();
  const [inputValue, setInputValue] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const { setValue, watch } = useFormContext();

  const { data, isLoading, refetch } = useInventoryBatchesQuery({
    query: {
      product_id: filter_product_id,
      location: filter_location_id,
      search: debouncedSearchTerm,
    },
  });

  const deselectedOptions = useMemo(() => {
    if (!data?.data) return [];
    return data.data
      .filter((batch) => batch.id != ignore_id)
      .map((batch) => ({
        value: batch.id.toString(),
        label: batch.name ? `${batch.name}` : `Batch #${batch.id}`,
      }));
  }, [data, ignore_id]);

  const [options, setOptions] = useState(deselectedOptions);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchTerm: string) => {
      setDebouncedSearchTerm(searchTerm);
    }, 300),
    []
  );

  // Effect to handle input value changes
  useEffect(() => {
    debouncedSearch(inputValue);
  }, [inputValue, debouncedSearch]);

  // Effect to update options when data changes
  useEffect(() => {
    setOptions(deselectedOptions);
  }, [deselectedOptions]);

  // Effect to handle initial value and default selection
  useEffect(() => {
    if (default_select_first && deselectedOptions.length > 0) {
      const firstOption = deselectedOptions[0];
      setSelectedOption(firstOption.value);
      setInputValue(firstOption.label);
      setValue(name, parseInt(firstOption.value));
      onObjectChange && onObjectChange(getBatch(parseInt(firstOption.value)));
    } else if (value) {
      const matchedOption = deselectedOptions.find(
        (option) => option.value === value.toString()
      );
      if (matchedOption) {
        setSelectedOption(matchedOption.value);
        setInputValue(matchedOption.label);
        setValue(name, parseInt(matchedOption.value));
        onObjectChange &&
          onObjectChange(getBatch(parseInt(matchedOption.value)));
      }
    }
  }, [deselectedOptions, value, default_select_first]);

  const updateText = useCallback(
    (value: string) => {
      // Clear selection if search text is modified
      if (value !== inputValue) {
        setSelectedOption(undefined);
        setValue(name, undefined);
        onObjectChange && onObjectChange(undefined);
      }
      setInputValue(value);
    },
    [inputValue, setValue, name, onObjectChange]
  );

  const updateSelection = useCallback(
    (selected: string) => {
      const matchedOption = options.find((option) => {
        return option.value === selected;
      });

      if (matchedOption) {
        setSelectedOption(selected);
        setInputValue(matchedOption.label);
        setValue(name, parseInt(selected));
        onObjectChange && onObjectChange(getBatch(parseInt(selected)));
      }
    },
    [options, setValue, name, onObjectChange]
  );

  function getBatch(id?: number | string) {
    return data?.data?.find((batch) => batch.id == id);
  }

  const loadingMarkup = isLoading ? (
    <Listbox.Loading accessibilityLabel="Loading" />
  ) : null;

  const optionsMarkup =
    options.length > 0
      ? options.map((option) => {
          const { label, value } = option;
          return (
            <Listbox.Option
              key={value}
              value={value}
              selected={selectedOption === value}
              accessibilityLabel={label}
            >
              {label}
            </Listbox.Option>
          );
        })
      : null;

  return (
    <div>
      <Combobox
        activator={
          <Combobox.TextField
            onChange={updateText}
            label={label}
            value={inputValue}
            placeholder="Search batches"
            autoComplete="off"
          />
        }
      >
        {options.length > 0 ? (
          <Listbox onSelect={updateSelection}>
            {!isLoading ? optionsMarkup : loadingMarkup}
          </Listbox>
        ) : null}
      </Combobox>
      {error && <InlineError message={error} fieldID={name} />}
    </div>
  );
};

export default SelectBatch;
