import React, { useEffect, useState } from "react";
import { Select, SelectOption, Spinner } from "@shopify/polaris";
import { useUI } from "@contexts/ui.context";
import { Location } from "@ts-types/types";
import { useFormContext } from "react-hook-form";
import { disable } from "@shopify/app-bridge/actions/LeaveConfirmation";
import { useLocationsQuery } from "@data/location/use-locations.query";

const SelectLocation = ({
  name,
  label,
  ignore_id,
  default_select_first,
  filter_product_id,
  onObjectChange,
  ...rest
}: {
  name: string;
  label: string;
  ignore_id?: string | number;
  default_select_first?: boolean;
  onObjectChange?: (e?: Location) => void;
  [key: string]: any;
}) => {
  const { data, isLoading } = useLocationsQuery({
    query: filter_product_id
      ? {
          product_id: filter_product_id,
        }
      : undefined,
  });

  const { setValue, watch } = useFormContext();
  const selectedValue = watch(name);
  useEffect(() => {
    if (default_select_first) {
      if (!selectedValue) {
        onObjectChange && onObjectChange(data?.[0]);
        setValue(name, data?.[0].id);
      }
    }
  }, [data]);

  if (isLoading) return <Spinner size="small" accessibilityLabel="loading" />;
  return (
    <Select
      label={label}
      {...rest}
      onChange={(value) => {
        const location = data?.find(
          (location: Location) => location.id == value
        );
        onObjectChange && onObjectChange(location);
        setValue(name, typeof value == "string" ? parseInt(value) : value);
      }}
      value={selectedValue ?? ""}
      options={(data ?? [])
        .filter((location: Location) => location.id != ignore_id)
        .map((location: Location) => {
          return {
            label: location.name,
            value: location.id,
          };
        }) as SelectOption[]}
    />
  );
};

export default SelectLocation;
