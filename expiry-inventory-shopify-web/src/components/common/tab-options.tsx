import React from "react";

interface TabOptionsProps {
    options: {
        label: string;
    }[];
    selected: number;
    onChange: any;
}

const TabOptions = (props: TabOptionsProps) => {
    const tab_options = props.options;
    const selected_option_index = props.selected;

    return (
        <div className="Polaris-Tabs__Wrapper border-0">
            <ul role="tablist" className="Polaris-Tabs">
                {
                    tab_options.map((option, index) => {
                        return (
                            <li key={index} className="Polaris-Tabs__TabContainer">
                                <a className={`Polaris-Tabs__Tab ${selected_option_index === index ? "Polaris-Tabs__Tab--selected" : ""}`} onClick={() => props.onChange(index)}>
                                    <span className="Polaris-Tabs__Title">{option.label}</span>
                                </a>
                            </li>
                        )
                    })
                }
            </ul>
        </div>
    )
}

export default TabOptions;