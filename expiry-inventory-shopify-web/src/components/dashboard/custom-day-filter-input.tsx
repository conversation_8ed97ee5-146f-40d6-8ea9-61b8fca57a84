import { Button, TextField } from "@shopify/polaris";
import { useState } from "react";
import {
  EditMajor
} from '@shopify/polaris-icons';

interface CustomDayFilterInputProps {
  current_day: string | number;
  onSave: any;
}

const CustomDayFilterInput: React.FC<CustomDayFilterInputProps> = ({
  current_day,
  onSave
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [dayFilter, setDayFilter] = useState(current_day);
  function handleSave() {
    onSave(dayFilter);
    setIsEditing(false);
  }
  return (
    <div className="flex items-center justify-between gap-1">
      <div className="w-24">
        <TextField
          label=""
          labelHidden
          autoComplete="off"
          suffix="days"
          inputMode="numeric"
          disabled={!isEditing}
          value={String(dayFilter)}
          onChange={setDayFilter}
        ></TextField>
      </div>
      <div>
        {isEditing ? (
          <Button
            primary
            onClick={() => handleSave()}
          >
            Set
          </Button>
        ) : (
          <Button
            plain
            onClick={() => setIsEditing(true)}
            icon={<EditMajor width={14} />}
          />
        )}
      </div>
    </div>
  );
}

export default CustomDayFilterInput;