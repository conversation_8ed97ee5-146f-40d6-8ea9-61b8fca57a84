import React, { useEffect, useState } from "react";
import { FormLayout, Modal } from "@shopify/polaris";

import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { useExportAnalyticsDataMutation } from "@data/dashboard/use-export-analytics-data.mutation";
import { PLAN_TYPE_ENUM } from "@utils/constants";

interface ExportDashboardDataModalProps {
    type: string;
    days: number;
    active: boolean;
    onDismiss: any;
}

function ExportDashboardDataModal(props: ExportDashboardDataModalProps) {
    const [plans, setPlans] = useState<any | null>(null);
    const app = useAppBridge();
    const { mutate: exportAnalytics, isLoading: isExporting } = useExportAnalyticsDataMutation();
    const {
        data: settings,
    } = useSettingsQuery();

    useEffect(() => {
        if(props.active) {
            getPlans();
        }
    }, [props.active]);

    function getPlans() {
        fetchAPI("GET", API_ENDPOINTS.PLAN)
            .then((plan_settings: any) => {
                setPlans(plan_settings);
            });
    }

    function viewPlans() {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.PLANS);
    }

    function handleExportDashboardData() {
        const payload = {
            type: props.type,
            days: props.days,
        };
        exportAnalytics(payload, {
            onSuccess: () => {
                showToast(toastOptions.ANALYTICS_EXPORT_SUCCESS);
                closeModal();
            },
            onError: () => {
                showToast(toastOptions.ANALYTICS_EXPORT_FAILED);
            }
        });
    }

    function closeModal() {
        props.onDismiss();
    }

    return (
        <Modal
            open={props.active}
            onClose={closeModal}
            title="Export dashboard data"
            primaryAction={{
                content: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? "Export" : "View plans",
                loading: isExporting,
                disabled: isExporting,
                onAction: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? handleExportDashboardData : viewPlans,
            }}
            secondaryActions={[
                {
                    content: "Cancel",
                    onAction: closeModal,
                }
            ]}
        >
            <Modal.Section>
                <FormLayout>
                    {(settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? (
                        <div>
                            <p>Confirm your export</p>
                            <p className="capitalize">Type: {props.type.replaceAll("_", " ")}</p>
                            <p className="capitalize">Period: {props.days} days</p>
                        </div>
                    ) : (
                        <p>This feature requires a subscription to the {plans?.pro.name} plan. Please upgrade by following the link below.</p>
                    )}
                </FormLayout>
            </Modal.Section>
        </Modal>
    );
}

export default ExportDashboardDataModal;