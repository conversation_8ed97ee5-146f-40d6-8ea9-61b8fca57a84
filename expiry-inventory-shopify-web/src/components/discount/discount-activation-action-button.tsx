import { useModalAction } from "@components/ui/modal/modal.context";
import { useApproveDiscountMutation } from "@data/discount/use-approve-discount.mutation";
import { useCancelDiscountMutation } from "@data/discount/use-cancel-discount.mutation";
import { showToast, toastOptions } from "@utils/helpers";

const DiscountApproveButton: React.FC<{ id: string | number; onApproved: any; }> = ({ id, onApproved }) => {
    const { openModal, closeModal } = useModalAction();
    const { mutate: approveDiscount } = useApproveDiscountMutation(id);
    function handleDiscountApprove() {
        openModal(
            "DISCOUNT_APPROVAL_VIEW",
            { type: "approve", discount_id: id },
            {
                modalTitle: "Approve discount",
                confirmBtnText: "Approve",
                onConfirm: () => {
                    approveDiscount(undefined, {
                        onSuccess: () => {
                            showToast(toastOptions.DISCOUNT_ACTIVATED);
                            onApproved();
                        },
                        onSettled: () => {
                            closeModal();
                        }
                    });
                }
            }
        );
    }
    return (
        <a className="green-link hover:underline" onClick={handleDiscountApprove}>Approve</a>
    );
}

const DiscountRejectButton: React.FC<{ id: string | number; onRejected: any; }> = ({ id, onRejected }) => {
    const { openModal, closeModal } = useModalAction();
    const { mutate: cancelDiscount } = useCancelDiscountMutation(id);
    function handleDiscountReject() {
        openModal(
            "DISCOUNT_APPROVAL_VIEW",
            { type: "reject", discount_id: id },
            {
                modalTitle: "Cancel discount",
                confirmBtnText: "Confirm",
                onConfirm: () => {
                    cancelDiscount(undefined, {
                        onSuccess: () => {
                            showToast(toastOptions.DISCOUNT_CANCELLED);
                            onRejected();
                        },
                        onSettled: () => {
                            closeModal();
                        }
                    });
                }
            }
        );
    }
    return (
        <a className="delete-link hover:underline" onClick={handleDiscountReject}>Cancel</a>
    );
}

interface DiscountActivationActionButtonProps {
    discount_id: string | number;
    type: "approve" | "reject";
    onAction: any;
}

const DiscountActivationActionButton: React.FC<DiscountActivationActionButtonProps> = ({
    discount_id,
    type,
    onAction,
}) => {
    return (
        <>
            {type === "approve"
                ? <DiscountApproveButton id={discount_id} onApproved={onAction} />
                : <DiscountRejectButton id={discount_id} onRejected={onAction} />
            }
        </>
    )
}

export default DiscountActivationActionButton;