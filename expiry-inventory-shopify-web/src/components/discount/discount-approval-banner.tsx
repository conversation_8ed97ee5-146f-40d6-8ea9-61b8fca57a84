import { useState } from "react";
import { Banner } from "@shopify/polaris";
import { usePendingDiscountsCountQuery } from "@data/discount/use-pending-discounts-count.query";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { ROUTES } from "@utils/routes";

const DiscountApprovalBannerView: React.FC<{ count: number; }> = ({ count }) => {
    const app = useAppBridge();
    const [hide_banner, setHideBanner] = useState(false);

    function handleViewDiscounts() {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.DISCOUNTS);
    }
    
    return (
        !hide_banner ? (
            <Banner
                title="Action Required"
                action={{content: "View pending discounts", onAction: handleViewDiscounts}}
                status="info"
                onDismiss={() => { setHideBanner(true) }}
            >
                <p>{count} discount{count > 1 ? "s" : ""} pending for activation, activate discount(s) to adjust product to discounted price.</p>
            </Banner>
        ) : <></>
    );
}

export default function DiscountApprovalBanner() {
    const { data, isLoading } = usePendingDiscountsCountQuery();
    if(isLoading || !data) return <></>;
    return data.pending_discounts_count > 0 ? <DiscountApprovalBannerView count={data.pending_discounts_count} /> : <></>;
}