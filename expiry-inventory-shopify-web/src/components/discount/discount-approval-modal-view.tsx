import { useModalState } from "@components/ui/modal/modal.context";
import { Modal } from "@shopify/polaris";

interface DiscountApprovalProps {
    discount_id: string | number;
    type: "approve" | "reject";
}

const DiscountApproval: React.FC<DiscountApprovalProps> = ({
    discount_id,
    type,
}) => {
    return (
        <Modal.Section>
            {type == "approve" ? (
                <p>Performing this will activate the discount and adjust product to discounted price in Shopify.</p>
            ) : (
                <p>Are you sure to cancel this discount? This action cannot be undone.</p>
            )}
        </Modal.Section>
    );
}

const DiscountApprovalView: React.FC = () => {
    const { data } = useModalState();
    return (
        data ? <DiscountApproval type={data.type} discount_id={data.discount_id} /> : <></>
    );
}

export default DiscountApprovalView;