import { ModalContentLoader } from "@components/ui/modal/modal";
import { ShopifyText } from "@components/ui/shopify/text";
import { usePendingDiscountsQuery } from "@data/discount/use-pending-discounts.query";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { Modal, SkeletonBodyText } from "@shopify/polaris";
import { useI18n } from "@shopify/react-i18n";

const PendingDiscounts: React.FC<{ currency: string; }> = ({ currency }) => {
    const [i18n] = useI18n();
    const { data, isLoading, isRefetching, refetch } = usePendingDiscountsQuery();
    return (
        <>
            <Modal.Section>
                <p>All pending activation discounts will be listed here</p>
                <p><b>Approve</b> to apply discount on the product, this will be done automatically by Zesty Expiry Inventory Tracker by adjusting product price to discounted price in Shopify store.</p>
                <p><b>Reject</b> to cancel/deactivate the discount.</p>
                {/* <ShopifyText variant="headingMd" as="h6"><span className="capitalize">{status.name}</span> products {status.value != "discounted" ? "by quantity and retail cost" : ""}</ShopifyText>
                <p>A breakdown of {status.name} products {status.value == "discounted" ? "in inventory." : "with the greatest number of remaining inventory."}</p> */}
            </Modal.Section>
            <Modal.Section>
                <div className="py-4">
                    {(isLoading || isRefetching || !data)
                        ? <SkeletonBodyText lines={5}></SkeletonBodyText>
                        : <></>
                    }
                    {(!isLoading && !isRefetching && data && data.length <= 0)
                        ? <p>No pending discount available.</p>
                        : <></>
                    }
                    {(!isLoading && !isRefetching && data && data.length > 0) ? (
                        data.map((discount) => {
                            return (
                                <div className="flex pb-4 mb-4 border-b" key={discount.id}>
                                    <div>
                                        {/* <Link url={`${ROUTES.PRODUCTS}/${product.parent_id}/${product.shopify_variant_id}`}>{product.name}</Link> */}
                                    </div>
                                    <div className="ml-auto">
                                        <div className="text-right">
                                            <div className="mb-1">
                                                <ShopifyText variant="bodyMd" as="p">{i18n.formatCurrency(discount.discounted_price, { currency, form: 'short' })}</ShopifyText>
                                                <del className="text-xs">{i18n.formatCurrency(discount.product.original_price, { currency, form: 'short' })}</del>
                                            </div>
                                            <div className="mb-1">
                                                <ShopifyText variant="bodyMd" color="subdued" as="p">Quantity: {discount.product.quantity}</ShopifyText>
                                            </div>
                                            <div>
                                                <ShopifyText variant="bodyMd" as="p">Total: {i18n.formatCurrency((discount.product.price * (discount.product.quantity as number)), { currency, form: 'short' })}</ShopifyText>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })
                    ) : <></>}
                </div>
            </Modal.Section>
        </>
    );
}

const DiscountPendingView: React.FC = () => {
    const {
        data: settings,
        isLoading
    } = useSettingsQuery();
    if(isLoading || !settings) return <ModalContentLoader />;
    return <PendingDiscounts currency={settings.shop.currency} />
}

export default DiscountPendingView;