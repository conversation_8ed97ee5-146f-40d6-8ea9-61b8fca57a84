import React from "react";
import { <PERSON><PERSON>, DataTable, Modal, TextField } from "@shopify/polaris";
import { useModalAction, useModalState } from "@components/ui/modal/modal.context";
import { FormProvider, useForm } from "react-hook-form";
import { useInventoryBatchVerificationsQuery } from "@data/inventory_batch/use-inventory-batch-verifications.query";
import { useCreateInventoryBatchVerificationMutation } from "@data/inventory_batch/use-create-inventory-batch-verification.mutation";
import { formatDate, renderErrors, showToast, toastOptions } from "@utils/helpers";

interface FormValues {
  adjusted_by: string;
  quantity: string;
}

const BatchVerificationModalView: React.FC = () => {
  const { data } = useModalState();
  const { closeModal } = useModalAction();
  const batch_id = data?.batch_id as (string | number);

  const { data: verifications, isLoading, refetch } = useInventoryBatchVerificationsQuery(batch_id);
  const { mutate: createVerification, isLoading: creating } = useCreateInventoryBatchVerificationMutation();

  const methods = useForm<FormValues>({
    defaultValues: { adjusted_by: "", quantity: "" },
  });

  function onSubmit(values: FormValues) {
    const payload = {
      inventory_batch_id: batch_id,
      adjusted_by: values.adjusted_by,
      quantity: parseInt(values.quantity as string, 10),
    };
    createVerification(payload, {
      onSuccess: () => {
        showToast({ message: "Verification added", duration: 2000, isError: false });
        methods.reset({ adjusted_by: "", quantity: "" });
        refetch();
      },
      onError: (response: any) => {
        showToast({ message: renderErrors(response?.data) ?? "Failed to add verification", duration: 2000, isError: true });
      },
    });
  }

  return (
    <>
      <Modal.Section>
        <div className="flex items-center mb-4">
          <h4 className="text-base font-semibold">Verified quantities</h4>
          <div className="ml-auto">
            <Button primary onClick={methods.handleSubmit(onSubmit)} loading={creating}>
              + Add verification
            </Button>
          </div>
        </div>
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)}>
            <div className="flex gap-4 mb-4">
              <div className="w-1/2">
                <TextField
                  label="User/Staff name"
                  value={methods.watch("adjusted_by")}
                  onChange={(v) => methods.setValue("adjusted_by", v)}
                  autoComplete="off"
                />
              </div>
              <div className="w-1/2">
                <TextField
                  label="Quantity"
                  type="number"
                  value={methods.watch("quantity")}
                  onChange={(v) => methods.setValue("quantity", v)}
                  autoComplete="off"
                />
              </div>
            </div>
          </form>
        </FormProvider>
        <div>
          <DataTable
            columnContentTypes={["text", "numeric", "text"]}
            headings={["Date", "Quantity", "User"]}
            rows={(verifications ?? []).map((v) => [
              formatDate(v.created_at, "MMM D, YYYY [at] h:mm A"),
              v.quantity,
              v.adjusted_by,
            ])}
          />
        </div>
      </Modal.Section>
    </>
  );
};

export default BatchVerificationModalView;

