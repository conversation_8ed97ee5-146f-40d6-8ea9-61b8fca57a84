import { useModalAction } from "@components/ui/modal/modal.context";
import { useCheckMerchantPasswordSetupQuery } from "@data/merchant/use-check-merchant-password-setup.query";
import { Button, FormLayout, Modal, TextField } from "@shopify/polaris";
import { useForm } from "react-hook-form";
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from "yup";
import { useSetupMerchantPasswordMutation } from "@data/merchant/use-setup-merchant-password.mutation";
import { renderErrors, showToast, toastOptions } from "@utils/helpers";
import { useInventoryBatchDeleteAllMutation } from "@data/inventory_batch/use-clear-inventory-batch.mutation";
import { ModalContentLoader } from "@components/ui/modal/modal";

type PasswordSetupPromptFormValues = {
  password: string;
  password_confirmation: string;
};

const PasswordSetupPrompt: React.FC<{ }> = ({ }) => {
  const { closeModal } = useModalAction();
  const { mutate: setupMerchantPassword, isLoading } = useSetupMerchantPasswordMutation();
  const {
    watch,
    setValue,
    handleSubmit,
    formState: { errors }
  } = useForm<PasswordSetupPromptFormValues>({
    defaultValues: {
      password: "",
      password_confirmation: "",
    },
    resolver: yupResolver(yup.object().shape({
      password: yup.string().required("Password is required").min(8, "Password should consist of at least 8 characters"),
      password_confirmation: yup.string().required("Confirm password is required").oneOf([yup.ref('password')], 'Passwords do not match')
    }))
  });
  const formValues = watch();
  function setValues(value: any, field: (keyof PasswordSetupPromptFormValues)) {
    setValue(field, value);
  }
  function onSubmit(values: PasswordSetupPromptFormValues) {
    setupMerchantPassword({
      password: values.password,
      password_confirmation: values.password_confirmation
    }, {
      onSuccess() {
        showToast(toastOptions.PASSWORD_SETUP_SUCCESS);
      },
      onError: (response: any) => {
        const errorData = response.data;
        showToast({ message: renderErrors(errorData) ?? "Password setup was failed", duration: 2000, isError: true });
      }
    });
  }
  return (
    <Modal.Section>
      <p className="mb-4">Before continue, please setup a password to secure your inventory data</p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <FormLayout>
          <FormLayout.Group>
            <TextField
              id="password"
              type="password"
              label="Password"
              autoComplete=""
              error={errors.password?.message}
              value={formValues.password}
              onChange={setValues}
            />
            <TextField
              id="password_confirmation"
              type="password"
              label="Confirm Password"
              autoComplete=""
              error={errors.password_confirmation?.message}
              value={formValues.password_confirmation}
              onChange={setValues}
            />
          </FormLayout.Group>
          <div className="text-right py-4 space-x-4">
            <Button
              onClick={closeModal}
              loading={isLoading}
            >
              Cancel
            </Button>
            <Button
              primary
              submit
              loading={isLoading}
            >
              Submit
            </Button>
          </div>
        </FormLayout>
      </form>
    </Modal.Section>
  );
}

type PasswordPromptFormValues = {
  password: string;
};

const BatchesDeletionPrompt: React.FC<{  }> = ({ }) => {
  const { openModal, closeModal } = useModalAction();
  const { mutate: requestDeleteAllBatches, isLoading } = useInventoryBatchDeleteAllMutation();
  const {
    watch,
    setValue,
    handleSubmit,
    formState: { errors }
  } = useForm<PasswordPromptFormValues>({
    defaultValues: {
      password: "",
    },
    resolver: yupResolver(yup.object().shape({
      password: yup.string().required("Password is required"),
    }))
  });
  const formValues = watch();
  function setValues(value: any, field: (keyof PasswordPromptFormValues)) {
    setValue(field, value);
  }
  function onSubmit(values: PasswordPromptFormValues) {
    requestDeleteAllBatches({
      password: values.password
    }, {
      onSuccess() {
        showToast(toastOptions.BATCH_DELETE_ALL_SUCCESS);
        closeModal();
      },
      onError: (response: any) => {
        const errorData = response.data;
        showToast({ message: renderErrors(errorData) ?? "Delete all batches was failed", duration: 2000, isError: true });
      }
    });
  }
  function handleForgotPassword() {
    openModal("FORGOT_PASSWORD_VIEW", null, { modalTitle: "Reset your password", hideCancelBtn: true });
  }
  return (
    <Modal.Section>
      <p className="mb-4">Enter password to confirm batches deletion</p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <FormLayout>
          <FormLayout.Group>
            <TextField
              id="password"
              type="password"
              label="Password"
              autoComplete=""
              error={errors.password?.message}
              value={formValues.password}
              onChange={setValues}
            />
          </FormLayout.Group>
          <div>
            <Button
              plain
              onClick={handleForgotPassword}
            >
              Forgot password?
            </Button>
          </div>
          <p className="text-red-500 mb-4">(Important) This will delete all inventory batches data created in your account, this action cannot be undone.</p>
          <div className="text-right py-4 space-x-4">
            <Button
              onClick={closeModal}
              loading={isLoading}
            >
              Cancel
            </Button>
            <Button
              destructive
              submit
              loading={isLoading}
            >
              Confirm and Delete
            </Button>
          </div>
        </FormLayout>
      </form>
    </Modal.Section>
  );
}

const ClearBatchesView: React.FC = () => {
  const {
    data,
    isLoading,
    isRefetching,
  } = useCheckMerchantPasswordSetupQuery();
  if(isLoading || isRefetching || !data) {
    return (
      <ModalContentLoader />
    );
  }
  if (data.is_password_setup_required) {
    return <PasswordSetupPrompt />
  }
  // prompt password for batches deletion
  return <BatchesDeletionPrompt />
}

export default ClearBatchesView;