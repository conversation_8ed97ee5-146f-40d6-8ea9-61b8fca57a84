import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, FormLayout, Modal, TextField } from "@shopify/polaris";
import { ResourcePicker } from "@shopify/app-bridge/actions";
import { useAppBridge } from "@shopify/app-bridge-react";
import DatePicker from "@components/common/date-picker";
import { showToast, toastOptions } from "@utils/helpers";
import { useUI } from "@contexts/ui.context";

interface EditBatchProductsModalProps {
    active: boolean;
    product_id: string | number;
    onDismiss: any;
}

const EditBatchProductsModal: React.FC<EditBatchProductsModalProps> = (props) => {
    const { location: selected_location } = useUI();
    const [product_id, setProductID] = useState<string | number | null>(props.product_id);
    const [variant_id, setVariantID] = useState<string | number | null>(null);
    const [quantity, setQuantity] = useState<string | null>("0");
    const [expiry_date, setExpiryDate] = useState<Date | null>(null);
    const [productPicker, setProductPicker] = useState<ResourcePicker.ResourcePicker | null>(null);
    const [selectUnsubscribe, setSelectUnsubscribe] = useState<any>(null);
    const app = useAppBridge();

    function handleDateChange(date: Date) {
        setExpiryDate(date);
    }

    useEffect(() => {
        initProductPicker();
    }, []);

    useEffect(() => {
        productPickerSubscribeCancel();
    }, [productPicker]);

    function initProductPicker() {
        const _product_picker = ResourcePicker.create(app, {
            resourceType: ResourcePicker.ResourceType.Product,
            options: {
                selectMultiple: false
            }
        });
        setProductPicker(_product_picker);
    }

    function productPickerSubscribeCancel() {
        if(productPicker) {
            productPicker.subscribe(ResourcePicker.Action.CANCEL, () => {
                if(selectUnsubscribe) {
                    selectUnsubscribe();
                }
            });
        }
    }

    function handleProductClicked() {
        let product_picker;
        if(product_id) {
            const selectedProductVariant = {
                id: product_id,
                variants: [{
                    id: variant_id as string,
                }],
            };
            product_picker = ResourcePicker.create(app, {
                resourceType: ResourcePicker.ResourceType.Product,
                options: {
                    selectMultiple: false,
                    initialSelectionIds: selectedProductVariant.variants
                }
            });
        }
        else {
            product_picker = ResourcePicker.create(app, {
                resourceType: ResourcePicker.ResourceType.Product,
                options: {
                    selectMultiple: false
                }
            });
        }
        product_picker.dispatch(ResourcePicker.Action.OPEN);
        handleSelection();
    }

    function handleSelection() {
        const selectUnsubscription = productPicker!.subscribe(ResourcePicker.Action.SELECT, ({selection}) => {
            setProductID(selection[0].id);
            setVariantID(selection[0].variants[0].id);
            selectUnsubscription();
        });
        setSelectUnsubscribe(() => () => selectUnsubscription);
    }

    function saveProduct() {
        if(!product_id || !variant_id) {
            showToast(toastOptions.PRODUCT_NOT_SELECTED);
            return;
        }
        
        if(!quantity) {
            showToast(toastOptions.INVALID_QUANTITY);
            return;
        }

        if(!expiry_date) {
            showToast(toastOptions.INVALID_EXPIRY_DATE);
            return;
        }
        
        // save product
        
        showToast(toastOptions.PRODUCT_SAVED);
        closeModal({
            product_id,
            variant_id,
            quantity,
            expiry_date
        });
    }

    function closeModal(data?: any) {
        setProductID(null);
        setVariantID(null);
        setQuantity(null);
        setExpiryDate(null);
        props.onDismiss(data);
    }

    return (
        <Modal
            open={props.active}
            onClose={() => closeModal()}
            title={ (product_id ? "Edit" : "Add") + " batch product"}
            primaryAction={{
                content: "Save",
                onAction: () => saveProduct(),
            }}
            secondaryActions={[
                {
                    content: "Cancel",
                    onAction: () => closeModal(),
                }
            ]}
        >
            <Modal.Section>
                <FormLayout>
                    <h4>Location: {selected_location ? selected_location.name : ""}</h4>
                    <FormLayout.Group>
                        <Button onClick={() => handleProductClicked()}>Browse product</Button>
                    </FormLayout.Group>
                    <FormLayout.Group>
                        <TextField
                            type="number"
                            label="Quantity"
                            autoComplete="batch_product_quantity"
                            value={quantity ?? ""}
                            onChange={(qty) => setQuantity(qty)}
                        />
                        <DatePicker inputLabel="Expiry date" date={expiry_date} onDateChange={(val: Date) => handleDateChange(val)} />
                    </FormLayout.Group>
                </FormLayout>
            </Modal.Section>
        </Modal>
    );
}

export default EditBatchProductsModal;