import React, { useMemo, useState } from "react";
import {
  Banner,
  Button,
  Checkbox,
  FormLayout,
  Modal,
  ResourceItem,
  ResourceList,
  Tag,
  TextField,
  Thumbnail,
} from "@shopify/polaris";
import { useAppBridge } from "@shopify/app-bridge-react";

import DatePicker from "@components/common/date-picker";
import {
  formatDate,
  handleExternalLink,
  renderErrors,
  showToast,
  toastOptions,
} from "@utils/helpers";
import { ShopifyText } from "@components/ui/shopify/text";
import { useUI } from "@contexts/ui.context";
import {
  BatchItem,
  InventoryBatch,
  Location,
  Meta,
  Product,
  Settings,
} from "@ts-types/types";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { BATCH_STATUS_ENUM, PLAN_TYPE_ENUM } from "@utils/constants";
import {
  useModalAction,
  useModalState,
} from "@components/ui/modal/modal.context";
import InventoryBatchLoader from "./inventory-batch-loader";
import ProductLoader from "@components/product/product-loader";
import { FormProvider, useForm } from "react-hook-form";
import { useCreateInventoryBatchMutation } from "@data/inventory_batch/use-create-inventory-batch.mutation";
import { useUpdateInventoryBatchMutation } from "@data/inventory_batch/use-update-inventory-batch.mutation";
import { useDeleteInventoryBatchMutation } from "@data/inventory_batch/use-delete-inventory-batch.mutation";
import { ModalContentLoader } from "@components/ui/modal/modal";
import { useRouter } from "next/router";
import { ROUTES } from "@utils/routes";
import moment from "moment";

interface EditInventoryBatchModalProps {
  settings: Settings;
  initialBatch?: InventoryBatch;
  initialProduct?: Product;
  onDismiss: any;
}

type EditInventoryBatchFormValues = {
  products: Array<Product>;
  quantity: string | number;
  expire_at: Date | null;
  dates: Array<Date>;
  is_sync_quantity: boolean;
  name: string | null;
  received_at: Date | null;
  lot_number: string | null;
  bin_location: string | null;
  barcode: string | null;
  invoice_number: string | null;
  description: string | null;
  batch_status: string;
  meta: Array<Meta>;
  adjusted_by?: string | null;
};

const defaultValues = {
  products: [],
  quantity: "",
  expire_at: null,
  dates: [],
  is_sync_quantity: false,
  name: null,
  received_at: null,
  lot_number: null,
  bin_location: null,
  barcode: null,
  invoice_number: null,
  description: null,
  batch_status: BATCH_STATUS_ENUM.IN_STOCK,
  meta: [],
};

function formatInventoryBatchFormDefaultValues(
  formProps: EditInventoryBatchModalProps
) {
  const { settings, initialBatch, initialProduct } = formProps;

  const metaPresets = (settings.meta_presets ?? []).map((meta) => ({
    name: meta,
    value: "",
  }));

  if (initialBatch) {
    const item = initialBatch.items[0] as BatchItem;
    const currentMeta = initialBatch.meta ?? [];
    const formMeta = metaPresets.map((meta) => {
      const existingMeta = currentMeta.find((m) => m.name == meta.name);
      if (existingMeta) return existingMeta;
      return meta;
    });
    currentMeta.forEach((meta) => {
      const existingMeta = formMeta.find((m) => m.name == meta.name);
      if (!existingMeta) formMeta.push(meta);
    });
    return {
      products: [item.product],
      quantity: item.quantity,
      expire_at: item.expire_at,
      dates: item.dates.map(({ date }) => moment(date).toDate()),
      is_sync_quantity: initialBatch.is_sync_quantity,
      name: initialBatch.name,
      received_at: initialBatch.received_at,
      lot_number: initialBatch.lot_number,
      bin_location: initialBatch.bin_location,
      barcode: initialBatch.barcode,
      invoice_number: initialBatch.invoice_number,
      description: initialBatch.description,
      batch_status: initialBatch.status,
      meta: formMeta,
    };
  }

  if (initialProduct) {
    return {
      products: [initialProduct],
      quantity: "",
      expire_at: null,
      dates: [],
      is_sync_quantity: settings.is_sync_batch_quantity,
      name: "Batch " + formatDate(Date(), "YYYY-MM-DD HH:mm:ss"),
      received_at: new Date(),
      lot_number: null,
      bin_location: null,
      barcode: null,
      invoice_number: null,
      description: null,
      batch_status: BATCH_STATUS_ENUM.IN_STOCK,
      meta: metaPresets,
    };
  }
  return {
    ...defaultValues,
    is_sync_quantity: settings.is_sync_batch_quantity,
    meta: metaPresets,
  };
}

const EditInventoryBatchModal: React.FC<EditInventoryBatchModalProps> = (
  batchModalProps
) => {
  const { settings, initialBatch, onDismiss } = batchModalProps;
  const router = useRouter();
  const { openModal, closeModal } = useModalAction();
  const { location: default_location, locations } = useUI();

  const { mutate: createBatch, isLoading: creating } =
    useCreateInventoryBatchMutation();
  const { mutate: updateBatch, isLoading: updating } =
    useUpdateInventoryBatchMutation();
  const { mutate: deleteBatch, isLoading: deleting } =
    useDeleteInventoryBatchMutation();

  const methods = useForm<EditInventoryBatchFormValues>({
    defaultValues: formatInventoryBatchFormDefaultValues(batchModalProps),
  });

  const { watch, setValue, getValues, handleSubmit } = methods;

  const canSetMeta = settings.plan != PLAN_TYPE_ENUM.FREE;

  const [to_dashboard, setToDashboard] = useState(false);
  const { data } = useModalState();
  const selected_location = useMemo(() => {
    if (data.location_id) {
      return locations.find(
        (location: Location) => location?.id == data.location_id
      );
    }
    return default_location;
  }, [locations, default_location, data]);
  function saveBatch(values: EditInventoryBatchFormValues) {
    if (initialBatch) {
      handleUpdateBatch(values);
    } else {
      handleCreateBatch(values);
    }
  }

  function handleCreateBatch(values: EditInventoryBatchFormValues) {
    const items = values.products.map((product) => {
      return {
        product_id: product.id,
        quantity: values.quantity,
        expire_at: values.expire_at
          ? formatDate(values.expire_at, "YYYY-MM-DD")
          : null,
        dates: values.dates
          ? values.dates.map((value) => formatDate(value, "YYYY-MM-DD"))
          : null,
      };
    });
    const payload = {
      location_id: selected_location.id,
      items,
      is_sync_quantity: values.is_sync_quantity,
      name: values.name!,
      received_at: values.received_at
        ? formatDate(values.received_at, "YYYY-MM-DD")
        : null,
      lot_number: values.lot_number,
      bin_location: values.bin_location,
      barcode: values.barcode,
      invoice_number: values.invoice_number,
      description: values.description,
      meta: values.meta,
      adjusted_by: values.adjusted_by,
    };
    createBatch(
      {
        variables: payload,
      },
      {
        onSuccess(response: any) {
          showToast(toastOptions.BATCH_CREATED);
          handleModalDismiss(
            to_dashboard
              ? { dashboard: true }
              : { batch_id: (response as InventoryBatch).id }
          );

          const { callbackView, ...callbackData } = data;
          if (callbackView == "QUANTITY_TRANSFER_VIEW") {
            openModal(
              callbackView,
              {
                ...callbackData,
                destination_batch_id: response.id,
              },
              {
                hideCancelBtn: true,
              }
            );
          }
        },
        onError: (response: any) => {
          const errorData = response.data;
          if (
            errorData &&
            errorData.data &&
            errorData.data.required_plan_upgrade
          ) {
            showToast(toastOptions.BATCH_PRODUCT_LIMIT);
            return;
          }
          showToast({
            message:
              renderErrors(errorData) ?? toastOptions.BATCH_NOT_CREATED.message,
            duration: 2000,
            isError: true,
          });
        },
      }
    );
  }

  function handleUpdateBatch(values: EditInventoryBatchFormValues) {
    const items = values.products.map((product) => {
      return {
        product_id: product.id,
        quantity: values.quantity,
        expire_at: values.expire_at
          ? formatDate(values.expire_at, "YYYY-MM-DD")
          : null,
        dates: values.dates
          ? values.dates.map((value) => formatDate(value, "YYYY-MM-DD"))
          : null,
      };
    });
    const payload = {
      items,
      is_sync_quantity: values.is_sync_quantity,
      name: values.name!,
      received_at: values.received_at
        ? formatDate(values.received_at, "YYYY-MM-DD")
        : null,
      lot_number: values.lot_number,
      bin_location: values.bin_location,
      barcode: values.barcode,
      invoice_number: values.invoice_number,
      description: values.description,
      meta: values.meta,
      adjusted_by: values.adjusted_by,
    };
    updateBatch(
      {
        id: initialBatch?.id!,
        variables: payload,
      },
      {
        onSuccess() {
          showToast(toastOptions.BATCH_SAVED);
          handleModalDismiss(to_dashboard ? { dashboard: true } : true);
        },
        onError: (response: any) => {
          const errorData = response.data;
          if (
            errorData &&
            errorData.data &&
            errorData.data.required_plan_upgrade
          ) {
            showToast(toastOptions.BATCH_PRODUCT_LIMIT);
            return;
          }
          showToast({
            message:
              renderErrors(errorData) ?? toastOptions.BATCH_NOT_SAVED.message,
            duration: 2000,
            isError: true,
          });
        },
      }
    );
  }

  function handleDeleteBatch() {
    if (window.confirm("Are you sure to delete this inventory batch?")) {
      deleteBatch(initialBatch?.id!, {
        onSuccess: () => {
          showToast(toastOptions.BATCH_DELETED);
          handleModalDismiss(true);
        },
        onError: (response: any) => {
          const errorData = response.data;
          showToast({
            message:
              renderErrors(errorData) ?? toastOptions.BATCH_NOT_DELETED.message,
            duration: 2000,
            isError: true,
          });
        },
      });
    }
  }

  function handleModalDismiss(
    result:
      | { dashboard?: boolean; batch_id?: string | number }
      | boolean = false
  ) {
    if (typeof result == "boolean") {
      return onDismiss(result);
    }
    if (result.dashboard) {
      router.push(ROUTES.PRODUCTS);
      closeModal();
      return;
    } else if (result.batch_id) {
      return openModal(
        "EDIT_INVENTORY_BATCH_VIEW",
        { batch_id: result.batch_id },
        { modalTitle: "Edit inventory batch", hideCancelBtn: true }
      );
    }
    return onDismiss(result);
  }

  function handleAddMeta() {
    openModal("META_PRESET_VIEW", null, {
      modalTitle: "Edit meta presets",
      hideCancelBtn: true,
    });
  }

  function setValues(value: any, field: keyof EditInventoryBatchFormValues) {
    setValue(field, value);
  }

  function handleSetDatesValue(value: Date | null) {
    if (value) {
      const newDates = [...getValues("dates")];
      const is_exists = newDates.find(
        (date) =>
          date.getFullYear() === value.getFullYear() &&
          date.getMonth() === value.getMonth() &&
          date.getDate() === value.getDate()
      );
      if (!is_exists) {
        newDates.push(value);
        setValue("dates", newDates);
      }
    }
  }

  function removeDate(index: number) {
    const newDates = [...getValues("dates")];
    newDates.splice(index, 1);
    setValue("dates", newDates);
  }

  function handleSetMetaValue(value: string, index: number) {
    const newMeta = [...getValues("meta")];
    newMeta[index].value = value;
    setValue("meta", newMeta);
  }

  const formValues = watch();

  function handleOpenQuantityTrasfer() {
    openModal(
      "QUANTITY_TRANSFER_VIEW",
      {
        source_location_id: selected_location.id,
        product_id: initialBatch?.items[0].product_id,
        source_batch_id: batchModalProps.initialBatch?.id,
      },
      {
        hideCancelBtn: true,
        modalTitle: "Quantity transfer",
        onDismiss: () =>
          openModal(
            "EDIT_INVENTORY_BATCH_VIEW",
            {
              batch_id: batchModalProps.initialBatch?.id,
            },
            {
              modalTitle: "Edit inventory batch",
              hideCancelBtn: true,
            }
          ),
      }
    );
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(saveBatch)}>
        <Modal.Section>
          <FormLayout>
            <div className="flex items-center">
              <h4>
                Location: {selected_location ? selected_location.name : ""}
              </h4>
              <div className="ml-auto flex gap-2">
                {!data.callbackView && (
                  <Button onClick={handleOpenQuantityTrasfer}>
                    Quantity Transfer
                  </Button>
                )}
                {!data.callbackView && initialBatch && (
                  <Button onClick={() =>
                    openModal(
                      "BATCH_VERIFICATION_VIEW",
                      { batch_id: initialBatch.id },
                      { modalTitle: "Verify batch quantity", hideCancelBtn: true }
                    )
                  }>
                    Verify Quantity
                  </Button>
                )}
              </div>
            </div>
            <ResourceList
              resourceName={{ singular: "product", plural: "products" }}
              items={formValues.products}
              renderItem={(item) => {
                const { id, image_url, name, parent_name } = item;
                return (
                  <ResourceItem
                    verticalAlignment="center"
                    id={id as string}
                    media={
                      <Thumbnail
                        size="medium"
                        alt={`${parent_name} (${name})`}
                        source={image_url ? image_url : "/assets/no-image.jpg"}
                      />
                    }
                    onClick={() => {}}
                    // shortcutActions={
                    //     !product_id &&
                    //     [
                    //         {
                    //             content: "Edit item",
                    //             onAction: () => editProduct(id)
                    //         },
                    //         {
                    //             content: "Remove item",
                    //             onAction: () => deleteProduct()
                    //         }
                    //     ]
                    // }
                    accessibilityLabel={`View details for ${`${parent_name} (${name})`}`}
                    name={`${parent_name} (${name})`}
                    persistActions
                  >
                    <h3>
                      <ShopifyText
                        as="h3"
                        variant="headingMd"
                        fontWeight="semibold"
                      >{`${parent_name} (${name})`}</ShopifyText>
                    </h3>
                    {/* <div>{quantity} in stock</div> */}
                  </ResourceItem>
                );
              }}
            />
            {/* For single item implementation */}
            <FormLayout.Group>
              <div className="flex flex-row gap-4">
                <div
                  className={
                    formValues.quantity !=
                    batchModalProps.initialBatch?.items[0].quantity
                      ? "w-1/3"
                      : "w-full"
                  }
                >
                  {" "}
                  <TextField
                    id="quantity"
                    type="number"
                    label="Quantity"
                    value={String(formValues.quantity)}
                    onChange={setValues}
                    min={0}
                    autoComplete="batch_quantity"
                    disabled={
                      formValues.batch_status == BATCH_STATUS_ENUM.EXPIRED
                    }
                    helpText={
                      formValues.batch_status == BATCH_STATUS_ENUM.EXPIRED
                        ? "This batch has expired"
                        : ""
                    }
                  />
                </div>

                {/* show this field when batch_quantity is different from initialValues */}
                {formValues.quantity !=
                  batchModalProps.initialBatch?.items[0].quantity && (
                  <div>
                    <TextField
                      id="adjusted_by"
                      type="text"
                      label="Adjusted by"
                      autoComplete="batch_adjusted_by"
                      value={formValues.adjusted_by!}
                      onChange={(adjusted_by) =>
                        setValues(adjusted_by, "adjusted_by")
                      }
                    />
                  </div>
                )}
              </div>

              <DatePicker
                inputLabel="Expiry date"
                date={formValues.expire_at}
                onDateChange={(expire_date: Date | null) =>
                  setValues(expire_date, "expire_at")
                }
                disabled={formValues.batch_status == BATCH_STATUS_ENUM.EXPIRED}
              />

              <Checkbox
                id="is_sync_quantity"
                label={
                  <ShopifyText variant="bodyMd" as="p">
                    Adjust quantity (Increase stock quantity in Shopify).
                  </ShopifyText>
                }
                checked={formValues.is_sync_quantity}
                disabled={settings.is_disable_adjust_quantity}
                onChange={setValues}
              />
              <div>
                <DatePicker
                  inputLabel="Best Before date (Multiple dates available)"
                  date={null}
                  onDateChange={(best_before_date: Date | null) =>
                    handleSetDatesValue(best_before_date)
                  }
                  disabled={
                    formValues.batch_status == BATCH_STATUS_ENUM.EXPIRED
                  }
                />
                <div className="mt-2 space-y-1 space-x-1">
                  {formValues.dates.map((date, index) => (
                    <Tag onRemove={() => removeDate(index)}>
                      {moment(date).format("YYYY-MM-DD")}
                    </Tag>
                  ))}
                </div>
              </div>
            </FormLayout.Group>
          </FormLayout>
        </Modal.Section>
        <Modal.Section>
          <FormLayout>
            <ShopifyText variant="headingMd" as="h1">
              Batch details
            </ShopifyText>
            <FormLayout.Group>
              <TextField
                id="name"
                type="text"
                label="Batch name"
                autoComplete="batch_name"
                value={formValues.name!}
                onChange={setValues}
              />
              <DatePicker
                inputLabel="Received date"
                date={formValues.received_at}
                onDateChange={(received_date: Date | null) =>
                  setValues(received_date, "received_at")
                }
              />
              <TextField
                id="lot_number"
                type="text"
                label="Batch / Lot number"
                autoComplete="batch_name"
                value={formValues.lot_number!}
                onChange={setValues}
              />
              <TextField
                id="bin_location"
                type="text"
                label="Bin location"
                autoComplete="batch_bin_location"
                value={formValues.bin_location!}
                onChange={setValues}
              />
              <TextField
                id="barcode"
                type="text"
                label="Barcode"
                autoComplete="batch_barcode"
                value={formValues.barcode!}
                onChange={setValues}
              />
              <TextField
                id="invoice_number"
                type="text"
                label="Invoice number"
                autoComplete="batch_invoice_number"
                value={formValues.invoice_number!}
                onChange={setValues}
              />
              <TextField
                id="dscription"
                type="text"
                label="Description"
                autoComplete="batch_description"
                value={formValues.description!}
                onChange={setValues}
              />
            </FormLayout.Group>
          </FormLayout>
        </Modal.Section>
        <Modal.Section>
          <FormLayout>
            <ShopifyText variant="headingMd" as="h1">
              Additional details
            </ShopifyText>
            <FormLayout.Group>
              {formValues.meta.map(({ name, value }, index) => (
                <TextField
                  type="text"
                  label={name}
                  autoComplete=""
                  disabled={!canSetMeta}
                  value={value}
                  onChange={(new_value) => handleSetMetaValue(new_value, index)}
                />
              ))}
              <div>
                {canSetMeta ? (
                  <a className="no-underline" onClick={() => handleAddMeta()}>
                    + Add more meta
                  </a>
                ) : (
                  <p>Upgrade plan to manage inventory batch meta presets.</p>
                )}
              </div>
            </FormLayout.Group>
          </FormLayout>
        </Modal.Section>
        <Modal.Section>
          <FormLayout>
            <FormLayout.Group>
              <div className="flex justify-between items-center">
                <div>
                  {initialBatch ? (
                    <a
                      className="delete-link no-underline"
                      onClick={() => handleDeleteBatch()}
                    >
                      Delete batch
                    </a>
                  ) : (
                    <></>
                  )}
                </div>
                <div className="text-right py-4 space-x-4">
                  <Button
                    onClick={handleModalDismiss}
                    loading={creating || updating}
                    disabled={creating || updating || deleting}
                  >
                    Cancel
                  </Button>
                  <Button
                    submit
                    onClick={() => setToDashboard(true)}
                    loading={creating || updating}
                    disabled={creating || updating || deleting}
                  >
                    Save and back to products list
                  </Button>
                  <Button
                    primary
                    submit
                    loading={creating || updating}
                    disabled={creating || updating || deleting}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </FormLayout.Group>
          </FormLayout>
        </Modal.Section>
      </form>
    </FormProvider>
  );
};

const EditInventoryBatchModalView: React.FC = () => {
  const app = useAppBridge();
  const { data, options } = useModalState();
  const { closeModal } = useModalAction();
  const { onDismiss } = options?.onDismiss
    ? options
    : { onDismiss: closeModal };
  const { data: settings, isLoading, isRefetching } = useSettingsQuery();
  if (isLoading || isRefetching || !settings) {
    return <ModalContentLoader />;
  }
  if (!data || (!data.batch_id && !data.product_id)) {
    return <ModalContentLoader />;
  }
  return data.batch_id ? (
    <InventoryBatchLoader id={data.batch_id}>
      {({ inventory_batch }: { inventory_batch: InventoryBatch }) => (
        <EditInventoryBatchModal
          settings={settings}
          initialBatch={inventory_batch}
          onDismiss={onDismiss}
        />
      )}
    </InventoryBatchLoader>
  ) : (
    <ProductLoader id={data.product_id}>
      {({ product }: { product: Product }) =>
        // check is item stocked only when create batch from product, not when edit inventory batch
        product.is_item_stocked ? (
          <EditInventoryBatchModal
            settings={settings}
            initialProduct={product}
            onDismiss={onDismiss}
          />
        ) : (
          <Modal.Section>
            <Banner
              action={{
                content: "Edit locations",
                onAction: () =>
                  handleExternalLink(
                    app,
                    `/products/${product.parent_id}/variants/${product.shopify_variant_id}`
                  ),
              }}
              status="warning"
            >
              <p>
                This variant is not stocked at this location. Edit location to
                add a batch to the selected variant.
              </p>
            </Banner>
          </Modal.Section>
        )
      }
    </ProductLoader>
  );
};

export default EditInventoryBatchModalView;
