import React, { useEffect, useState } from "react";
import { <PERSON>List, Button, FormLayout, Label, Modal, Popover } from "@shopify/polaris";

import DatePicker from "@components/common/date-picker";
import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { useInventoryBatchExportMutation } from "@data/inventory_batch/use-export-inventory-batch.mutation";
import { useUI } from "@contexts/ui.context";
import { Location } from "@ts-types/types";
import { PLAN_TYPE_ENUM } from "@utils/constants";

interface ExportBatchModalProps {
    active: boolean;
    onDismiss: any;
}

function ExportBatchModal(props: ExportBatchModalProps) {
    const statusOptions = [
        {
            name: "All",
            value: null
        },
        {
            name: "In stock",
            value: "in_stock"
        },
        {
            name: "Out of stock",
            value: "out_of_stock"
        },
        {
            name: "Past best before",
            value: "past_best_before"
        },
        {
            name: "Upcoming best before",
            value: "upcoming_best_before"
        },
        {
            name: "Expired",
            value: "expired"
        }
    ];
    const [from, setFrom] = useState(null);
    const [to, setTo] = useState(null);
    const [status, setStatus] = useState(statusOptions[0]);
    const [active, setActive] = useState(false);
    const [location, setLocation] = useState<{ id: string | number | null; name: string; }>({ name: "All", id: null });
    const { locations } = useUI();
    const [locationActive, setLocationActive] = useState(false);
    const [plans, setPlans] = useState<any | null>(null);
    const app = useAppBridge();
    const { mutate: exportBatch, isLoading: isExporting } = useInventoryBatchExportMutation();
    const {
        data: settings,
    } = useSettingsQuery();

    useEffect(() => {
        if(props.active) {
            getPlans();
        }
    }, [props.active]);

    function getPlans() {
        fetchAPI("GET", API_ENDPOINTS.PLAN)
            .then((plan_settings: any) => {
                setPlans(plan_settings);
            });
    }

    function viewPlans() {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.PLANS);
    }

    function handleExportBatch() {
        if(!from) {
            return showToast({ message: "From date is required", duration: 2000, isError: true });
        }
        if(!to) {
            return showToast({ message: "To date is required", duration: 2000, isError: true });
        }
        const payload = {
            from,
            to,
            status: status.value,
            location: location.id
        };
        exportBatch(payload, {
            onSuccess: () => {
                showToast(toastOptions.BATCH_EXPORT_SUCCESS);
                closeModal();
            },
            onError: () => {
                showToast(toastOptions.BATCH_EXPORT_FAILED);
            }
        });
    }

    function toggleActive() {
        setActive(!active);
    }

    function toggleLocationActive() {
        setLocationActive(!locationActive);
    }

    function closeModal() {
        setFrom(null);
        setTo(null);
        setStatus(statusOptions[0]);
        props.onDismiss();
    }

    return (
        <Modal
            open={props.active}
            onClose={closeModal}
            title="Export inventory batch"
            primaryAction={{
                content: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? "Export" : "View plans",
                loading: isExporting,
                disabled: isExporting,
                onAction: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? handleExportBatch : viewPlans,
            }}
            secondaryActions={[
                {
                    content: "Cancel",
                    onAction: closeModal,
                }
            ]}
        >
            <Modal.Section>
                <FormLayout>
                    {(settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? (
                        <>
                            <FormLayout.Group>
                                <DatePicker inputLabel="From date" date={from} onDateChange={setFrom} />
                                <DatePicker inputLabel="To date" date={to} onDateChange={setTo} />
                            </FormLayout.Group>
                            <FormLayout.Group>
                                <>
                                    <Label id="status">Filter</Label>
                                    <Popover
                                        active={active}
                                        activator={
                                            <Button
                                                onClick={toggleActive}
                                                disclosure
                                            >
                                                {status ? status.name : "Filter"}
                                            </Button>
                                        }
                                        onClose={toggleActive}
                                    >
                                        <ActionList
                                            items={
                                                (statusOptions && statusOptions.length > 0)
                                                ? (
                                                    statusOptions.map((option) => {
                                                        return {
                                                            content: option.name,
                                                            onAction: () => {
                                                                setStatus(option);
                                                                toggleActive();
                                                            }
                                                        };
                                                    })
                                                )
                                                : []
                                            }
                                        />
                                    </Popover>
                                </>
                            </FormLayout.Group>
                            <FormLayout.Group>
                                <>
                                    <Label id="location">Location</Label>
                                    <Popover
                                        active={locationActive}
                                        activator={
                                            <Button
                                                onClick={toggleLocationActive}
                                                disclosure
                                            >
                                                {location ? location.name : "Location"}
                                            </Button>
                                        }
                                        onClose={toggleLocationActive}
                                    >
                                        <ActionList
                                            items={[
                                                {
                                                    content: "All",
                                                    onAction: () => {
                                                        setLocation({ id: null, name: "All" });
                                                        toggleLocationActive();
                                                    }
                                                },
                                                ...((locations && locations.length > 0)
                                                    ? (
                                                        locations.map(({ id, name }: Location) => {
                                                            return {
                                                                content: name,
                                                                onAction: () => {
                                                                    setLocation({ id, name });
                                                                    toggleLocationActive();
                                                                }
                                                            };
                                                        })
                                                    )
                                                    : [])
                                            ]}
                                        />
                                    </Popover>
                                </>
                            </FormLayout.Group>
                        </>
                    ) : (
                        <p>This feature requires a subscription to the {plans?.pro.name} plan. Please upgrade by following the link below.</p>
                    )}
                </FormLayout>
            </Modal.Section>
        </Modal>
    );
}

export default ExportBatchModal;