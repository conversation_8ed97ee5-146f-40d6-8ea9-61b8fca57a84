import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { Button, DropZone, FormLayout, Icon, Link, Modal, Stack } from "@shopify/polaris";
import { NoteMinor } from "@shopify/polaris-icons";

import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import UploadIcon from "@components/ui/icons/upload-icon";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useUI } from "@contexts/ui.context";
import { Location } from "@ts-types/types";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { ShopifyText } from "@components/ui/shopify/text";
import { useInventoryBatchImportMutation } from "@data/inventory_batch/use-import-inventory-batch.mutation";
import { PLAN_TYPE_ENUM } from "@utils/constants";

interface ImportBatchModalProps {
    active: boolean;
    onDismiss: any;
}

function ImportBatchModal(props: ImportBatchModalProps) {
    const { locations } = useUI();
    const router = useRouter();
    const [file, setFile] = useState<File | null>(null);
    const valid_file_types = ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel", ".csv"];
    const [plans, setPlans] = useState<any>(null);
    const app = useAppBridge();
    const { mutate: importBatch, isLoading: isImporting } = useInventoryBatchImportMutation();
    const {
        data: settings,
    } = useSettingsQuery();

    useEffect(() => {
        if(props.active) {
            getPlans();
        }
    }, [props.active]);

    function getPlans() {
        fetchAPI("GET", API_ENDPOINTS.PLAN)
            .then((plan_settings) => {
                setPlans(plan_settings);
            });
    }

    function viewPlans() {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.PLANS);
    }

    function handleDropZoneDrop(dropped_file: File[]) {
        setFile(dropped_file[0]);
    }
    
    function handleImportBatch() {
        const payload = new FormData();
        payload.append("batch_import", file!);
        importBatch(payload, {
            onSuccess: () => {
                showToast(toastOptions.BATCH_IMPORTED);
                closeModal(true);
            },
            onError: () => {
                showToast(toastOptions.BATCH_IMPORT_FAILED);
            }
        });
    }

    function closeModal(success = false) {
        setFile(null);
        props.onDismiss(success);
    }

    function handleViewImportHistory() {
        router.push(ROUTES.IMPORTS);
    }

    return (
        <Modal
            open={props.active}
            onClose={closeModal}
            title="Import inventory batch"
            primaryAction={{
                content: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? "Import" : "View plans",
                loading: isImporting,
                disabled: isImporting,
                onAction: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? handleImportBatch : viewPlans
            }}
            secondaryActions={[
                {
                    content: "Cancel",
                    onAction: closeModal,
                }
            ]}
            footer={
                (settings && settings.plan != PLAN_TYPE_ENUM.FREE)
                    ? <a className="hover:underline" onClick={handleViewImportHistory}>View import history</a>
                    : <></>
            }
        >
            <Modal.Section>
                <FormLayout>
                    {(settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? (
                        <>
                            <FormLayout.Group>
                                <>
                                    <div>
                                        <p className="mb-4">Import a XLSX file to bulk add batches or adjust inventory quantities.</p>
                                        <p className="mb-4"><Link url="https://expiry-inventory-public.s3.ap-southeast-1.amazonaws.com/assets/batch_template.xlsx" external>Click here</Link> to download a XLSX file with sample data on bulk creating and updating new batches.</p>
                                        <p className="mb-2">You may fill in <b>Inventory Batch ID (2nd column of import file)</b> with the batch ID gotten from inventory batches list or when exporting inventory batches if you wish to update existing batch details, otherwise <b>leave it blank</b> if you wish to <b>create new inventory batch</b>.</p>
                                        <p className="mb-4">If the specified Inventory Batch ID is invalid, a new batch will be created with the provided details.</p>
                                        <p className="mb-4">Please fill in <b>Location ID (3rd column of import file)</b> corresponding with your specified location(s) as per below:</p>
                                        <div className="mb-4 ml-4">
                                            {
                                                (locations && locations.length > 0)
                                                ? (
                                                    locations.map((location: Location, index: number) => {
                                                        return (
                                                            <p key={index}>{index + 1}. <b>{location.shopify_location_id}</b>: {location.name}</p>
                                                        )
                                                    })
                                                )
                                                : []
                                            }
                                        </div>
                                        <p className="mb-4">If you are to provide CSV file, please make sure the dates column values are formatted to <b>D/M/Y (eg. 31/12/2023)</b></p>
                                    </div>
                                    <DropZone
                                        onDrop={handleDropZoneDrop}
                                        accept={valid_file_types.join()}
                                        allowMultiple={false}
                                    >
                                        {file ? (
                                            <div className="flex items-center justify-center h-full">
                                                <div className="text-center">
                                                    <span className="Polaris-Thumbnail Polaris-Thumbnail--sizeSmall m-auto">
                                                        <Icon source={NoteMinor} />
                                                    </span>
                                                    {file.name} <ShopifyText as="p" variant="bodySm">{file.size} bytes</ShopifyText>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="flex w-full h-full text-center justify-center items-center p-4">
                                                <Stack distribution="center">
                                                    <Stack vertical>
                                                        <Stack.Item>
                                                            <div className="w-full text-center">
                                                                <UploadIcon className="w-24 h-24 m-auto" />
                                                            </div>
                                                        </Stack.Item>
                                                        <Stack.Item>
                                                            <Button>Add files</Button>
                                                        </Stack.Item>
                                                        <Stack.Item>
                                                            <ShopifyText as="h3" variant="bodyMd" color="subdued" fontWeight="regular">
                                                                or drop to upload
                                                            </ShopifyText>
                                                        </Stack.Item>
                                                    </Stack>
                                                </Stack>
                                            </div>
                                        )}
                                    </DropZone>
                                </>
                            </FormLayout.Group>
                        </>
                    ) : (
                        <p>This feature requires a subscription to the {plans?.pro.name} plan. Please upgrade by following the link below.</p>
                    )}
                </FormLayout>
            </Modal.Section>
        </Modal>
    );
}

export default ImportBatchModal;