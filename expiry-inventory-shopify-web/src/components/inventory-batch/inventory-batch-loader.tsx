import { ModalContentLoader } from "@components/ui/modal/modal";
import { useUI } from "@contexts/ui.context";
import { useInventoryBatchQuery } from "@data/inventory_batch/use-inventory-batch.query";

interface InventoryBatchLoaderProps {
  id: string;
}

const InventoryBatchLoader: React.FC<InventoryBatchLoaderProps> = ({ id, children }) => {
  const { location } = useUI();
  const { data, isLoading } = useInventoryBatchQuery(id, {
    query: {
      location: String(location.id),
    }
  });
  if (isLoading || !data) {
    return (
      <ModalContentLoader />
    );
  }
  // @ts-ignore
  return children({ inventory_batch: data });
}

export default InventoryBatchLoader;