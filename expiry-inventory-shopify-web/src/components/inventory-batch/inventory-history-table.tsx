import React from "react";
import { DataTable, Pagination, Link } from "@shopify/polaris";
import { formatDate, getShopURL } from "@utils/helpers";
import EmptyDataTable from "@components/ui/loaders/empty-data-table";
import NoResult from "@components/common/no-result";
import { ShopifyCard } from "@components/ui/shopify/card";
import { ROUTES } from "@utils/routes";
import { ShopifyText } from "@components/ui/shopify/text";
import { InventoryHistory } from "@ts-types/types";
import { useModalAction } from "@components/ui/modal/modal.context";

function InventoryHistoryTable({
  isLoading,
  data,
  paginationData,
  currentPage,
  onPageChange,
  onReload,
}: {
  isLoading: boolean;
  data: Array<InventoryHistory>;
  paginationData: any;
  currentPage: number;
  onPageChange: any;
  onReload: any;
}) {
  const { openModal, closeModal } = useModalAction();

  function handleBatchClicked(batch_id: string | number) {
    openModal(
      "EDIT_INVENTORY_BATCH_VIEW",
      { batch_id },
      {
        modalTitle: "Edit inventory batch",
        onDismiss: handleBatchModalClosed,
        hideCancelBtn: true,
      }
    );
  }

  function handleBatchModalClosed(status = false) {
    if (status === true) {
      onReload();
    }
    closeModal();
  }

  return (
    <>
      <ShopifyCard>
        <div>
          {isLoading ? <EmptyDataTable /> : <></>}
          {!isLoading && (!data || data.length <= 0) ? <NoResult /> : <></>}
          {!isLoading && data.length > 0 ? (
            <DataTable
              hideScrollIndicator
              columnContentTypes={[
                "text",
                "text",
                "text",
                "numeric",
                "numeric",
                "numeric",
              ]}
              headings={[
                "Date",
                "Event",
                "Adjusted by",
                "Adjustment",
                "Quantity",
                "Total Batches Items Qty in Location",
              ]}
              rows={data.map((row) => {
                return [
                  formatDate(row.created_at, "MMM D, YYYY [at] h:mm A"),
                  <div className="whitespace-normal">
                    <p>{row.action.name}</p>
                    {row.batch ? (
                      <p>
                        <a
                          className="no-underline"
                          onClick={() => handleBatchClicked(row.batch_id)}
                        >
                          {row.batch.name ?? `Batch #${row.batch.id}`}
                        </a>
                      </p>
                    ) : (
                      <></>
                    )}
                    {row.order ? (
                      <p>
                        <Link
                          url={`${ROUTES.ORDERS}/${row.order_id}`}
                          removeUnderline
                        >
                          Order #{row.order.order_number}
                        </Link>
                      </p>
                    ) : (
                      <></>
                    )}
                  </div>,
                  <span className="whitespace-normal">
                    {row.adjusted_by ||
                      (row.action.trigger_by
                        ? row.action.trigger_by
                        : getShopURL())}
                  </span>,
                  <ShopifyText
                    as="p"
                    variant="bodyMd"
                    color={row.adjustment > 0 ? "success" : "warning"}
                    fontWeight="regular"
                  >
                    {row.adjustment > 0 ? "+" : ""}
                    {row.adjustment}
                  </ShopifyText>,
                  row.result_quantity,
                  row.location_quantity,
                ];
              })}
              footerContent={
                <p>
                  Showing{" "}
                  <b>
                    {paginationData.from} - {paginationData.to}
                  </b>{" "}
                  of <b>{paginationData.total}</b> events
                </p>
              }
            />
          ) : (
            <></>
          )}
        </div>
      </ShopifyCard>
      <div id="product-paginator">
        <Pagination
          hasPrevious={1 < currentPage}
          onPrevious={() => {
            onPageChange(currentPage - 1);
          }}
          hasNext={currentPage < paginationData.last_page}
          onNext={() => {
            onPageChange(currentPage + 1);
          }}
        />
      </div>
    </>
  );
}

export default InventoryHistoryTable;
