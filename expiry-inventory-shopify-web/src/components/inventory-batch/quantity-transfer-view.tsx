import LocationPicker from "@components/common/location-picker";
import SelectBatch from "@components/common/select-batch";
import SelectLocation from "@components/common/select-location";
import {
  useModalAction,
  useModalState,
} from "@components/ui/modal/modal.context";
import { useInventoryBatchTransferMutation } from "@data/inventory_batch/use-inventory-batch-transfer.mutation";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  AlphaCard,
  Button,
  Checkbox,
  DataTable,
  Link,
  Modal,
  Text,
  TextField,
} from "@shopify/polaris";
import { InventoryBatch } from "@ts-types/types";
import { showToast, toastOptions } from "@utils/helpers";
import React, { useEffect, useState } from "react";
import { FormProvider, set, useForm } from "react-hook-form";
import * as yup from "yup";

const schema = yup.object().shape({
  source_location_id: yup.string().required("Source location is required"),
  destination_location_id: yup
    .string()
    .required("Destination location is required"),
  product_id: yup.string().required("Product is required"),
  source_batch_id: yup.string().required("Batch is required").nullable(),
  destination_batch_id: yup.string().required("Batch is required").nullable(),
  quantity: yup.string().required("Quantity is required"),
  source_quantity_adjustment: yup.boolean().required(),
  destination_quantity_adjustment: yup.boolean().required(),
});

type QuantityTransferDataProps = {
  source_location_id: string;
  destination_location_id: string;
  product_id: string;
  source_batch_id: string;

  destination_batch_id: string;
  source_batch_quantity: number;
  destination_batch_quantity: number;
};

type FormProps = {
  source_location_id: string;
  destination_location_id: string;
  product_id: string;
  source_batch_id: string | null;
  destination_batch_id: string | null;

  source_quantity_adjustment: boolean;
  destination_quantity_adjustment: boolean;
  quantity: string;
  is_draft?: boolean;
};

const QuantityTransferView = () => {
  const { openModal, closeModal } = useModalAction();
  const methods = useForm<FormProps>({
    defaultValues: {
      source_location_id: "",
      destination_location_id: "",
      product_id: "",
      source_batch_id: "",
      destination_batch_id: "",
      source_quantity_adjustment: true,
      destination_quantity_adjustment: true,
    },

    resolver: yupResolver(schema),
  });
  const { mutate: createBatchTransfer, isLoading: creating } =
    useInventoryBatchTransferMutation();
  const [sourceBatch, setSourceBatch] = useState<InventoryBatch | null>(null);
  const [destinationBatch, setDestinationBatch] =
    useState<InventoryBatch | null>(null);
  const sourceBatchItem = sourceBatch?.items[0];
  const destinationBatchItem = destinationBatch?.items[0];
  const modalState = useModalState();
  const data = modalState.data as QuantityTransferDataProps;
  const {
    source_location_id,
    destination_location_id,
    product_id,
    source_batch_id,
    destination_batch_id,
  } = data || {};

  const [step, setStep] = useState(1);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = methods;

  const submitTransfer = (data: FormProps, isDraft: boolean = false) => {
    createBatchTransfer(
      {
        variables: {
          ...data,
          source_batch_id: data.source_batch_id!,
          destination_batch_id: data.destination_batch_id!,
          is_draft: isDraft,
        },
      },
      {
        onSuccess: () => {
          const message = isDraft
            ? "Transfer saved as draft successfully"
            : "Batch transfer has been completed successfully";
          showToast({
            message,
            duration: 2000,
            isError: false,
          });
          closeModal();
        },
        onError: (error: any) => {
          showToast({
            message: error?.data?.message,
            duration: 2000,
            isError: true,
          });
        },
      }
    );
  };

  const onSubmit = (data: FormProps) => {
    submitTransfer(data, false);
  };

  const onSaveAsDraft = () => {
    const data = methods.getValues();
    submitTransfer(data, true);
  };

  useEffect(() => {
    reset({
      source_location_id,
      destination_location_id,
      product_id,
      source_batch_id,
      destination_batch_id,
    });
  }, [
    source_location_id,
    destination_location_id,
    product_id,
    source_batch_id,
    destination_batch_id,
  ]);

  const handleNextStep = async () => {
    if (
      methods.watch("source_batch_id") === methods.watch("destination_batch_id")
    ) {
      methods.setError("destination_batch_id", {
        type: "manual",
        message: "Source and destination batch cannot be the same",
      });
      return;
    }
    const valid = await methods.trigger([
      "source_location_id",
      "destination_location_id",
      "product_id",
      "source_batch_id",
      "destination_batch_id",
    ]);

    methods.setValue(
      "source_quantity_adjustment",

      methods.watch("source_quantity_adjustment") == undefined
        ? true
        : methods.watch("source_quantity_adjustment")
    );
    methods.setValue(
      "destination_quantity_adjustment",

      methods.watch("destination_quantity_adjustment") == undefined
        ? true
        : methods.watch("destination_quantity_adjustment")
    );
    if (valid) {
      setStep(2);
    }
  };

  const handleAddNewBatch = () => {
    openModal(
      "EDIT_INVENTORY_BATCH_VIEW",
      {
        ...data,
        ...methods.watch(),
        location_id: methods.watch("destination_location_id"),
        callbackView: "QUANTITY_TRANSFER_VIEW",
      },
      {
        modalTitle: `Add inventory batch`,
        onDismiss: () => {
          openModal(
            "QUANTITY_TRANSFER_VIEW",
            { ...modalState.data, ...methods.watch() },
            {
              modalTitle: `Transfer Quantity`,
              hideCancelBtn: true,
            }
          );
        },
        hideCancelBtn: true,
      }
    );
  };

  const quantity = methods.watch("quantity");
  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Modal.Section>
          {step === 1 && (
            <div className="flex gap-4">
              <div className="w-1/2 flex flex-col gap-2">
                <SelectLocation
                  name="source_location_id"
                  label="Source Location"
                  filter_product_id={methods.watch("product_id")}
                  onObjectChange={(location) => {
                    methods.setValue("source_batch_id", null);
                  }}
                />
                <SelectBatch
                  name="source_batch_id"
                  label="Batch"
                  onObjectChange={(batch) => {
                    setSourceBatch(batch ?? null);
                  }}
                  filter_product_id={methods.watch("product_id")}
                  filter_location_id={methods.watch("source_location_id")}
                  value={methods.watch("source_batch_id")}
                  error={errors.source_batch_id?.message}
                />
              </div>
              <div className="w-1/2 flex flex-col gap-2">
                <SelectLocation
                  name="destination_location_id"
                  label="Destination Location"
                  filter_product_id={methods.watch("product_id")}
                  onObjectChange={(location) => {
                    methods.setValue("destination_batch_id", null);
                  }}
                  default_select_first
                />
                <div>
                  <SelectBatch
                    name="destination_batch_id"
                    label="Batch"
                    onObjectChange={(batch) => {
                      setDestinationBatch(batch ?? null);
                    }}
                    filter_product_id={methods.watch("product_id")}
                    filter_location_id={methods.watch(
                      "destination_location_id"
                    )}
                    ignore_id={methods.watch("source_batch_id") as (string | undefined)}
                    error={errors.destination_batch_id?.message}
                    value={methods.watch("destination_batch_id")}
                  />
                  <div className="flex justify-end">
                    <Link onClick={handleAddNewBatch}>Assign to new batch</Link>
                  </div>
                </div>
              </div>
            </div>
          )}
          {step === 2 && (
            <>
              <TextField
                name="quantity"
                autoComplete="quantity"
                label="Quantity"
                type="number"
                value={methods.watch("quantity") as (string | undefined)}
                max={sourceBatchItem?.quantity}
                onChange={(value) => {
                  // the quantity cannot be larger than the source batch quantity
                  if (sourceBatchItem) {
                    if (
                      parseInt(value) <= sourceBatchItem.quantity ||
                      isNaN(parseInt(value))
                    ) {
                      methods.setValue("quantity", value);
                    }
                  }
                }}
                error={errors.quantity?.message}
              />
              <div className="my-4">
                <AlphaCard>
                  <Text variant="headingSm" as="h6">
                    Adjust quantity (Increase stock quantity in Shopify)
                  </Text>
                  <div className="flex gap-2">
                    <div className="w-1/2">
                      <Checkbox
                        name="source_quantity_adjustment"
                        label="Adjust source quantity"
                        checked={methods.watch("source_quantity_adjustment")}
                        onChange={(value) => {
                          methods.setValue("source_quantity_adjustment", value);
                        }}
                      />
                    </div>
                    <div className="w-1/2">
                      <Checkbox
                        name="destination_quantity_adjustment"
                        label="Adjust destination quantity"
                        checked={methods.watch(
                          "destination_quantity_adjustment"
                        )}
                        onChange={(value) => {
                          methods.setValue(
                            "destination_quantity_adjustment",
                            value
                          );
                        }}
                      />
                    </div>
                  </div>
                </AlphaCard>
              </div>
              {
                <div>
                  <DataTable
                    showTotalsInFooter
                    headings={[
                      ``,
                      ` ${sourceBatch?.name ?? `Batch #${sourceBatch?.id}`} (Source Batch)` || "Source",
                      ` ${destinationBatch?.name ?? `Batch #${destinationBatch?.id}`} (Destination Batch)` ||
                        "Destination",
                    ]}
                    rows={[
                      [
                        "",
                        `${sourceBatchItem?.quantity} ${`${
                          quantity
                            ? `
                          (-${quantity})`
                            : ``
                        }`}`,
                        `${destinationBatchItem?.quantity} ${`${
                          quantity
                            ? `
                          (+${quantity})`
                            : ``
                        }`}`,
                      ],
                      [
                        "After Transferred",
                        (sourceBatchItem && quantity)
                          ? (
                              typeof sourceBatchItem.quantity == "string"
                                ? parseInt(sourceBatchItem.quantity)
                                : sourceBatchItem.quantity
                            )
                            -
                            (
                              typeof quantity == "string"
                                ? parseInt(quantity)
                                : quantity
                            )
                          : "-",

                        (destinationBatchItem && quantity)
                          ? (
                              typeof destinationBatchItem.quantity == "string"
                                ? parseInt(destinationBatchItem.quantity)
                                : destinationBatchItem.quantity
                            )
                            +
                            (
                              typeof quantity == "string"
                                ? parseInt(quantity)
                                : quantity
                            )
                          : "-",
                      ],
                    ]}
                    columnContentTypes={["text", "numeric", "numeric"]}
                  ></DataTable>
                </div>
              }
            </>
          )}
        </Modal.Section>

        <Modal.Section>
          {step === 1 && (
            <div className="flex gap-4 justify-end">
              {" "}
              <Button primary onClick={handleNextStep}>
                Next
              </Button>
            </div>
          )}
          {step === 2 && (
            <div className="flex gap-4 justify-end">
              <Button
                onClick={() => setStep(1)}
                loading={creating}
                disabled={creating}
              >
                Back
              </Button>
              <Button
                onClick={onSaveAsDraft}
                loading={creating}
                disabled={creating}
              >
                Save as Draft
              </Button>
              <Button primary submit loading={creating} disabled={creating}>
                Transfer
              </Button>
            </div>
          )}
        </Modal.Section>
      </form>
    </FormProvider>
  );
};

export default QuantityTransferView;
