import React, { useEffect, useState } from "react";
import {
  Modal,
  DataTable,
  <PERSON><PERSON>,
  Button,
  Pagination,
  Select,
  TextField,
  ButtonGroup,
  Stack,
} from "@shopify/polaris";
import { formatDate, showToast } from "@utils/helpers";
import { Transfer } from "@ts-types/types";
import EmptyDataTable from "@components/ui/loaders/empty-data-table";
import NoResult from "@components/common/no-result";
import { useTransfersQuery } from "@data/transfers/use-transfers.query";
import { useUpdateTransferMutation } from "@data/transfers/use-update-transfer.mutation";
import { useExecuteTransferMutation } from "@data/transfers/use-execute-transfer.mutation";
import { useDeleteTransferMutation } from "@data/transfers/use-delete-transfer.mutation";

interface TransfersModalProps {
  active: boolean;
  onDismiss: () => void;
}

const TransfersModal: React.FC<TransfersModalProps> = ({ active, onDismiss }) => {
  const [page, setPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState("");
  const [draftFilter, setDraftFilter] = useState("");
  const [editingTransfer, setEditingTransfer] = useState<Transfer | null>(null);
  const [editQuantity, setEditQuantity] = useState("");
  const [editNotes, setEditNotes] = useState("");

  const queryParams = {
    page,
    ...(statusFilter && { status: statusFilter }),
    ...(draftFilter && { is_draft: draftFilter === "true" }),
  };

  const { data: transfersData, isLoading } = useTransfersQuery(queryParams);
  const updateTransferMutation = useUpdateTransferMutation();
  const executeTransferMutation = useExecuteTransferMutation();
  const deleteTransferMutation = useDeleteTransferMutation();

  const transfers = transfersData?.data || [];
  const totalPages = transfersData?.last_page || 1;

  const statusOptions = [
    { label: "All Statuses", value: "" },
    { label: "Pending", value: "pending" },
    { label: "Completed", value: "completed" },
    { label: "Cancelled", value: "cancelled" },
  ];

  const draftOptions = [
    { label: "All Transfers", value: "" },
    { label: "Draft", value: "true" },
    { label: "Completed", value: "false" },
  ];

  // Reset page when filters change
  useEffect(() => {
    setPage(1);
  }, [statusFilter, draftFilter]);

  const handleExecuteTransfer = (transfer: Transfer) => {
    if (!confirm("Are you sure you want to approve this transfer?")) return;

    executeTransferMutation.mutate(transfer.id, {
      onSuccess: () => {
        showToast({
          message: "Transfer approved and executed successfully",
          duration: 2000,
          isError: false,
        });
      },
      onError: (error: any) => {
        showToast({
          message: error?.data?.message || "Failed to approve transfer",
          duration: 2000,
          isError: true,
        });
      },
    });
  };

  const handleDeleteTransfer = (transfer: Transfer) => {
    if (!confirm("Are you sure you want to delete this transfer?")) return;

    deleteTransferMutation.mutate(transfer.id, {
      onSuccess: () => {
        showToast({
          message: "Transfer deleted successfully",
          duration: 2000,
          isError: false,
        });
      },
      onError: (error: any) => {
        showToast({
          message: error?.data?.message || "Failed to delete transfer",
          duration: 2000,
          isError: true,
        });
      },
    });
  };

  const handleEditTransfer = (transfer: Transfer) => {
    setEditingTransfer(transfer);
    setEditQuantity(transfer.quantity.toString());
    setEditNotes(transfer.notes || "");
  };

  const handleSaveEdit = () => {
    if (!editingTransfer) return;

    const updateData: any = { id: editingTransfer.id };
    if (editQuantity !== editingTransfer.quantity.toString()) {
      updateData.quantity = parseInt(editQuantity);
    }
    if (editNotes !== (editingTransfer.notes || "")) {
      updateData.notes = editNotes;
    }

    updateTransferMutation.mutate(updateData, {
      onSuccess: () => {
        showToast({
          message: "Transfer updated successfully",
          duration: 2000,
          isError: false,
        });
        setEditingTransfer(null);
      },
      onError: (error: any) => {
        showToast({
          message: error?.data?.message || "Failed to update transfer",
          duration: 2000,
          isError: true,
        });
      },
    });
  };

  const renderStatusBadge = (transfer: Transfer) => {
    const status = transfer.is_draft ? "Draft" : transfer.status;
    const statusColor = transfer.is_draft 
      ? "info" 
      : transfer.status === "completed" 
        ? "success" 
        : transfer.status === "cancelled" 
          ? "critical" 
          : "warning";
    
    return <Badge status={statusColor as any}>{status}</Badge>;
  };

  const renderActions = (transfer: Transfer) => {
    if (editingTransfer?.id === transfer.id) {
      return (
        <ButtonGroup>
          <Button size="slim" onClick={handleSaveEdit}>Save</Button>
          <Button size="slim" onClick={() => setEditingTransfer(null)}>Cancel</Button>
        </ButtonGroup>
      );
    }

    if (transfer.is_draft) {
      return (
        <ButtonGroup>
          <Button size="slim" onClick={() => handleEditTransfer(transfer)}>Edit</Button>
          <Button size="slim" primary onClick={() => handleExecuteTransfer(transfer)}>Approve</Button>
          <Button size="slim" destructive onClick={() => handleDeleteTransfer(transfer)}>Delete</Button>
        </ButtonGroup>
      );
    }

    return <span>—</span>;
  };

  const renderQuantity = (transfer: Transfer) => {
    if (editingTransfer?.id === transfer.id) {
      return (
        <div className="w-20">
          <TextField
            label=""
            labelHidden
            name="quantity"
            autoComplete="off"
            type="number"
            value={editQuantity}
            onChange={setEditQuantity}
          />
        </div>
      );
    }
    return transfer.quantity.toString();
  };

  const tableRows = transfers.map((transfer: Transfer) => [
    transfer.id,
    (transfer.source_batch?.name || `Batch #${transfer.source_batch_id}`) + ` (${transfer.source_batch?.location?.name})`,
    (transfer.destination_batch?.name || `Batch #${transfer.destination_batch_id}`) + ` (${transfer.destination_batch?.location?.name})`,
    transfer.product?.name || "—",
    renderQuantity(transfer),
    renderStatusBadge(transfer),
    formatDate(transfer.created_at),
    transfer.executed_at ? formatDate(transfer.executed_at) : "—",
    renderActions(transfer),
  ]);

  return (
    <Modal
      open={active}
      onClose={onDismiss}
      title="Transfers"
      large
    >
      <Modal.Section>
        <Stack>
          <Stack.Item fill>
            <Select
              label="Status Filter"
              options={statusOptions}
              value={statusFilter}
              onChange={setStatusFilter}
            />
          </Stack.Item>
          <Stack.Item fill>
            <Select
              label="Type Filter"
              options={draftOptions}
              value={draftFilter}
              onChange={setDraftFilter}
            />
          </Stack.Item>
        </Stack>
      </Modal.Section>

      <Modal.Section>
        {isLoading ? (
          <EmptyDataTable />
        ) : transfers.length === 0 ? (
          <NoResult />
        ) : (
          <DataTable
            columnContentTypes={[
              "text",
              "text", 
              "text",
              "text",
              "text",
              "text",
              "text",
              "text",
              "text",
            ]}
            headings={[
              "ID",
              "Source Batch",
              "Destination Batch", 
              "Product",
              "Quantity",
              "Status",
              "Created",
              "Executed",
              "Actions",
            ]}
            rows={tableRows}
          />
        )}
      </Modal.Section>

      {totalPages > 1 && (
        <Modal.Section>
          <Pagination
            hasPrevious={page > 1}
            onPrevious={() => setPage(page - 1)}
            hasNext={page < totalPages}
            onNext={() => setPage(page + 1)}
          />
        </Modal.Section>
      )}
    </Modal>
  );
};

export default TransfersModal;
