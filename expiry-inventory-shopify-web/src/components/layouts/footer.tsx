import { ShopifyText } from "@components/ui/shopify/text";
import { Link } from "@shopify/polaris";
import { ROUTES } from "@utils/routes";

function Footer() {
    return (
        <div className="flex flex-col sm:flex-row justify-center items-center p-8">
            <div className="border-b sm:border-r sm:border-b-0 py-1 sm:px-2">
                <ShopifyText variant="bodyMd" fontWeight="semibold" as="p">Menu</ShopifyText>
            </div>
            <div className="border-b sm:border-r sm:border-b-0 py-1 sm:px-2">
                <Link url={ROUTES.DASHBOARD}>Dashboard</Link>
            </div>
            <div className="border-b sm:border-r sm:border-b-0 py-1 sm:px-2">
                <Link url={ROUTES.PRODUCTS}>Products</Link>
            </div>
            <div className="border-b sm:border-r sm:border-b-0 py-1 sm:px-2">
                <Link url={ROUTES.INVENTORY_BATCHES}>Inventory Batches</Link>
            </div>
            <div className="border-b sm:border-r sm:border-b-0 py-1 sm:px-2">
                <Link url={ROUTES.ORDERS}>Orders</Link>
            </div>
            <div className="py-1 sm:px-2">
                <Link url={ROUTES.SETTINGS}>Settings</Link>
            </div>
        </div>
    );
}

export default Footer;