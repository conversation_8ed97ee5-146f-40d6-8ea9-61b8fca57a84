import { useRouter } from "next/router";
import Footer from "./footer";
import { ROUTES } from "@utils/routes";
import { Frame, Page } from "@shopify/polaris";
import { isShopifyEmbedded } from "@shopify/app-bridge/utilities";
import { UIProvider } from "@contexts/ui.context";
import { AppBridgeProvider } from "@components/providers/AppBridgeProvider";
import RoutePropagator from "@components/propagators/RoutePropagator";
import PageLoading from "@components/common/page-loading";
import { useShopify } from "@contexts/shopify.context";

const Layout: React.FC<{}> = ({ children }) => {
    const router = useRouter();
    const { query, isReady, pathname } = router;
    const { host } = useShopify();
    
    if(!isReady) {
        return (
            <Page>
                <PageLoading />
            </Page>
        );
    }

    const { host: queryHost } = query;

    if(!host && !queryHost) {
        if(!isShopifyEmbedded()) {
            router.replace(ROUTES.SHOPIFY_LOGIN);
        }
        return <></>;
    }

    function PageContent() {
        return (
            <Frame>
                <div className="min-h-screen bg-gray-100 flex flex-col transition-colors duration-150">
                    <div className="flex flex-1">
                        <main className="w-full">
                            {children}
                        </main>
                    </div>
                    {pathname.indexOf(ROUTES.SHOPIFY) == -1 ? <Footer /> : <></>}
                </div>
            </Frame>
        );
    }
    
    return (
        <AppBridgeProvider host={(host ?? queryHost) as string}>
            <RoutePropagator />
            <UIProvider>
                <PageContent />
            </UIProvider>
        </AppBridgeProvider>
    );
  };
  export default Layout;