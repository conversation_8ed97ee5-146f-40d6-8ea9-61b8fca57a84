import { But<PERSON>, TextField } from "@shopify/polaris";
import { useFieldArray, useFormContext } from "react-hook-form"
import { MetaPresetFormValues } from "./meta-preset-modal-view";
import { ShopifyText } from "@components/ui/shopify/text";
import { DeleteMajor } from "@shopify/polaris-icons";

const MetaPresetListingInput: React.FC<{}> = () => {
  const { control } = useFormContext<MetaPresetFormValues>();
  const { fields, update, append, remove } = useFieldArray<MetaPresetFormValues>({
    name: "meta_presets",
    control
  });
  return (
    <div>
      <div className="mb-2">
        <ShopifyText as="h3">Meta Name</ShopifyText>
      </div>
      <div className="space-y-2 mb-3">
        {fields.map((field, index) => (
          <div key={index} className="flex items-center gap-4">
            <div className="flex-1">
              <TextField
                id={`meta_presets_${index}`}
                type="text"
                label=""
                autoComplete=""
                value={field.value}
                onChange={(new_value) => update(index, { ...field, value: new_value })}
              />
            </div>
            <Button
              icon={DeleteMajor}
              onClick={() => remove(index)}
            />
          </div>
        ))}
      </div>
      <Button onClick={() => append({ value: "" })}>
        + Add more
      </Button>
    </div>
  )
}

export default MetaPresetListingInput;