import { useModalAction } from "@components/ui/modal/modal.context";
import { useSettingsMutation } from "@data/settings/use-settings.mutation";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { Button, FormLayout, Modal } from "@shopify/polaris";
import { Settings } from "@ts-types/types";
import { renderErrors, showToast, toastOptions } from "@utils/helpers";
import { FormProvider, useForm } from "react-hook-form";
import MetaPresetListingInput from "./meta-preset-listing-input";
import { ModalContentLoader } from "@components/ui/modal/modal";

export type MetaPresetFormValues = {
  meta_presets: Array<{
    value: string;
  }>;
};

const MetaPresetModalForm: React.FC<{ settings: Settings; }> = ({ settings }) => {
  const { closeModal } = useModalAction();
  const { mutate: updateSettings, isLoading } = useSettingsMutation();
  const methods = useForm<MetaPresetFormValues>({
    defaultValues: {
      meta_presets: (settings.meta_presets ?? []).map((meta) => ({
        value: meta
      })),
    },
  });
  const {
    setValue,
    handleSubmit
  } = methods;
  function onSubmit(values: MetaPresetFormValues) {
    updateSettings({
      variables: {
        ...settings,
        meta_presets: values.meta_presets.map(({ value }) => value).filter((value) => !!value && value.length)
      }
    }, {
      onSuccess() {
        showToast(toastOptions.META_PRESETS_SAVED);
        closeModal();
      },
      onError: (response: any) => {
        const errorData = response.data;
        showToast({ message: renderErrors(errorData) ?? "Update meta presets failed", duration: 2000, isError: true });
      }
    });
  }
  return (
    <Modal.Section>
      <p className="mb-4">Manage your meta presets</p>
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <FormLayout>
            <FormLayout.Group>
              <MetaPresetListingInput />
            </FormLayout.Group>
            <div className="text-right py-4 space-x-4">
              <Button
                onClick={closeModal}
                loading={isLoading}
              >
                Cancel
              </Button>
              <Button
                primary
                submit
                loading={isLoading}
              >
                Save
              </Button>
            </div>
          </FormLayout>
        </form>
      </FormProvider>
    </Modal.Section>
  );
}

const MetaPresetModalView: React.FC = () => {
  const {
    data,
    isLoading,
    isRefetching,
  } = useSettingsQuery();
  if(isLoading || isRefetching || !data) {
    return (
      <ModalContentLoader />
    );
  }
  return <MetaPresetModalForm settings={data} />
}

export default MetaPresetModalView;