import { Checkbox, Modal as PolarisModal, Text } from "@shopify/polaris";
import { InventoryBatchPaginationData } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import React, { useState } from "react";
import SelectBatchesToAssign from "./select-batches-to-assign";
import {
  useModalAction,
  useModalState,
} from "@components/ui/modal/modal.context";
import Modal from "@components/ui/modal/modal";

const ConfirmAssignBatchesModal = ({
  open,
  setOpen,
  submit,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  submit: (selectedBatchId?: string[]) => void;
}) => {
  const [isAuto, setIsAuto] = useState(true);
  const [selectedBatchId, setSelectedBatchId] = useState<string[]>([]);

  const onConfirm = () => {
    if (isAuto) {
      submit();
    } else {
      submit(selectedBatchId);
    }
  };

  return (
    <Modal
      open={open}
      onConfirm={onConfirm}
      title="Assign Batches"
      onClose={() => setOpen(false)}
      confirmBtnText={"Confirm"}
    >
      <PolarisModal.Section>
        <div className="mb-2">
          <Checkbox
            label="Automated Batch Priority Assignment: Best-Before Precedes Expiry"
            onChange={setIsAuto}
            checked={isAuto}
          />
        </div>
        {!isAuto && (
          <>
            <Text as="h1" fontWeight="bold">
              Manually select batches to assign
            </Text>
            <SelectBatchesToAssign
              selected={selectedBatchId}
              setSelected={setSelectedBatchId}
            />
          </>
        )}
      </PolarisModal.Section>
    </Modal>
  );
};

export default ConfirmAssignBatchesModal;
