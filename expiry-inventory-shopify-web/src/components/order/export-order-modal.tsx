import React, { useEffect, useState } from "react";
import { <PERSON>List, Button, FormLayout, Label, Modal, Popover } from "@shopify/polaris";
import { Redirect } from "@shopify/app-bridge/actions";
import { useAppBridge } from "@shopify/app-bridge-react";

import DatePicker from "@components/common/date-picker";
import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { PLAN_TYPE_ENUM } from "@utils/constants";

interface ExportOrderModalProps {
    active: boolean;
    onDismiss: any;
}

function ExportOrderModal({ active: modalIsActive, onDismiss }: ExportOrderModalProps) {
    const filterOptions = [
        {
            name: "All",
            value: null
        },
        {
            name: "With assigned batch",
            value: "assigned"
        },
        {
            name: "Without assigned batch",
            value: "unassigned"
        }
    ];
    const [from, setFrom] = useState(null);
    const [to, setTo] = useState(null);
    const [filter, setFilter] = useState(filterOptions[0]);
    const [active, setActive] = useState(false);
    const [plans, setPlans] = useState<any>(null);
    const [isExporting, setIsExporting] = useState(false);
    const app = useAppBridge();
    const {
        data: settings
    } = useSettingsQuery();

    useEffect(() => {
        if(modalIsActive) {
            getPlans();
        }
    }, [modalIsActive]);

    function getPlans() {
        fetchAPI("GET", API_ENDPOINTS.PLAN)
            .then((plan_settings) => {
                setPlans(plan_settings);
            });
    }
    
    function viewPlans() {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.PLANS);
    }

    function exportOrder() {
        if(!from) {
            return showToast({ message: "From date is required", duration: 2000, isError: true });
        }
        if(!to) {
            return showToast({ message: "To date is required", duration: 2000, isError: true });
        }
        setIsExporting(true);
        const payload = {
            from,
            to,
            filter: filter.value
        };
        fetchAPI("POST", API_ENDPOINTS.ORDER_EXPORT, payload)
            .then((response: any) => {
                showToast(toastOptions.ORDER_EXPORT_SUCCESS);
                closeModal();
            })
            .catch(() => {
                showToast(toastOptions.ORDER_EXPORT_FAILED);
            })
            .finally(() => {
                setIsExporting(false);
            });
    }

    function toggleActive() {
        setActive(!active);
    }

    function closeModal() {
        setFrom(null);
        setTo(null);
        setFilter(filterOptions[0]);
        onDismiss();
    }

    return (
        <Modal
            open={modalIsActive}
            onClose={closeModal}
            title="Export order"
            primaryAction={{
                content: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? "Export" : "View plans",
                loading: isExporting,
                disabled: isExporting,
                onAction: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? exportOrder : viewPlans
            }}
            secondaryActions={[
                {
                    content: "Cancel",
                    onAction: closeModal,
                }
            ]}
        >
            <Modal.Section>
                <FormLayout>
                    {(settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? (
                        <>
                            <FormLayout.Group>
                                <DatePicker inputLabel="From date" date={from} onDateChange={setFrom} />
                                <DatePicker inputLabel="To date" date={to} onDateChange={setTo} />
                            </FormLayout.Group>
                            <FormLayout.Group>
                                <>
                                    <Label id="filter">Filter</Label>
                                    <Popover
                                        active={active}
                                        activator={
                                            <Button
                                                onClick={toggleActive}
                                                disclosure
                                            >
                                                {filter ? filter.name : "Filter"}
                                            </Button>
                                        }
                                        onClose={toggleActive}
                                    >
                                        <ActionList
                                            items={
                                                (filterOptions && filterOptions.length > 0)
                                                ? (
                                                    filterOptions.map((option) => {
                                                        return {
                                                            content: option.name,
                                                            onAction: () => {
                                                                setFilter(option);
                                                                toggleActive();
                                                            }
                                                        };
                                                    })
                                                )
                                                : []
                                            }
                                        />
                                    </Popover>
                                </>
                            </FormLayout.Group>
                        </>
                    ) : (
                        <p>This feature requires a subscription to the {plans?.pro.name} plan. Please upgrade by following the link below.</p>
                    )}
                </FormLayout>
            </Modal.Section>
        </Modal>
    );
}

export default ExportOrderModal;