import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import {
  <PERSON>ton,
  Link,
  Layout,
  Page,
  Stack,
  Thumbnail,
  Icon,
  Badge,
  Subheading,
} from "@shopify/polaris";
import {
  CheckoutMajor,
  DeleteMajor,
  NoteMinor,
  PrintMinor,
} from "@shopify/polaris-icons";
import { Redirect } from "@shopify/app-bridge/actions";
import {
  fetchAPI,
  formatDate,
  showToast,
  sumArray,
  toastOptions,
} from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { Order } from "@ts-types/types";
import { ShopifyCard } from "@components/ui/shopify/card";
import { useAppBridge } from "@shopify/app-bridge-react";
import { useShopify } from "@contexts/shopify.context";
import ConfirmAssignBatchesModal from "./confirm-assign-batches-modal";
import { useModalAction } from "@components/ui/modal/modal.context";
import OrderDetailsSkeleton from "@components/ui/loaders/order-details-skeleton";
import SelectPackingSlipItemsModal from "./select-packing-slip-items-modal";

export default function OrderDetails({ ...props }) {
  const { pathname } = useRouter();
  const { query } = props;
  const { order_id } = props.query;
  const { openModal, closeModal } = useModalAction();
  const [current_order_id, setCurrentOrderID] = useState(order_id);
  const [is_loading, setIsLoading] = useState(true);
  const [order, setOrder] = useState<Order>();
  const app = useAppBridge();
  const [openSelectBatch, setOpenSelectBatch] = useState(false);
  const [packingSlipGenerateType, setPackingSlipGenerateType] = useState<string | null>(null);
  useEffect(() => {
    if (pathname === ROUTES.ORDER_EXTENSION) {
      getOrderDetailsByShopifyID(query.id);
    } else if (current_order_id) {
      getOrderDetails();
    }
  }, []);

  function getOrderDetails() {
    setIsLoading(true);
    fetchAPI("GET", `${API_ENDPOINTS.ORDER}/${current_order_id}`)
      .then((response) => {
        setOrder(response as Order);
        setIsLoading(false);
      })
      .catch((error) => {
        setIsLoading(false);
        showToast(toastOptions.ORDER_NOT_FOUND);
        navigateBackToOrders();
      });
  }

  function getOrderDetailsByShopifyID(id: string | number) {
    setIsLoading(true);
    fetchAPI("GET", `${API_ENDPOINTS.ORDER_SHOPIFY}/${id}`)
      .then((response: any) => {
        setCurrentOrderID(response.id);
        setOrder(response);
        setIsLoading(false);
      })
      .catch((error) => {
        setIsLoading(false);
        showToast(toastOptions.ORDER_NOT_FOUND);
        navigateBackToOrders();
      });
  }

  function navigateBackToOrders() {
    const redirect = Redirect.create(app);
    redirect.dispatch(Redirect.Action.APP, ROUTES.ORDERS);
  }

  function autoAssignBatch(manual_order_list?: string[]) {
    setIsLoading(true);
    fetchAPI("POST", `${API_ENDPOINTS.ORDER}/${current_order_id}/auto-assign`, {
      manual_order_list,
    })
      .then((response: any) => {
        setIsLoading(false);
        if (response.errors && response.errors.length > 0) {
          showToast(toastOptions.ORDER_AUTO_NOT_ASSIGNED);
        } else {
          showToast(toastOptions.ORDER_AUTO_ASSIGNED);
        }
        getOrderDetails();
        setOpenSelectBatch(false);
      })
      .catch((error) => {
        showToast(toastOptions.ORDERS_ASSIGN_FAILED);
        setIsLoading(false);
      });
  }

  function unassignBatches() {
    setIsLoading(true);
    fetchAPI("POST", `${API_ENDPOINTS.ORDER}/${current_order_id}/unassign`)
      .then((response: any) => {
        setIsLoading(false);
        if (response.errors && response.errors.length > 0) {
          showToast(toastOptions.ORDERS_UNASSIGN_FAILED);
        } else {
          showToast(toastOptions.ORDER_UNASSIGNED);
        }
        getOrderDetails();
        setOpenSelectBatch(false);
      })
      .catch((error) => {
        showToast(toastOptions.ORDERS_UNASSIGN_FAILED);
        setIsLoading(false);
      });
  }

  function getUniqueBatches() {
    if (!order || !order.items) {
      return [];
    }
    const batches_list = order.items.flatMap((order_item) => {
      if (!order_item.batch_items) {
        return [];
      }
      return order_item.batch_items.flatMap((batch_item) => {
        return batch_item.batch;
      });
    });

    const unique_batches_list = [];
    for (let i = 0; i < batches_list.length; i++) {
      if (
        unique_batches_list.findIndex(
          (batch) => batch.id === batches_list[i].id
        ) === -1
      ) {
        unique_batches_list.push(batches_list[i]);
      }
    }

    return unique_batches_list;
  }

  function handleAutoAssignBatch() {
    setOpenSelectBatch(true);
  }

  function closePackingSlipSelectItemsModal() {
    setPackingSlipGenerateType(null);
  }

  function submitAction(manual_order_list?: string[]) {
    autoAssignBatch(manual_order_list);
  }

  function handleBatchClicked(batch_id: string | number) {
    openModal("EDIT_INVENTORY_BATCH_VIEW", { batch_id }, { modalTitle: "Edit inventory batch", onDismiss: handleBatchModalClosed, hideCancelBtn: true });
  }

  function handlePreviewPackingSlip() {
    setPackingSlipGenerateType("preview");
  }
  function handlePrintPackingSlip() {
    setPackingSlipGenerateType("print");
  }

  function handleBatchModalClosed(status = false) {
    closeModal();
  }

  function handleExternalLink(path: string) {
    const redirect = Redirect.create(app);
    redirect.dispatch(Redirect.Action.ADMIN_PATH, {
      path,
      newContext: true,
    });
  }

  return (
    <>
      {is_loading || !order ? (
        <OrderDetailsSkeleton />
      ) : (
        <></>
      )}
      {!is_loading && order ? (
        <Page
          breadcrumbs={[{ content: "Orders", url: ROUTES.ORDERS }]}
          title={`#${order.order_number}`}
          primaryAction={
            order.fulfillment_status == "fulfilled" ||
            order.fulfillment_status == "unfulfilled" ? (
              <Button primary onClick={() => handleAutoAssignBatch()}>
                Assign batches
              </Button>
            ) : (
              <></>
            )
          }
          secondaryActions={[
            {
              content: 'Unassign batches',
              accessibilityLabel: 'unassign batches label',
              onAction: () => unassignBatches(),
            },
          ]}
        >
          <div className="capitalize mb-4">
            <Badge
              status={
                order.batch_assign_status == "assigned"
                  ? "success"
                  : "attention"
              }
            >
              {order.batch_assign_status}
            </Badge>
          </div>
          <div className="flex justify-between items-center mb-5">
            <div className="flex gap-4">
              <Button
                icon={PrintMinor}
                onClick={handlePreviewPackingSlip}
              >
                Print packing slip
              </Button>
              <Button
                icon={NoteMinor}
                onClick={handlePrintPackingSlip}
              >
                Export as PDF
              </Button>
            </div>
            <div>
              <Link
                onClick={() =>
                  handleExternalLink(`/orders/${order.shopify_order_id}`)
                }
                removeUnderline
              >
                View order in Shopify
              </Link>
            </div>
          </div>
          <div className="mt-4">
            <Layout>
              <Layout.Section>
                <ShopifyCard
                  title={`Products (${sumArray(order.items, "quantity")})`}
                  sectioned
                >
                  {order.items.map((item, idx) => {
                    const product = item.product ?? item;
                    return (
                      <div key={idx} className="mb-4">
                        <Stack alignment="center">
                          <Thumbnail
                            alt={product.name}
                            source={
                              product.image_url
                                ? product.image_url
                                : "/assets/no-image.jpg"
                            }
                          />
                          <div>
                            {item.product ? (
                              <Link
                                url={`${ROUTES.PRODUCTS}/${product.parent_id}/${product.shopify_variant_id}`}
                                removeUnderline
                              >
                                {product.parent_name}{" "}
                                {product.name != product.parent_name
                                  ? ` (${product.name})`
                                  : ""}
                              </Link>
                            ) : (
                              <span>
                                {product.name}
                              </span>
                            )}
                            <p>
                              Quantity:
                              {item.product ? (
                                <Badge
                                  status={
                                    item.batch_items
                                      ? sumArray(item.batch_items, [
                                          "pivot",
                                          "quantity",
                                        ]) >= item.quantity
                                        ? "success"
                                        : "attention"
                                      : "attention"
                                  }
                                >
                                  {`${
                                    item.batch_items
                                      ? sumArray(item.batch_items, [
                                          "pivot",
                                          "quantity",
                                        ])
                                      : 0
                                  }/${item.quantity}`}
                                </Badge>
                              ) : (
                                item.quantity
                              )}
                            </p>
                          </div>
                        </Stack>
                        {item.batch_items ? (
                          item.batch_items.map((item_assigned, idx) => (
                            <div key={`assigned-${idx}`} className="mt-4">
                              <Stack distribution="fill">
                                <div>
                                  <Icon
                                    source={CheckoutMajor}
                                    color="base"
                                  ></Icon>
                                </div>
                                <div>
                                  <Link
                                    onClick={() =>
                                      handleBatchClicked(item_assigned.batch.id)
                                    }
                                    removeUnderline
                                  >
                                    {item_assigned.batch.name ?? `Batch #${item_assigned.batch.id}`}
                                  </Link>
                                  <p>
                                    Location:{" "}
                                    {item_assigned.batch.location.name}
                                  </p>
                                  {item_assigned.expire_at ? (
                                    <p>
                                      Expiry date:{" "}
                                      {formatDate(item_assigned.expire_at)}
                                    </p>
                                  ) : (
                                    <></>
                                  )}
                                  {item_assigned.dates ? (
                                    <p>
                                      Best Before date:{" "}
                                      {item_assigned.dates.length
                                        ? item_assigned.dates.map(({ date }, index) => (
                                          <p key={index}>{formatDate(date)}</p>
                                        ))
                                        : "—"
                                      }
                                    </p>
                                  ) : (
                                    <></>
                                  )}
                                  {item_assigned.batch.received_at ? (
                                    <p>
                                      Received date:{" "}
                                      {formatDate(
                                        item_assigned.batch.received_at
                                      )}
                                    </p>
                                  ) : (
                                    <></>
                                  )}
                                </div>
                                <div>x{item_assigned.pivot.quantity}</div>
                                <div>
                                  <div className="invisible">
                                    <Button
                                      icon={
                                        <Icon
                                          source={DeleteMajor}
                                          color="base"
                                        />
                                      }
                                      plain
                                      removeUnderline
                                    ></Button>
                                  </div>
                                </div>
                              </Stack>
                            </div>
                          ))
                        ) : (
                          <></>
                        )}
                      </div>
                    );
                  })}
                </ShopifyCard>
              </Layout.Section>
              <Layout.Section secondary>
                <>
                  <ShopifyCard title="Order details">
                    <ShopifyCard.Section>
                      <div className="capitalize">
                        <Badge>{order.status}</Badge>
                        &nbsp;
                        <Badge
                          status={
                            order.fulfillment_status == "fulfilled"
                              ? "success"
                              : "attention"
                          }
                        >
                          {order.fulfillment_status}
                        </Badge>
                      </div>
                    </ShopifyCard.Section>
                    {/* <ShopifyCard.Section title={<Subheading>Batch-tracked Products <Badge>0</Badge></Subheading>}>
                                            <p color="base">No products available</p>
                                        </ShopifyCard.Section> */}
                    <ShopifyCard.Section
                      title={
                        <Subheading>
                          Assigned Batches{" "}
                          <Badge>{`${getUniqueBatches().length}`}</Badge>
                        </Subheading>
                      }
                    ></ShopifyCard.Section>
                  </ShopifyCard>
                  {/* <ShopifyCard title="Notes">
                                        <ShopifyCard.Section>
                                            <p color="base">No notes from customer</p>
                                        </ShopifyCard.Section>
                                    </ShopifyCard> */}
                  {order.customer ? (
                    <ShopifyCard title="Customer">
                      {order.customer.shopify_customer_id && order.customer.name ? (
                        <ShopifyCard.Section>
                          <Link
                            onClick={() =>
                              handleExternalLink(
                                `/customers/${order.customer!.shopify_customer_id}`
                              )
                            }
                            removeUnderline
                          >
                            {order.customer.name}
                          </Link>
                        </ShopifyCard.Section>
                      ) : <></>}
                      {(order.customer.email || order.customer.phone) ? (
                        <ShopifyCard.Section title="Contact Information">
                          {order.customer.phone ? (
                            <Link
                              url={"mailto:" + order.customer.email}
                              removeUnderline
                              external
                            >
                              {order.customer.email}
                            </Link>
                          ) : <></>}
                          {order.customer.phone ? (
                            <div>
                              {order.customer.phone}
                            </div>
                          ) : <></>}
                        </ShopifyCard.Section>
                      ) : <></>}
                    </ShopifyCard>
                  ) : <></>}
                </>
              </Layout.Section>
            </Layout>
          </div>
          <ConfirmAssignBatchesModal
            open={openSelectBatch}
            setOpen={setOpenSelectBatch}
            submit={submitAction}
          />
          <SelectPackingSlipItemsModal
            orderId={current_order_id}
            defaultItems={order.items}
            generateType={packingSlipGenerateType}
            onDismiss={closePackingSlipSelectItemsModal}
          />
        </Page>
      ) : (
        <></>
      )}
    </>
  );
}
