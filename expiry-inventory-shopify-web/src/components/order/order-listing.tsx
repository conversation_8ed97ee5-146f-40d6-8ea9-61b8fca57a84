import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

import {
  Link,
  Button,
  Pagination,
  Badge,
  useIndexResourceState,
  IndexTable,
} from "@shopify/polaris";
import { ExportMinor, PromoteMinor, SortMinor } from "@shopify/polaris-icons";
import { Redirect } from "@shopify/app-bridge/actions";
import TabOptions from "@components/common/tab-options";

import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import EmptyDataTable from "@components/ui/loaders/empty-data-table";
import NoResult from "@components/common/no-result";
import ExportOrderModal from "@components/order/export-order-modal";
import Search from "@components/common/search";
import PopActionChoiceList from "@components/common/pop-action-choice-list";
import { ORDER_SORT_OPTIONS } from "@utils/types";
import { ROUTES } from "@utils/routes";
import { useI18n } from "@shopify/react-i18n";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import {
  Order,
  OrderBulkAssignResponse,
  OrderResponse,
  SortFilterOptions,
} from "@ts-types/types";
import { ShopifyCard } from "@components/ui/shopify/card";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { useAppBridge } from "@shopify/app-bridge-react";
import { useModalAction } from "@components/ui/modal/modal.context";
import ConfirmAssignBatchesModal from "./confirm-assign-batches-modal";

const tabOptions = [
  {
    key: "",
    label: "All",
  },
  {
    key: "assigned",
    label: "With assigned batches",
  },
  {
    key: "unassigned",
    label: "Without assigned batches",
  },
];

export default function OrderListing({ ...props }) {
  const router = useRouter();
  const { pathname } = router;
  const { query } = props;
  const [i18n] = useI18n();
  const [is_loading, setIsLoading] = useState(true);
  const [selected_tab, setSelectedTab] = useState(0);
  const [page, setPage] = useState(1);
  const [total_pages, setTotalPage] = useState(1);
  const [order_search, setOrderSearch] = useState("");
  const [order_rows, setOrderRows] = useState<Array<Order>>([]);
  const [export_modal_active, setExportModalActive] = useState(false);
  const [selected_sort, setSelectedSort] = useState<string>("created_at_desc");
  const { selectedResources, allResourcesSelected, handleSelectionChange } =
    useIndexResourceState(order_rows);
  const [currentPageAssigning, setCurrentPageAssigning] = useState(false);
  const app = useAppBridge();
  const { data: settings, isLoading: settingsLoading } = useSettingsQuery();
  const currency = settings && settings.shop ? settings.shop.currency : "USD";
  const [openSelectBatch, setOpenSelectBatch] = useState(false);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      getOrdersData();
    }, 100);
    return () => clearTimeout(delayDebounceFn);
  }, [selected_tab, page]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      getOrdersData();
    }, 1000);
    return () => clearTimeout(delayDebounceFn);
  }, [order_search]);

  useEffect(() => {
    if (pathname === ROUTES.ORDER_BULK_ASSIGN_BATCHES_EXTENSION) {
      setOpenSelectBatch(true);
    }
  }, []);

  function getOrdersData(options: SortFilterOptions | null = null) {
    let filter = tabOptions[selected_tab].key;
    const searchPayload = {
      filter,
      search: order_search,
      page,
      order_by: options && options.sort_field ? options.sort_field : null,
      order_direction:
        options && options.sort_direction ? options.sort_direction : null,
    };
    setIsLoading(true);
    fetchAPI("GET", API_ENDPOINTS.ORDER, searchPayload)
      .then((response) => {
        setTotalPage((response as OrderResponse).last_page);
        setOrderRows((response as OrderResponse).data);
      })
      .catch((error) => {
        showToast(toastOptions.ORDERS_GET_FAILED);
      })
      .finally(() => {
        setTimeout(() => {
          setIsLoading(false);
        }, 2000);
      });
  }

  function handleSelectTab(option: number) {
    setPage(1);
    setSelectedTab(option);
  }

  function handleAssignBatch() {
    setOpenSelectBatch(true);
    // setCurrentPageAssigning(true);
    // bulkAssignBatch(order_rows.map((order) => order.id));
  }

  function submitAction(manual_order_list?: string[] | number[]) {
    let ids = query["ids[]"];
    if (ids && typeof ids === "string") {
      ids = [ids];
    }
    if (pathname === ROUTES.ORDER_BULK_ASSIGN_BATCHES_EXTENSION) {
      bulkShopifyAssign(ids, manual_order_list);
      router.push(ROUTES.ORDERS);
    } else if (selectedResources.length <= 0) {
      bulkAssignBatch(
        order_rows.map((order) => order.id),
        manual_order_list
      );
    } else {
      bulkAssignBatch(selectedResources, manual_order_list);
    }
  }

  function bulkAssignBatch(
    order_ids: Array<string | number> = selectedResources,
    manual_order_list?: string[] | number[]
  ) {
    setIsLoading(true);
    fetchAPI("POST", API_ENDPOINTS.ORDER_BULK_ASSIGN, {
      order_ids,
      manual_order_list,
    })
      .then((response) => {
        setIsLoading(false);
        if (
          (response as OrderBulkAssignResponse).errors &&
          (response as OrderBulkAssignResponse).errors.length > 0
        ) {
          showToast(toastOptions.ORDERS_AUTO_NOT_ASSIGNED);
        } else {
          showToast(toastOptions.ORDERS_AUTO_ASSIGNED);
        }
        getOrdersData();
        setOpenSelectBatch(false);
      })
      .catch((error) => {
        showToast(toastOptions.ORDERS_ASSIGN_FAILED);
        setIsLoading(false);
      })
      .finally(() => {
        setCurrentPageAssigning(false);
      });
  }

  function bulkShopifyAssign(
    order_ids: Array<string>,
    manual_order_list?: string[] | number[]
  ) {
    setIsLoading(true);
    fetchAPI("POST", API_ENDPOINTS.ORDER_BULK_ASSIGN_SHOPIFY, {
      order_ids,
      manual_order_list,
    })
      .then((response) => {
        setIsLoading(false);
        if (
          (response as OrderBulkAssignResponse).errors &&
          (response as OrderBulkAssignResponse).errors.length > 0
        ) {
          showToast(toastOptions.ORDERS_AUTO_NOT_ASSIGNED);
        } else {
          showToast(toastOptions.ORDERS_AUTO_ASSIGNED);
        }
        getOrdersData();
      })
      .catch((error) => {
        showToast(toastOptions.ORDERS_ASSIGN_FAILED);
        setIsLoading(false);
      });
  }

  function handleOrderSort(sort_value: Array<string>) {
    if (sort_value.length > 0) {
      const sort = sort_value[0];
      const sort_option = ORDER_SORT_OPTIONS.find(
        ({ value }) => value === sort
      );
      if (sort_option) {
        setSelectedSort(sort_value[0]);
        getOrdersData({
          sort_field: sort_option.field,
          sort_direction: sort_option.order,
        });
      }
    }
  }

  function toggleExportModal() {
    setExportModalActive(!export_modal_active);
  }

  function handleExternalLink(path: string) {
    const redirect = Redirect.create(app);
    redirect.dispatch(Redirect.Action.ADMIN_PATH, {
      path,
      newContext: true,
    });
  }

  return (
    <>
      <div className="flex justify-between items-center mb-5">
        <div className="flex">
          <div className="mr-4">
            <Button icon={ExportMinor} onClick={toggleExportModal}>
              Export
            </Button>
          </div>
          <div className="mr-4">
            <Button
              loading={currentPageAssigning}
              disabled={currentPageAssigning}
              onClick={handleAssignBatch}
            >
              Assign batches
            </Button>
          </div>
        </div>
        <div>
          {/* @ts-ignore */}
          <Link
            onClick={() => handleExternalLink("/orders")}
            /* @ts-ignore */
            icon={PromoteMinor}
            removeUnderline
          >
            View all orders in Shopify
          </Link>
        </div>
      </div>
      <ShopifyCard>
        <div>
          <div className="flex items-stretch border-b">
            <TabOptions
              options={tabOptions}
              selected={selected_tab}
              onChange={handleSelectTab}
            />
          </div>
          <div className="flex items-stretch gap-2 px-4 py-2">
            <div className="flex-1">
              <Search
                value={order_search}
                onChange={setOrderSearch}
                placeholder="Search orders and customers"
                showClear={true}
              />
            </div>
            <PopActionChoiceList
              options={ORDER_SORT_OPTIONS}
              //@ts-ignore
              buttonIcon={SortMinor}
              data_display="Sort"
              current_value={[selected_sort]}
              setData={handleOrderSort}
            />
          </div>
          {is_loading || settingsLoading ? <EmptyDataTable /> : <></>}
          {!is_loading &&
          !settingsLoading &&
          (!order_rows || order_rows.length <= 0) ? (
            <NoResult
              is_filtering={selected_tab}
              onResetButtonClicked={() => setSelectedTab(0)}
            />
          ) : (
            <></>
          )}
          {order_rows.length > 0 ? (
            <div className={is_loading || settingsLoading ? "hidden" : ""}>
              <IndexTable
                hideScrollIndicator
                resourceName={{
                  singular: "order",
                  plural: "orders",
                }}
                itemCount={order_rows.length}
                selectedItemsCount={
                  allResourcesSelected ? "All" : selectedResources.length
                }
                onSelectionChange={handleSelectionChange}
                promotedBulkActions={[
                  {
                    content: "Assign batches",
                    onAction: () => handleAssignBatch(),
                  },
                ]}
                headings={[
                  //@ts-ignore
                  { title: "Order", key: "order" },
                  { title: "Customer", key: "customer" },
                  { title: "Total", key: "total" },
                  { title: "Payment", key: "payment" },
                  { title: "Fulfillment", key: "fulfillment" },
                  { title: "Batches", key: "batches" },
                  { title: "Assignment", key: "assignment" },
                ]}
              >
                {order_rows.map((row, index) => {
                  const batches_count = row.items.reduce(
                    (t, it) => t + (it.batch_items ? it.batch_items.length : 0),
                    0
                  );
                  return (
                    <IndexTable.Row
                      id={row.id as string}
                      key={row.id}
                      selected={selectedResources.includes(row.id as string)}
                      position={index}
                    >
                      <IndexTable.Cell>
                        <div>
                          <Link
                            url={`${ROUTES.ORDERS}/${row.id}`}
                            removeUnderline
                          >
                            #{row.order_number}
                          </Link>
                        </div>
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        {row.customer ? row.customer.name : (row.billing_address ? row.billing_address.name : "")}
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        {i18n.formatCurrency(row.amount, {
                          currency,
                          form: "short",
                        })}
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        <div className="capitalize">{row.status}</div>
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        <div className="capitalize">
                          <Badge
                            status={
                              row.fulfillment_status == "fulfilled"
                                ? "success"
                                : "attention"
                            }
                          >
                            {row.fulfillment_status}
                          </Badge>
                        </div>
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        {batches_count +
                          " batch" +
                          (batches_count != 1 ? "es" : "")}
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        <div className="capitalize">
                          <Badge
                            progress={
                              row.batch_assign_status == "assigned"
                                ? "complete"
                                : "incomplete"
                            }
                            status={
                              row.batch_assign_status == "assigned"
                                ? "success"
                                : "attention"
                            }
                          >
                            {row.batch_assign_status}
                          </Badge>
                        </div>
                      </IndexTable.Cell>
                    </IndexTable.Row>
                  );
                })}
              </IndexTable>
            </div>
          ) : (
            <></>
          )}
        </div>
        <ExportOrderModal
          active={export_modal_active}
          onDismiss={toggleExportModal}
        ></ExportOrderModal>
      </ShopifyCard>
      <div id="order-paginator">
        <Pagination
          hasPrevious={1 < page}
          onPrevious={() => {
            setPage(page - 1);
          }}
          hasNext={page < total_pages}
          onNext={() => {
            setPage(page + 1);
          }}
        />
      </div>
      <ConfirmAssignBatchesModal
        open={openSelectBatch}
        setOpen={setOpenSelectBatch}
        submit={submitAction}
      />
    </>
  );
}
