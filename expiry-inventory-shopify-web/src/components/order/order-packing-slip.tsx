import React, { useEffect, useState } from "react";
import { ReactLiquid } from "react-liquid";

import { fetchAPIWithShopURL } from "@utils/helpers";
import NotFound from "@components/common/404";
import OrderMockupData from "@data/sample/order-mockup-data";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopify } from "@contexts/shopify.context";
import PageLoading from "@components/common/page-loading";
import moment from "moment";

export default function OrderPackingSlip({ order_id, items, ...props }: any) {
    const { shop } = useShopify();
    const [is_error, setIsError] = useState(false);
    const [order, setOrder] = useState<typeof OrderMockupData | null>(null);
    const [template, setTemplate] = useState<any | null>(null);

    useEffect(() => {
        if(shop) {
            getPrintTemplate(shop);
            if(order_id) {
                getPrintOrderData(shop);
            }
            else {
                setOrder(OrderMockupData);
            }
        }
    }, [shop]);

    function getPrintTemplate(shop: string) {
        fetchAPIWithShopURL("GET", API_ENDPOINTS.PREFERENCE_PRINT, shop)
            .then((response) => {
                setTemplate(response);
            })
            .catch((error) => {
                setIsError(true);
            });
        }
    function localDateStringToSafeDate(dateString: string | null) {
        if(!dateString) {
            return null;
        }
        const date = moment.utc(dateString).set({ hour: 12 });
        return date.toDate();
    }

    function getPrintOrderData(shop: string) {
        fetchAPIWithShopURL("GET", `${API_ENDPOINTS.ORDER}/${order_id}/print`, shop, items ? { itemstr: items } : undefined)
            .then((response: any) => {
                const orderData = {
                    ...response,
                    created_at: localDateStringToSafeDate(response.created_at),
                    items: response.items.map((item: any) => {
                        return {
                            ...item,
                            batches: item.batches.map((batch: any) => {
                                return {
                                    ...batch,
                                    best_before: batch.best_before.map((date: string) => localDateStringToSafeDate(date)),
                                    expire_at: localDateStringToSafeDate(batch.expire_at),
                                    received_at: localDateStringToSafeDate(batch.received_at),
                                };
                            })
                        };
                    })
                } as (typeof OrderMockupData);

                setOrder(orderData);
            })
            .catch((error) => {
                setIsError(true);
            });
    }

    function handleRenderCompleted() {
        if(props.onRenderCompleted) {
            props.onRenderCompleted();
        }
    }

    if(!shop) {
        return <PageLoading />;
    }

    if(is_error) {
        return (<NotFound />);
    }

    if(!order || !template) {
        return null;
    }

    return (
        <ReactLiquid
            template={template}
            data={order}
            render={(renderedTemplate: any) => {
                if(renderedTemplate.__html) {
                    handleRenderCompleted();
                }
                return <div dangerouslySetInnerHTML={renderedTemplate} />
            }}
        />
    );
}