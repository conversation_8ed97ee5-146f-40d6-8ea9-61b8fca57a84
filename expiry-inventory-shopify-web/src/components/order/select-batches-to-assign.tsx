import Search from "@components/common/search";

import {
  IndexTable,
  LegacyCard,
  useIndexResourceState,
  Text,
  useBreakpoints,
  Pagination,
  Badge,
} from "@shopify/polaris";
import { Status } from "@shopify/polaris/build/ts/latest/src/components/Badge";
import { IndexTableSortDirection } from "@shopify/polaris/build/ts/latest/src/components/IndexTable";

import { InventoryBatch, InventoryBatchPaginationData } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { fetchAPI, formatDate, showToast, toastOptions } from "@utils/helpers";
import React, { useEffect, useState } from "react";

export default function SelectBatchesToAssign({
  selected,
  setSelected,
}: {
  selected: Array<string>;
  setSelected: (selected: Array<string>) => void;
}) {
  const resourceName = {
    singular: "batch",
    plural: "batch_rows",
  };

  const [batch_rows, setBatchRows] = useState<Array<InventoryBatch>>([]);
  const { selectedResources, allResourcesSelected, handleSelectionChange } =
    useIndexResourceState(batch_rows, {
      allResourcesSelected: false,
      resourceIDResolver: (resource) => `b-${resource.id.toString()}`,
    });

  useEffect(() => {
    setSelected([
      ...selectedResources.map((resource) =>
        typeof resource === "string" ? resource?.split("-")[1] : resource
      ),
    ]);
  }, [selectedResources]);

  const [page, setPage] = useState(1);
  const [total_pages, setTotalPage] = useState(1);
  const [is_loading, setIsLoading] = useState(true);
  const [sortColumnIndex, setSortColumnIndex] = useState(0);
  const [sortDirection, setSortDirection] = useState<IndexTableSortDirection>("ascending");

  const [batch_search, setBatchSearch] = useState("");
  const [sortOptions, setSortOptions] = useState<{ sort_direction: "ASC" | "DESC"; sort_field: string | number | null; }>({
    sort_direction: "DESC",
    sort_field: 0,
  });
  const statusColors: Array<{ key: string; value: Status }> = [
    {
      key: "in_stock",
      value: "success",
    },
    {
      key: "discounted",
      value: "warning",
    },
    {
      key: "out_of_stock",
      value: "warning",
    },
    {
      key: "expired",
      value: "critical",
    },
  ];
  const tabOptions = [
    {
      key: "",
      label: "All",
    },
    {
      key: "in_stock",
      label: "In stock",
    },
    {
      key: "discounted",
      label: "Discounted",
    },
    {
      key: "out_of_stock",
      label: "Out of stock",
    },
    {
      key: "expired",
      label: "Expired",
    },
  ];

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      getInventoryBatchesData();
    }, 1000);
    return () => clearTimeout(delayDebounceFn);
  }, [batch_search]);
  function handleBatchSort(index: number, direction: IndexTableSortDirection) {
    setSortColumnIndex(index);
    setSortDirection(direction);
    let field = null;
    switch (index) {
      case 0:
        field = "id";
        break;
      case 1:
        field = "name";
        break;
      case 2:
        field = "product";
        break;
      case 3:
        field = "status";
        break;
      case 4:
        field = "expiry_date";
        break;
      case 5:
        field = "best_before";
        break;
      case 6:
        field = "quantity";
        break;

      default:
        return;
    }
    setSortOptions({
      sort_field: field,
      sort_direction: direction == "ascending" ? "ASC" : "DESC",
    });
  }

  useEffect(() => {
    getInventoryBatchesData();
  }, [page, sortOptions]);

  function getInventoryBatchesData() {
    setIsLoading(true);
    const searchPayload = {
      // location: selected_location.id,
      filter: "in_stock,discounted",
      page,
      search: batch_search,
      order_by:
        sortOptions && sortOptions.sort_field
          ? sortOptions.sort_field
          : "created_at",
      order_direction:
        sortOptions && sortOptions.sort_direction
          ? sortOptions.sort_direction
          : "DESC",
    };
    fetchAPI("GET", API_ENDPOINTS.INVENTORY_BATCH, searchPayload)
      .then((response) => {
        setTotalPage((response as InventoryBatchPaginationData).last_page);
        setBatchRows((response as InventoryBatchPaginationData).data);
      })
      .catch((error) => {
        showToast(toastOptions.BATCH_NOT_FOUND);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }
  function renderBadgeStatus(status: string) {
    const status_color =
      statusColors.find((option) => option.key === status)?.value ?? "success";
    const status_label =
      tabOptions.find((option) => option.key === status)?.label ?? status;
    return <Badge status={status_color}>{status_label}</Badge>;
  }
  const rowMarkup = batch_rows.map(
    ({ id, name, location, items, status }, index) => {
      const batch_item = items[0];

      return (
        <IndexTable.Row
          id={`b-${id}` as string}
          key={`b${id}`}
          selected={selectedResources.includes(`b-${id}` as string)}
          position={index}
        >
          <IndexTable.Cell>
            <Text as="span">{id}</Text>
          </IndexTable.Cell>
          <IndexTable.Cell>
            <Text fontWeight="bold" as="span">
              {name ?? `Batch #${id}`}
            </Text>
          </IndexTable.Cell>

          <IndexTable.Cell>
            <Text as="span" alignment="end" numeric>
              {items[0] ? items[0].product.name : "-"}
            </Text>
          </IndexTable.Cell>
          <IndexTable.Cell>
            <Text as="span" alignment="end" numeric>
              {renderBadgeStatus(status)}
            </Text>
          </IndexTable.Cell>

          <IndexTable.Cell>
            <Text as="span" alignment="end" numeric>
              {batch_item?.expire_at ? formatDate(batch_item.expire_at) : "—"}
            </Text>
          </IndexTable.Cell>

          <IndexTable.Cell>
            <Text as="span" alignment="end" numeric>
              {batch_item?.dates && batch_item.dates.length
                ? batch_item.dates.map(({ date }, index) => (
                  <p key={index}>{formatDate(date)}</p>
                ))
                : "—"}
            </Text>
          </IndexTable.Cell>

          <IndexTable.Cell>
            <Text as="span" alignment="end" numeric>
              {batch_item?.quantity}
            </Text>
          </IndexTable.Cell>
        </IndexTable.Row>
      );
    }
  );

  return (
    <LegacyCard>
      <div className="flex items-stretch border-b">
        <div className="self-center m-2">
          <Search
            value={batch_search}
            onChange={setBatchSearch}
            placeholder="Search batch details"
            showClear={true}
          />
        </div>
        {/* <div className="self-center ml-auto m-2">
          <LocationPicker />
        </div> */}
      </div>
      <div className="m-2">
        <Text as="span">
          Choose the batches in sequence for prioritized processing.
        </Text>
        <div className="flex gap-2">
          {selected.map((el) => (
            <Badge status="new" key={el}>
              {el}
            </Badge>
          ))}
        </div>
      </div>

      <IndexTable
        id="inventory-batch-table"
        key={"batch-table"}
        condensed={useBreakpoints().smDown}
        resourceName={resourceName}
        loading={is_loading}
        itemCount={batch_rows.length}
        selectedItemsCount={
          allResourcesSelected ? "All" : selectedResources.length
        }
        onSelectionChange={handleSelectionChange}
        headings={[
          //@ts-ignore
          { title: "Batch ID", key: "id" },
          { title: "Batch name", key: "batch_name", alignment: "end" },
          {
            title: "Product",
            alignment: "end",
          },
          {
            title: "Status",
            alignment: "end",
          },
          {
            title: "Expiry date",
            alignment: "end",
          },
          {
            title: "Best Before date",
            alignment: "end",
          },
          {
            title: "Quantity Left",
            alignment: "end",
          },
        ]}
        sortColumnIndex={sortColumnIndex}
        sortDirection={sortDirection}
        sortable={[true, true, false, true, true, true, true]}
        onSort={handleBatchSort}
      >
        {rowMarkup}
      </IndexTable>
      <div id="inventory-batch-paginator">
        {" "}
        <Pagination
          hasPrevious={1 < page}
          onPrevious={() => {
            setPage((page) => page - 1);
          }}
          hasNext={page < total_pages}
          onNext={() => {
            setPage((page) => page + 1);
          }}
        />
      </div>
    </LegacyCard>
  );
}
