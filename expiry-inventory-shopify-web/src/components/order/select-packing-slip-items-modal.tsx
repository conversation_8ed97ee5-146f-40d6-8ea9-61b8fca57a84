import { useShopify } from "@contexts/shopify.context";
import { Checkbox, FormLayout, Modal } from "@shopify/polaris";
import { OrderItem, PackingSlipRenderItem } from "@ts-types/types";
import { generatePackingSlipItemsCipher } from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import { useEffect, useMemo, useState } from "react";

const SelectPackingSlipItemsModal = ({
  orderId,
  defaultItems,
  generateType,
  onDismiss
}: {
  orderId: string | number;
  defaultItems: Array<OrderItem>;
  generateType: string | null;
  onDismiss: any;
}) => {
  const { shop, host } = useShopify();
  const [isAllItems, setIsAllItems] = useState(true);
  const [items, setItems] = useState<Array<PackingSlipRenderItem>>([]);

  function handleItemChecked(index: number, is_checked: boolean) {
    const newItems = [...items];
    newItems[index].checked = is_checked;
    newItems[index].assignments = newItems[index].assignments.map((a) => ({ ...a, checked: is_checked }));
    setItems(newItems);
  }
  function handleAssignmentItemChecked(index: number, assignment_index: number, is_checked: boolean) {
    const newItems = [...items];
    newItems[index].assignments[assignment_index].checked = is_checked;
    newItems[index].checked = newItems[index].assignments.some(({ checked }) => checked);
    setItems(newItems);
  }

  function initializeItems() {
    const selectionItems = defaultItems.map(({ id, name, batch_items, quantity, product }) => {
      const unassignedQuantity = quantity - batch_items.reduce((p, { pivot }) => p + pivot.quantity, 0);
      return {
        order_item_id: id,
        name: product?.display_name ?? name,
        total_quantity: quantity,
        checked: true,
        assignments: [
          ...batch_items.map(({ id, pivot, batch }) => ({
            batch_item_id: id,
            assignment_id: pivot.id,
            batch_name: batch.name,
            quantity: pivot.quantity,
            checked: true,
          })),
          ...(unassignedQuantity ? [{
            batch_item_id: null,
            assignment_id: null,
            batch_name: null,
            quantity: unassignedQuantity,
            checked: true,
          }] : []),
        ]
      }
    });
    setItems(selectionItems);
  }

  function closeModal() {
    initializeItems();
    setIsAllItems(true);
    onDismiss();
  }

  useEffect(() => {
    if (defaultItems) {
      initializeItems();
    }
  }, [defaultItems]);

  const packingSlipURL = useMemo(() => {
    if (!isAllItems && items.every(({ checked }) => !checked)) {
      return undefined;
    }
    const items_string = generatePackingSlipItemsCipher(isAllItems ? [] : items);
    return `${generateType == "print" ? ROUTES.PACKING_SLIP_PDF : ROUTES.PACKING_SLIP_PREVIEW}/${orderId}?shop=${shop}&host=${host}` + (items_string ? `&items=${items_string}` : "");
  }, [generateType, items]);

  return (
    <Modal
      open={!!generateType}
      onClose={closeModal}
      title="Select items to generate packing slip"
      primaryAction={{
        content: "Generate",
        onAction: closeModal,
        disabled: !packingSlipURL,
        url: packingSlipURL,
        external: true,
      }}
      secondaryActions={[
        {
          content: "Cancel",
          onAction: closeModal,
        }
      ]}
    >
      <Modal.Section>
        <FormLayout>
          <FormLayout.Group>
            <>
              <Checkbox
                label="All items"
                checked={isAllItems}
                onChange={setIsAllItems}
              />
              {!isAllItems ? (
                <div className="mt-2">
                  {items.map(({ name, total_quantity, checked: itemChecked, assignments }, index) => (
                    <div key={index}>
                      <Checkbox
                        id={`item-${index}`}
                        label={`${name} (Quantity: ${total_quantity})`}
                        checked={itemChecked}
                        onChange={(value) => handleItemChecked(index, value)}
                      />
                      {assignments.length ? (
                        <div className="pl-4 mt-1">
                          {assignments.map(({ batch_name, quantity, checked: assignmentChecked }, assignment_index) => (
                            <div key={`${index}-${assignment_index}`}>
                              <Checkbox
                                id={`assignment-${index}-${assignment_index}`}
                                label={`${batch_name ?? "Unassigned"} (${quantity})`}
                                checked={assignmentChecked}
                                onChange={(value) => handleAssignmentItemChecked(index, assignment_index, value)}
                              />
                            </div>
                          ))}
                        </div>
                      ) : <></>}
                    </div>
                  ))}
                </div>
              ) : <></>}
            </>
          </FormLayout.Group>
        </FormLayout>
      </Modal.Section>
    </Modal>
  );
};

export default SelectPackingSlipItemsModal;
