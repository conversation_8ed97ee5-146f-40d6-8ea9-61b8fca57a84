import { Modal } from "@shopify/polaris";

interface PlanDowngradeConfirmModalProps {
    active: boolean;
    onConfirm: any;
    onDismiss: any;
}

const PlanDowngradeConfirmModal = ({ active, onConfirm, onDismiss }: PlanDowngradeConfirmModalProps) => {
    return (
        <Modal
            open={active}
            onClose={() => onDismiss(false)}
            title="Plan Downgrade / Unsubscribe"
            primaryAction={{
                content: "Confirm",
                onAction: onConfirm
            }}
            secondaryActions={[
                {
                    content: "Cancel",
                    onAction: () => onDismiss(false),
                }
            ]}
        >
            <Modal.Section>
                <p>Are you sure to downgrade / unsubscribe current plan?</p>
            </Modal.Section>
        </Modal>
    );
}

export default PlanDowngradeConfirmModal;