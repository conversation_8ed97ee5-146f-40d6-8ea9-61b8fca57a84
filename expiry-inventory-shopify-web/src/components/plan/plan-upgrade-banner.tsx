import { useState } from "react";
import { Banner } from "@shopify/polaris";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { ROUTES } from "@utils/routes";

export default function UpgradePlanBanner() {
    const app = useAppBridge();
    const [hide_banner, setHideBanner] = useState(false);

    function handleUpgrade() {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.PLANS);
    }
    
    return (
        !hide_banner ? (
            <Banner
                title="Plan Upgrade"
                action={{content: "Upgrade", onAction: handleUpgrade}}
                status="info"
                onDismiss={() => { setHideBanner(true) }}
            >
                <p>You're still using our free plan. Click the upgrade button below to have access to more awesome features!</p>
            </Banner>
        ) : <></>
    );
}