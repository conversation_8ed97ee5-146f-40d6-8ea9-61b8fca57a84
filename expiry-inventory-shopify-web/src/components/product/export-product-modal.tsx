import React, { useEffect, useState } from "react";
import { ActionList, Button, Checkbox, FormLayout, Label, Modal, Popover } from "@shopify/polaris";

import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { useProductExportMutation } from "@data/product/use-export-product.mutation";
import { useUI } from "@contexts/ui.context";
import { Location } from "@ts-types/types";
import { PLAN_TYPE_ENUM } from "@utils/constants";

interface ExportProductModalProps {
    exportInitialValues: {
        filter: { key: string; label: string; },
        location: { id: string; name: string; },
        inventory_status: string[],
        with_unaccounted_quantity: boolean;
    }
    active: boolean;
    onDismiss: any;
}

function ExportProductModal(props: ExportProductModalProps) {
    const filterOptions = [
        {
            key: "",
            label: "All",
        },
        {
            key: "with_batches",
            label: "With batches",
        },
        {
            key: "without_batches",
            label: "Without batches",
        },
        {
            key: "discounted",
            label: "Discounted",
        },
    ];
    const inventoryStatusFilterOptions = [
        {
            key: "",
            label: "All",
        },
        {
            key: "in_stock",
            label: "In stock",
        },
        {
            key: "expired",
            label: "Expired",
        },
        {
            key: "past_best_before",
            label: "Past best before",
        },
    ];
    const [location, setLocation] = useState<{ id: string | number | null; name: string; }>({ name: "All", id: null });
    const [filter, setFilter] = useState(filterOptions[0]);
    const [inventoryStatus, setInventoryStatus] = useState<{ key: string; label: string; }>(inventoryStatusFilterOptions[0]);
    const [is_with_unaccounted_quantity, setIsWithUnaccountedQuantity] = useState(false);
    const [active, setActive] = useState(false);
    const { locations } = useUI();
    const [locationActive, setLocationActive] = useState(false);
    const [inventoryStatusActive, setInventoryStatusActive] = useState(false);
    const [plans, setPlans] = useState<any | null>(null);
    const app = useAppBridge();
    const { mutate: exportProduct, isLoading: isExporting } = useProductExportMutation();
    const {
        data: settings,
    } = useSettingsQuery();

    useEffect(() => {
        if(props.active) {
            getPlans();
        }
    }, [props.active]);

    useEffect(() => {
        if(props.active) {
            setFilter(props.exportInitialValues.filter);
            setLocation(props.exportInitialValues.location);
            setInventoryStatus(props.exportInitialValues.inventory_status.length ? (inventoryStatusFilterOptions.find(({ key }) => key == props.exportInitialValues.inventory_status[0]) ?? inventoryStatusFilterOptions[0]) : inventoryStatusFilterOptions[0]);
            setIsWithUnaccountedQuantity(props.exportInitialValues.with_unaccounted_quantity);
        }
    }, [props.active, props.exportInitialValues]);

    function getPlans() {
        fetchAPI("GET", API_ENDPOINTS.PLAN)
            .then((plan_settings: any) => {
                setPlans(plan_settings);
            });
    }

    function viewPlans() {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.PLANS);
    }

    function handleExportProduct() {
        let location_id = location.id;
        const payload = {
            location: location_id,
            ...(inventoryStatus.key != "" ? { inventory_status: inventoryStatus.key } : {}),
            ...(is_with_unaccounted_quantity ? { with_unaccounted_quantity: 1 } : {}),
            filter: filter.key,
        };
        exportProduct(payload, {
            onSuccess: () => {
                showToast(toastOptions.PRODUCT_EXPORT_SUCCESS);
                closeModal();
            },
            onError: () => {
                showToast(toastOptions.PRODUCT_EXPORT_FAILED);
            }
        });
    }

    function toggleActive() {
        setActive(!active);
    }

    function toggleLocationActive() {
        setLocationActive(!locationActive);
    }

    function toggleInventoryStatusActive() {
        setInventoryStatusActive(!inventoryStatusActive);
    }

    function closeModal() {
        setFilter(filterOptions[0]);
        props.onDismiss();
    }

    return (
        <Modal
            open={props.active}
            onClose={closeModal}
            title="Export product"
            primaryAction={{
                content: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? "Export" : "View plans",
                loading: isExporting,
                disabled: isExporting,
                onAction: (settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? handleExportProduct : viewPlans,
            }}
            secondaryActions={[
                {
                    content: "Cancel",
                    onAction: closeModal,
                }
            ]}
        >
            <Modal.Section>
                <FormLayout>
                    {(settings && settings.plan != PLAN_TYPE_ENUM.FREE) ? (
                        <>
                            <FormLayout.Group>
                                <>
                                    <Label id="location">Location</Label>
                                    <Popover
                                        active={locationActive}
                                        activator={
                                            <Button
                                                onClick={toggleLocationActive}
                                                disclosure
                                            >
                                                {location ? location.name : "Location"}
                                            </Button>
                                        }
                                        onClose={toggleLocationActive}
                                    >
                                        <ActionList
                                            items={[
                                                {
                                                    content: "All",
                                                    onAction: () => {
                                                        setLocation({ id: null, name: "All" });
                                                        toggleLocationActive();
                                                    }
                                                },
                                                ...((locations && locations.length > 0)
                                                    ? (
                                                        locations.map(({ id, name }: Location) => {
                                                            return {
                                                                content: name,
                                                                onAction: () => {
                                                                    setLocation({ id, name });
                                                                    toggleLocationActive();
                                                                }
                                                            };
                                                        })
                                                    )
                                                    : [])
                                            ]}
                                        />
                                    </Popover>
                                </>
                            </FormLayout.Group>
                            <FormLayout.Group>
                                <>
                                    <Label id="filter">Filter</Label>
                                    <Popover
                                        active={active}
                                        activator={
                                            <Button
                                                onClick={toggleActive}
                                                disclosure
                                            >
                                                {filter ? filter.label : "Filter"}
                                            </Button>
                                        }
                                        onClose={toggleActive}
                                    >
                                        <ActionList
                                            items={
                                                (filterOptions && filterOptions.length > 0)
                                                ? (
                                                    filterOptions.map((option) => {
                                                        return {
                                                            content: option.label,
                                                            onAction: () => {
                                                                setFilter(option);
                                                                toggleActive();
                                                            }
                                                        };
                                                    })
                                                )
                                                : []
                                            }
                                        />
                                    </Popover>
                                </>
                            </FormLayout.Group>
                            <FormLayout.Group>
                                <>
                                    <Label id="inventory_status">Inventory Status</Label>
                                    <Popover
                                        active={inventoryStatusActive}
                                        activator={
                                            <Button
                                                onClick={toggleInventoryStatusActive}
                                                disclosure
                                            >
                                                {inventoryStatus ? inventoryStatus.label : "Filter inventory status"}
                                            </Button>
                                        }
                                        onClose={toggleInventoryStatusActive}
                                    >
                                        <ActionList
                                            items={
                                                (inventoryStatusFilterOptions && inventoryStatusFilterOptions.length > 0)
                                                ? (
                                                    inventoryStatusFilterOptions.map((option) => {
                                                        return {
                                                            content: option.label,
                                                            onAction: () => {
                                                                setInventoryStatus(option);
                                                                toggleInventoryStatusActive();
                                                            }
                                                        };
                                                    })
                                                )
                                                : []
                                            }
                                        />
                                    </Popover>
                                </>
                            </FormLayout.Group>
                            <FormLayout.Group>
                                <>
                                    <Label id="batched_status">Batched Status</Label>
                                    <Checkbox
                                        label="With unaccounted quantity"
                                        checked={is_with_unaccounted_quantity}
                                        onChange={setIsWithUnaccountedQuantity}
                                    />
                                </>
                            </FormLayout.Group>
                        </>
                    ) : (
                        <p>This feature requires a subscription to the {plans?.pro.name} plan. Please upgrade by following the link below.</p>
                    )}
                </FormLayout>
            </Modal.Section>
        </Modal>
    );
}

export default ExportProductModal;