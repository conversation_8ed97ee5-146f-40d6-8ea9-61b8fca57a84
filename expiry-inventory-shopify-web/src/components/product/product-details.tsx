import {
  <PERSON><PERSON>,
  FormLayout,
  Layout,
  OptionL<PERSON>,
  Page,
  SkeletonBodyText,
  SkeletonDisplayText,
  Stack,
  TextContainer,
  TextField,
  Thumbnail,
  DataTable,
  Banner,
  Link,
} from "@shopify/polaris";
import TabOptions from "@components/common/tab-options";
import {
  fetchAPI,
  formatDate,
  showToast,
  sumArray,
  toastOptions,
} from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import { useState, useEffect } from "react";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { InventoryBatch, Location, Product, ProductStoreURL } from "@ts-types/types";
import { useUI } from "@contexts/ui.context";
import { ShopifyText } from "@components/ui/shopify/text";
import { ShopifyCard } from "@components/ui/shopify/card";
import { Redirect } from "@shopify/app-bridge/actions";
import { useAppBridge } from "@shopify/app-bridge-react";
import { useRouter } from "next/router";
import ProductDetailsSkeleton from "@components/ui/loaders/product-details-skeleton";
import { useModalAction } from "@components/ui/modal/modal.context";
import { BATCH_STATUS_ENUM } from "@utils/constants";
import { useProductStoreURLMutation } from "@data/product/use-product-store-url.mutation";
import Loader from "@components/common/loader";
import { useSettingsQuery } from "@data/settings/use-settings.query";

const tabOptions = [
  {
    key: BATCH_STATUS_ENUM.IN_STOCK,
    label: "In stock",
  },
  {
    key: BATCH_STATUS_ENUM.DISCOUNTED,
    label: "Discounted",
  },
  {
    key: BATCH_STATUS_ENUM.OUT_OF_STOCK,
    label: "Out of stock",
  },
  {
    key: "past_best_before",
    label: "Past Best Before",
  },
  {
    key: "upcoming_best_before",
    label: "Upcoming Best Before",
  },
  {
    key: BATCH_STATUS_ENUM.EXPIRED,
    label: "Expired",
  },
];

export default function ProductDetails({
  product_id,
  variant_id,
  variants,
  selected_variant,
  ...props
}: any) {
  const { query } = props;
  const router = useRouter();
  const { openModal, closeModal } = useModalAction();
  const { location: selected_location, locations, setLocation } = useUI();
  const { mutate: getProductStoreURL, isLoading: isLoadingStoreURL } = useProductStoreURLMutation();
  const [is_loading, setIsLoading] = useState(true);
  const [is_auto_creating, setIsAutoCreating] = useState(false);
  const [is_variant_loading, setIsVariantLoading] = useState(true);
  const [product_variants, setProductVariants] = useState<Array<Product>>([]);
  const [selected_variant_id, setSelectedVariantId] = useState<
    string | number | null
  >(null);
  const [variant_details, setVariantDetails] = useState<Product | null>(null);
  const [selected_tab, setSelectedTab] = useState(0);
  const { data: settings, isLoading: settingsLoading } = useSettingsQuery();
  const app = useAppBridge();

  useEffect(() => {
    const _status = query.status;
    const _location = query.location;
    if (_status) {
      let initial_tab = 0;
      const tab_index = tabOptions.findIndex((t) => t.key == _status);
      if (tab_index != -1) {
        initial_tab = tab_index;
      }
      setSelectedTab(initial_tab);
    }
    if (_location) {
      const location_obj = locations.find(
        (location: Location) => location.id == _location
      );
      if (location_obj) {
        setLocation(location_obj);
      }
    }
    if (product_id) {
      getProductVariants();
    } else if (variant_id) {
      getRelatedVariants();
    }
  }, []);

  useEffect(() => {
    if (selected_variant_id) {
      getProduct();
    }
  }, [selected_variant_id, selected_location, selected_tab]);

  function getProduct() {
    const location_id =
      selected_location && selected_location.id
        ? String(selected_location.id)
        : locations && locations.length
        ? locations[0].id
        : null;
    const filter = tabOptions[selected_tab].key;
    const searchPayload = {
      location: location_id,
      filter,
    };
    setIsVariantLoading(true);
    fetchAPI(
      "GET",
      `${API_ENDPOINTS.PRODUCT}/${selected_variant_id}`,
      searchPayload
    )
      .then((response) => {
        setVariantDetails(response as Product);
        setIsVariantLoading(false);
      })
      .catch((error) => {
        showToast(toastOptions.PRODUCT_NOT_FOUND);
        setIsVariantLoading(false);
      });
  }

  function getRelatedVariants() {
    setIsLoading(true);
    fetchAPI("GET", `${API_ENDPOINTS.PRODUCT_RELATED_VARIANTS}/${variant_id}`)
      .then((response) => {
        const related_variants = response as Array<Product>;
        const selected_variant = related_variants.find(
          (variant) => variant.shopify_variant_id == variant_id
        );
        setProductVariants(related_variants);
        setSelectedVariantId(
          selected_variant ? selected_variant.id : related_variants[0].id
        );
        setIsLoading(false);
      })
      .catch((error) => {
        showToast(toastOptions.VARIANTS_GET_FAILED);
        setIsLoading(false);
      });
  }

  function getProductVariants() {
    setIsLoading(true);
    fetchAPI("GET", `${API_ENDPOINTS.PRODUCT}/${product_id}/variants`)
      .then((response) => {
        const related_variants = response as Array<Product>;
        const selected_variant = related_variants.find(
          (variant) => variant.shopify_variant_id == variant_id
        );
        setProductVariants(related_variants);
        setSelectedVariantId(
          selected_variant ? selected_variant.id : related_variants[0].id
        );
        setIsLoading(false);
      })
      .catch((error) => {
        showToast(toastOptions.VARIANTS_GET_FAILED);
        setIsLoading(false);
      });
  }

  function handleLocationChange(ids: Array<string | number>) {
    setLocation(locations.find((location: Location) => location.id == ids[0]));
  }

  function handleCreateUnaccountedQuantity() {
    setIsAutoCreating(true);
    const items = [
      {
        product_id: variant_details!.id,
        quantity:
          variant_details!.quantity_at_location -
          sumArray(variant_details!.batch_items, "quantity"),
        expire_at: null,
        dates: [],
      },
    ];
    const payload = {
      location_id: selected_location.id,
      items,
      is_sync_quantity: settings?.is_sync_batch_quantity == null ? true : settings.is_sync_batch_quantity,
      type: "auto",
      name: "Auto-created batch",
      received_at: null,
      bin_location: null,
      barcode: null,
      invoice_number: null,
      description: null,
    };
    fetchAPI("POST", API_ENDPOINTS.INVENTORY_BATCH, payload)
      .then((response: any) => {
        showToast(toastOptions.BATCH_CREATED);
        getProduct();
        handleBatchModal((response as InventoryBatch).id);
      })
      .catch((error) => {
        if (error && error.data) {
          if (error.data.required_plan_upgrade) {
            showToast(toastOptions.BATCH_PRODUCT_LIMIT);
            return;
          }
        }
        showToast(toastOptions.BATCH_NOT_CREATED);
      })
      .finally(() => {
        setIsAutoCreating(false);
      });
  }

  function handleBatchModal(batch_id: string | number | null) {
    openModal("EDIT_INVENTORY_BATCH_VIEW", { batch_id, product_id: selected_variant_id }, { modalTitle: `${batch_id ? "Edit" : "Add"} inventory batch`, onDismiss: handleBatchModalClosed, hideCancelBtn: true });
  }

  function handleBatchModalClosed(
    result:
      | { dashboard?: boolean; batch_id?: string | number }
      | boolean = false
  ) {
    if (typeof result == "boolean") {
      if (result === true) {
        getProduct();
      }
    }
    closeModal();
    if (typeof result != "boolean") {
      if (result.dashboard) {
        router.push(ROUTES.PRODUCTS);
      }
      if (result.batch_id) {
        handleBatchModal(result.batch_id!);
      }
    }
  }

  function handleViewProductStore(product: Product) {
    getProductStoreURL(product.id, {
      onSuccess: (response: ProductStoreURL) => {
        if (response.store_url || response.preview_url) {
          const redirect = Redirect.create(app);
          //@ts-ignore
          redirect.dispatch(Redirect.Action.REMOTE, {
            url: `${response.store_url ?? response.preview_url}${product_variants.length > 1 ? `?variant=${product.shopify_variant_id}` : ""}`,
            newContext: true
          });
        }
        else {
          showToast({ message: "Failed to get store URL", duration: 3000, isError: true });
        }
      },
      onError: () => {
        showToast({ message: "Failed to get store URL", duration: 3000, isError: true });
      }
    })
  }

  function handleExternalLink(path: string) {
    const redirect = Redirect.create(app);
    redirect.dispatch(Redirect.Action.ADMIN_PATH, {
      path,
      newContext: true,
    });
  }

  return (
    <div>
      {is_loading && product_variants.length <= 0 && !variant_details ? (
        <ProductDetailsSkeleton />
      ) : (
        <></>
      )}
      {!is_loading && product_variants.length >= 0 && variant_details ? (
        <Page
          breadcrumbs={[{ content: "Products", url: ROUTES.PRODUCTS }]}
          title={variant_details.name}
          primaryAction={
            <Button primary onClick={() => handleBatchModal(null)}>
              Add batch
            </Button>
          }
        >
          <div className="flex mb-5">
            <Link
              onClick={() =>
                handleExternalLink(
                  `/products/${variant_details.parent_id}/variants/${variant_details.shopify_variant_id}`
                )
              }
              removeUnderline
            >
              View variant in Shopify
            </Link>
          </div>
          <div className="mt-4">
            <Layout>
              <Layout.Section secondary>
                <ShopifyCard title="Product info" sectioned>
                  <Stack alignment="center">
                    <Thumbnail
                      alt={variant_details.name}
                      source={
                        variant_details.image_url
                          ? variant_details.image_url
                          : "/assets/no-image.jpg"
                      }
                    />
                    <div>
                      <ShopifyText variant="headingMd" as="h1">
                        {variant_details.parent_name}
                      </ShopifyText>
                      <p>{product_variants.length} variants</p>
                    </div>
                  </Stack>
                  <div className="mt-4">
                    <Link
                      onClick={() =>
                        handleExternalLink(
                          `/products/${variant_details.parent_id}`
                        )
                      }
                      removeUnderline
                    >
                      View product in Shopify
                    </Link>
                  </div>
                </ShopifyCard>
                <ShopifyCard title="Store Preview" sectioned>
                  {is_variant_loading || isLoadingStoreURL ? (
                    <SkeletonBodyText lines={1} />
                  ) : (
                    <Link
                      onClick={() =>
                        handleViewProductStore(variant_details)
                      }
                      removeUnderline
                    >
                      View product in store
                    </Link>
                  )}
                </ShopifyCard>
                <ShopifyCard>
                  <OptionList
                    title="Product Variant"
                    onChange={(ids) => setSelectedVariantId(ids[0])}
                    options={
                      product_variants && product_variants.length > 0
                        ? product_variants.map((variant) => {
                            return {
                              value: variant.id as string,
                              label: variant.name,
                            };
                          })
                        : []
                    }
                    selected={[
                      selected_variant_id
                        ? (selected_variant_id as string)
                        : "",
                    ]}
                  />
                </ShopifyCard>
                <ShopifyCard>
                  <OptionList
                    title="Inventory Locations"
                    onChange={handleLocationChange}
                    options={
                      locations && locations.length > 0 ? (
                        locations.map((location: Location) => {
                          return { value: location.id, label: location.name };
                        })
                      ) : (
                        <></>
                      )
                    }
                    selected={[selected_location.id]}
                  />
                </ShopifyCard>
              </Layout.Section>
              <Layout.Section>
                {is_variant_loading ? (
                  <div>
                    <ShopifyCard>
                      <ShopifyCard.Section>
                        <TextContainer>
                          <SkeletonDisplayText size="small" />
                          <SkeletonBodyText lines={4} />
                        </TextContainer>
                      </ShopifyCard.Section>
                      <ShopifyCard.Section>
                        <SkeletonBodyText lines={3} />
                      </ShopifyCard.Section>
                    </ShopifyCard>
                    <ShopifyCard>
                      <ShopifyCard.Section>
                        <TextContainer>
                          <SkeletonDisplayText size="small" />
                          <SkeletonBodyText lines={3} />
                        </TextContainer>
                      </ShopifyCard.Section>
                    </ShopifyCard>
                  </div>
                ) : (
                  <></>
                )}
                {!is_variant_loading && variant_details ? (
                  <div>
                    <ShopifyCard title="Inventory">
                      {!variant_details.is_inventory_managed ? (
                        <ShopifyCard.Section>
                          <Banner
                            title="Enable inventory tracking"
                            action={{
                              content: "Edit variant in Shopify",
                              onAction: () =>
                                handleExternalLink(
                                  `/products/${variant_details.parent_id}/variants/${variant_details.shopify_variant_id}`
                                ),
                            }}
                            status="warning"
                          >
                            <p>
                              Before you can add batches, enable inventory
                              tracking for this variant in Shopify. Do this by
                              selecting <strong>Track quantity</strong> in the{" "}
                              <strong>Inventory section</strong>.
                            </p>
                          </Banner>
                        </ShopifyCard.Section>
                      ) : (
                        <></>
                      )}
                      {variant_details.is_inventory_managed &&
                      !variant_details.is_item_stocked ? (
                        <ShopifyCard.Section>
                          <Banner
                            action={{
                              content: "Edit locations",
                              onAction: () =>
                                handleExternalLink(
                                  `/products/${variant_details.parent_id}/variants/${variant_details.shopify_variant_id}`
                                ),
                            }}
                            status="warning"
                          >
                            <p>
                              This variant is not stocked at this location. Edit
                              location to add a batch to the selected variant.
                            </p>
                          </Banner>
                        </ShopifyCard.Section>
                      ) : (
                        <></>
                      )}
                      {variant_details.is_inventory_managed &&
                      variant_details.is_item_stocked &&
                      tabOptions[selected_tab].key == BATCH_STATUS_ENUM.IN_STOCK &&
                      variant_details.quantity_at_location >
                        variant_details.stocked_quantity ? (
                        <ShopifyCard.Section>
                          <Banner
                            title={
                              "Add unaccounted variant quantity (" +
                              (variant_details.quantity_at_location -
                                variant_details.stocked_quantity) +
                              " items) to a batch"
                            }
                            action={{
                              content: "Create batch",
                              onAction: handleCreateUnaccountedQuantity,
                              loading: is_auto_creating || settingsLoading,
                              disabled: is_auto_creating || settingsLoading,
                            }}
                            status="warning"
                          >
                            <p>
                              This variant has{" "}
                              {variant_details.quantity_at_location -
                                variant_details.stocked_quantity}{" "}
                              items that do not belong to a batch. Create a
                              batch to set an expiry date for these items.
                            </p>
                          </Banner>
                        </ShopifyCard.Section>
                      ) : (
                        <></>
                      )}
                      <ShopifyCard.Section>
                        <FormLayout>
                          <FormLayout.Group>
                            <TextField
                              autoComplete="off"
                              type="text"
                              label="SKU (Stock Keeping Unit)"
                              value={variant_details.sku}
                              disabled
                            />
                            <TextField
                              autoComplete="off"
                              type="number"
                              label="Total variant quantity"
                              value={variant_details.quantity as string}
                              disabled
                            />
                            <TextField
                              autoComplete="off"
                              type="text"
                              label="Location"
                              value={selected_location.name}
                              disabled
                            />
                            <TextField
                              autoComplete="off"
                              type="number"
                              label="Variant quantity at location"
                              value={String(
                                variant_details.quantity_at_location
                              )}
                              disabled
                            />
                          </FormLayout.Group>
                        </FormLayout>
                        <div className="mt-4">
                          <Link
                            url={`${ROUTES.INVENTORY_HISTORY}/${variant_details.id}`}
                            removeUnderline
                          >
                            View product inventory batch history
                          </Link>
                        </div>
                      </ShopifyCard.Section>
                    </ShopifyCard>
                    <ShopifyCard>
                      <div>
                        <div className="border-b">
                          <TabOptions
                            options={tabOptions}
                            selected={selected_tab}
                            onChange={setSelectedTab}
                          />
                        </div>
                        <DataTable
                          hideScrollIndicator
                          columnContentTypes={[
                            "text",
                            "numeric",
                            "numeric",
                            "numeric",
                            "numeric",
                          ]}
                          headings={[
                            "Batch name",
                            "Expiry date",
                            "Best Before date",
                            "Created",
                            "Quantity",
                          ]}
                          rows={variant_details.batch_items.map((row) => {
                            return [
                              <Link
                                onClick={() => handleBatchModal(row.batch.id)}
                                removeUnderline
                              >
                                {row.batch.name ?? `Batch #${row.batch.id}`}
                              </Link>,
                              row.expire_at ? formatDate(row.expire_at) : "—",
                              row.dates && row.dates.length
                                ? row.dates.map(({ date }, index) => (
                                  <p key={index}>{formatDate(date)}</p>
                                ))
                                : "—",
                              formatDate(row.batch.created_at),
                              row.quantity,
                            ];
                          })}
                          totals={[
                            "—",
                            "—",
                            "—",
                            "—",
                            sumArray(variant_details.batch_items, "quantity"),
                          ]}
                          totalsName={{
                            plural: "Summary",
                            singular: "Summary",
                          }}
                          footerContent={`Showing ${variant_details.batch_items.length} of ${variant_details.batch_items.length} results`}
                        />
                      </div>
                    </ShopifyCard>
                  </div>
                ) : (
                  <></>
                )}
              </Layout.Section>
            </Layout>
          </div>
        </Page>
      ) : (
        <></>
      )}
    </div>
  );
}
