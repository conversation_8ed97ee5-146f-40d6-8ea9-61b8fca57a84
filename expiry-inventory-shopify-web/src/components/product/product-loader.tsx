import { ModalContentLoader } from "@components/ui/modal/modal";
import { useUI } from "@contexts/ui.context";
import { useProductQuery } from "@data/product/use-product.query";

interface ProductLoaderProps {
  id: string;
}

const ProductLoader: React.FC<ProductLoaderProps> = ({ id, children }) => {
  const { location } = useUI();
  const { data, isLoading } = useProductQuery(id, {
    query: {
      location: String(location.id),
    }
  });
  if (isLoading || !data) {
    return (
      <ModalContentLoader />
    );
  }
  // @ts-ignore
  return children({ product: data });
}

export default ProductLoader;