import PageLoading from "@components/common/page-loading";
import { useUI } from "@contexts/ui.context";
import { useProductQuery } from "@data/product/use-product.query";

interface ProductVariantsLoaderProps {
  product_id: string | null;
  variant_id: string | null;
}

// TODO: get related variants loader (if !product_id)
// const ProductGetRelatedVariants: React.FC<ProductVariantsLoaderProps> = ({ variant_id, children }) => {

// }

// TODO: get product variants loader (by product_id, assigned first variant as selected variant if !variant_id)
// const ProductGetRelatedVariants: React.FC<ProductVariantsLoaderProps> = ({ variant_id, children }) => {

// }

const ProductVariantsLoader: React.FC<ProductVariantsLoaderProps> = ({ product_id, variant_id, children }) => {
  const { location } = useUI();
  const { data, isLoading } = useProductQuery(product_id!, {
    query: {
      location: String(location.id),
    }
  });
  if (isLoading || !data) {
    return (
      <PageLoading />
    );
  }
  // @ts-ignore
  return children({ product: data });
}

export default ProductVariantsLoader;