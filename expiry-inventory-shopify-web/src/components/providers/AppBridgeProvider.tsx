import { Provider } from "@shopify/app-bridge-react";
import { createAppObject } from "@utils/helpers";
import { useEffect } from "react";
import { onCLS, onFID, onLCP } from "web-vitals";

function InitApp({ children }: { children: any }) {
  const appObject = createAppObject();
  useEffect(() => {
    onCLS(console.log);
    onFID(console.log);
    onLCP(console.log);
  }, []);
  return children;
}

export function AppBridgeProvider({ host, children }: { host: string, children: any }) {
  return (
    <Provider
      config={{
        host: host,
        apiKey: process.env.NEXT_PUBLIC_SHOPIFY_KEY!,
        forceRedirect: true,
      }}
    >
      <InitApp>
        {children}
      </InitApp>
    </Provider>
  );
}
