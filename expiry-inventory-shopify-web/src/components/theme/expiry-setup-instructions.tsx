import { ShopifyText } from "@components/ui/shopify/text";
import V2SupportedThemeSetupInstructions from "./v2-supported-theme-setup-instructions";
import VintageThemeSetupInstructions from "./vintage-theme-setup-instructions";
import { Badge } from "@shopify/polaris";
import { Theme } from "@ts-types/types";
import { useThemeQuery } from "@data/theme/use-theme.query";
import Loader from "@components/common/loader";

interface ExpirySetupInstructionsProps {
    theme: Theme;
}

const ExpirySetupInstructions: React.FC<ExpirySetupInstructionsProps> = ({
    theme
}) => {
    const { data, isLoading } = useThemeQuery(theme.id);
    if(isLoading || !data) return <Loader />;
    return (
        <>
            <ShopifyText as="h4" variant="headingMd">
                Instructions
                <span className="ml-2">
                    <Badge status={data.is_supported ? "info" : "new"}>
                        {data.is_supported ? "Online Store 2.0" : "Legacy"}
                    </Badge>
                </span>
            </ShopifyText>
            <div className="mt-4">
                {data.is_supported ? <V2SupportedThemeSetupInstructions /> : <VintageThemeSetupInstructions />}
            </div>
        </>
    );
}

export default ExpirySetupInstructions;