import { useState } from "react";
import SelectTheme from "./select-theme";
import { Modal } from "@shopify/polaris";
import { Theme } from "@ts-types/types";
import ExpirySetupInstructions from "./expiry-setup-instructions";

const ExpirySetupView = () => {
    const [theme, setTheme] = useState<Theme>();
    return (
        <>
            <Modal.Section>
                <SelectTheme selected_theme={theme ? theme.id.toString() : undefined} onThemeSelected={setTheme} />
            </Modal.Section>
            {(theme && theme.id) ? (
                <Modal.Section>
                    <ExpirySetupInstructions theme={theme} />
                </Modal.Section>
            ) : <></>}
        </>
    );
}

export default ExpirySetupView;