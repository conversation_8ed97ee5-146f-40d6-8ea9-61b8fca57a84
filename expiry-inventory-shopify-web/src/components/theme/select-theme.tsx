import Loader from "@components/common/loader";
import { ShopifyText } from "@components/ui/shopify/text";
import { useThemesQuery } from "@data/theme/use-themes.query";
import { Select } from "@shopify/polaris";

interface SelectThemeProps {
    selected_theme?: string;
    onThemeSelected: any;
};

const SelectTheme: React.FC<SelectThemeProps> = ({
    selected_theme,
    onThemeSelected
}) => {
    const { data, isLoading } = useThemesQuery();
    if(isLoading || !data) return <Loader />;
    function handleThemeSelected(selected_id: string) {
        onThemeSelected(data?.find(({ id }) => id == selected_id));
    }
    return (
        <>
            <ShopifyText as="h4" variant="headingMd">Select a theme to setup</ShopifyText>
            <div className="mt-4">
                <Select
                    label="Theme"
                    options={[{ label: "Select", value: "" }, ...data.map(({id, name, is_published}) => ({ label: `${name}${is_published ? " (Live)" : ""}`, value: id.toString() }))]}
                    value={selected_theme ?? ""}
                    onChange={handleThemeSelected}
                ></Select>
            </div>
        </>
    );
}

export default SelectTheme;