import { Modal } from "@shopify/polaris";
import ExpirySetupView from "./expiry-setup-view";

interface ThemeExpirySetupInstructionsModalProps {
    active: boolean;
    onConfirm: any;
    onDismiss: any;
}

const ThemeExpirySetupInstructionsModal = ({ active, onConfirm, onDismiss }: ThemeExpirySetupInstructionsModalProps) => {
    return (
        <Modal
            open={active}
            onClose={() => onDismiss(false)}
            title="Installation Guide"
            primaryAction={{
                content: "Done",
                onAction: onConfirm
            }}
        >
            <ExpirySetupView />
        </Modal>
    );
}

export default ThemeExpirySetupInstructionsModal;