import { ShopifyText } from "@components/ui/shopify/text";
import siteSettings from "@settings/site.settings";
import { List } from "@shopify/polaris";

const V2SupportedThemeSetupInstructions = () => {
    return (
        <>
            <List type="number">
                <List.Item>Open the theme you’d like to customize.</List.Item>
                <List.Item>Open a product template you’d like to add the expiry date to.</List.Item>
                <List.Item>Click <b>Add block</b> for the section.</List.Item>
                <List.Item>Select <b>Expiry Date</b>, which is listed under <b>Apps</b>.</List.Item>
                <List.Item>In the <b>Expiry Date</b> block, you can then adjust the expiry label text, date format and text color.</List.Item>
                <List.Item>To see the expiry date for the product in storefront, ensure that there is inventory batch associated with the product with expiry date.</List.Item>
            </List>
            <div className="mt-4">
                <ShopifyText as="p" variant="bodyMd">Watch this video tutorial for detailed steps on how to add <b>Expiry Date</b> block to your Online Store 2.0 theme in a few simple steps.</ShopifyText>
                <div className="mt-2">
                    <a href={siteSettings.theme.v2_instruction_video_url} target="_blank">Watch Video on Youtube</a>
                </div>
            </div>
        </>
    );
}

export default V2SupportedThemeSetupInstructions;