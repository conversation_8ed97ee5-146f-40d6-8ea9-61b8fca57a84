import Loader from "@components/common/loader";
import { ShopifyText } from "@components/ui/shopify/text";
import { useExpirySettingsQuery } from "@data/settings/use-expiry-settings.query";
import siteSettings from "@settings/site.settings";
import { List } from "@shopify/polaris";
import moment from "moment";
import dynamic from "next/dynamic";

const CodeEditor = dynamic(
    () => import("@uiw/react-textarea-code-editor").then((mod) => mod.default),
    { ssr: false }
  );

const VintageThemeSetupInstructions = () => {
    const { data, isLoading } = useExpirySettingsQuery();
    if(isLoading || !data) return <Loader />;
    return (
        <>
            {data
                ? (
                    <CodeEditor
                        value={typeof data == "string" ? data : ""}
                        language="html"
                        padding={15}
                        style={{
                            fontSize: 12,
                            backgroundColor: "#f5f5f5",
                            fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace',
                            marginBottom: 12
                        }}
                        disabled
                    />
                ) : <></>
            }
            <List type="number">
                <List.Item>Open the theme you’d like to customize.</List.Item>
                <List.Item>Place the code above in product.liquid section file.</List.Item>
                <List.Item>To see the expiry date for the product in storefront, ensure that there is inventory batch associated with the product with expiry date.</List.Item>
                <List.Item>Optionally, you can change how the expiry date to display, such as text (e.g. Expiring soon) and date format (e.g. %Y-%m-%d will format to {moment().add(1, "y").format("YYYY-MM-DD")}) using Liquid code.</List.Item>
                <List spacing="extraTight" type="bullet">
                    <List.Item>Refer this <a href="https://strftime.net/" target="_blank">link</a> to build your desired date format.</List.Item>
                </List>
            </List>
            <div className="mt-4">
                <ShopifyText as="p" variant="bodyMd">Watch this video tutorial for detailed steps on how to setup expiry date code snippet to your legacy theme's (incompatible with Online Store 2.0) product templates in a few simple steps.</ShopifyText>
                <div className="mt-2">
                    <a href={siteSettings.theme.legacy_instruction_video_url} target="_blank">Watch Video on Youtube</a>
                </div>
            </div>
        </>
    );
}

export default VintageThemeSetupInstructions;