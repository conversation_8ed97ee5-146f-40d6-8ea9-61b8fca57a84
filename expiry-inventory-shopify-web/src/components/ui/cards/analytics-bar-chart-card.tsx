import { RefObject, useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YA<PERSON><PERSON>,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { ShopifyCard } from "@components/ui/shopify/card";
import { ShopifyText } from "@components/ui/shopify/text";
import { ShopifySkeletonBodyText } from "@components/ui/shopify/skeleton-body-text";

export default function AnalyticsBarChartCard({
  summaryTitle = "",
  summaryText = "",
  chartTitle = "",
  xKey = "Date",
  yKey = "Quantity",
  color = "#008060",
  data,
  loading,
}: {
  summaryTitle?: string;
  summaryText?: string | number;
  chartTitle?: string;
  xKey?: string;
  yKey?: string;
  color?: string;
  data?: any[];
  loading: boolean;
}) {
  const useResize = (myRef: RefObject<HTMLDivElement>) => {
    const [width, setWidth] = useState(700);
    const [height, setHeight] = useState(300);

    useEffect(() => {
      const handleResize = () => {
        if (myRef.current) {
          setWidth(myRef.current.offsetWidth);
          setHeight(myRef.current.offsetHeight);
        }
      };

      handleResize();

      window.addEventListener("resize", handleResize);

      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }, []);

    return { width, height };
  };
  const componentRef = useRef<HTMLDivElement>(null);
  const { width, height } = useResize(componentRef);

  return (
    <ShopifyCard>
      <ShopifyCard.Section>
        <ShopifyText variant="headingXl" as="h4">
          {summaryTitle}
        </ShopifyText>
        {loading || !data ? (
          <ShopifySkeletonBodyText lines={1}></ShopifySkeletonBodyText>
        ) : summaryText || summaryText === 0 ? (
          <ShopifyText variant="heading2xl" as="h1">
            {summaryText}
          </ShopifyText>
        ) : (
          <></>
        )}
      </ShopifyCard.Section>
      <ShopifyCard.Section title={chartTitle}>
        {loading || !data ? (
          <ShopifySkeletonBodyText lines={8}></ShopifySkeletonBodyText>
        ) : (
          <ResponsiveContainer ref={componentRef} width="100%" height={400}>
            <BarChart
              width={width}
              height={height}
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <XAxis dataKey={xKey} />
              <YAxis dataKey={yKey} />
              <Tooltip />
              <Bar dataKey={yKey} fill={color} />
            </BarChart>
          </ResponsiveContainer>
        )}
      </ShopifyCard.Section>
    </ShopifyCard>
  );
}
