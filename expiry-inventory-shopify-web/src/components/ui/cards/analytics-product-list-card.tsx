import { But<PERSON>, <PERSON>, Pagination, SkeletonBodyText } from "@shopify/polaris";
import { useState } from "react";
import moment from "moment";
import { dateDiff } from "@utils/helpers";
import { useI18n } from "@shopify/react-i18n";
import { ShopifyCard } from "@components/ui/shopify/card";
import { ShopifyText } from "@components/ui/shopify/text";
import { ROUTES } from "@utils/routes";
import { useProductAnalyticsQuery } from "@data/dashboard/use-analytics.query";

function AnalyticsProductListCard({
  status,
  days,
  currency,
  onExport,
}: {
  status: {
    name: string;
    value: string;
  };
  days: number;
  currency: string;
  onExport?: any;
}) {
  const [page, setPage] = useState(1);
  const [i18n] = useI18n();
  const { data: productAnalyticsData, isLoading } = useProductAnalyticsQuery({
    type: status.value,
    days,
    page,
  });

  const { data: products, last_page } = productAnalyticsData ?? {};

  function formatDiscountType(type: string) {
    switch (type) {
      case "expiry":
        return "Expires";

      case "slow_moving":
        return "Slow Moving";

      case "best_before":
        return "Best Before discount";

      default:
        return "Discount";
    }
  }

  function formatRemainingDays(
    to_date: Date,
    type = "discount",
    formatMonth = true
  ) {
    const today = moment();
    const diff_days = dateDiff(today, to_date);
    const day_text =
      diff_days > 0
        ? `in ${diff_days} day${diff_days > 1 ? "s" : ""}`
        : "today";
    if (formatMonth) {
      if (diff_days <= 31) {
        return `${type === "expiry" ? "" : "Discount ends"} ${day_text}`;
      }
      const diff_months = dateDiff(today, to_date, "months");
      return `${
        type === "expiry" ? "" : "Discount ends"
      } in ${diff_months} month${diff_months > 1 ? "s" : ""}`;
    }
    return `${type === "expiry" ? "" : "Discount ends"} ${day_text}`;
  }

  return (
    <ShopifyCard>
      <ShopifyCard.Section>
        <ShopifyText variant="headingMd" as="h6">
          <span className="capitalize">{status.name}</span> products{" "}
          {status.value != "discounted" ? "by quantity and retail cost" : ""}
        </ShopifyText>
        <p>
          A breakdown of {status.name} products{" "}
          {status.value == "discounted"
            ? "in inventory."
            : "with the greatest number of remaining inventory."}
        </p>
        {onExport ? (
          <div className="mt-2">
            <Button primary onClick={onExport}>
              Export
            </Button>
          </div>
        ) : <></>}
      </ShopifyCard.Section>
      <ShopifyCard.Section title="Products">
        <div>
          <div className="py-4">
            {isLoading || !productAnalyticsData ? (
              <SkeletonBodyText lines={5}></SkeletonBodyText>
            ) : (
              <></>
            )}
            {!isLoading && productAnalyticsData && products!.length <= 0 ? (
              <p>No products are available.</p>
            ) : (
              <></>
            )}
            {!isLoading && productAnalyticsData && products!.length > 0 ? (
              products!.map((product) => {
                return (
                  <div className="flex pb-4 mb-4 border-b" key={product.id}>
                    <div>
                      <Link
                        url={`${ROUTES.PRODUCTS}/${product.parent_id}/${product.shopify_variant_id}`}
                      >
                        {product.name}
                      </Link>
                      {product.nearest_expire_date ? (
                        <div>
                          Nearest expiry date:&nbsp;
                          {moment(product.nearest_expire_date).format("DD MMM YYYY")}
                        </div>
                      ) : (
                        <></>
                      )}
                      {(product.best_before_dates && product.best_before_dates.length) ? (
                        <div>
                          Best before:&nbsp;
                          {product.best_before_dates.map((date) => moment(date).format("DD MMM YYYY")).join(", ")}
                        </div>
                      ) : (
                        <></>
                      )}
                      {status.value == "discounted" && product.discount ? (
                        <div className="mt-2">
                          {formatDiscountType(product.discount.type)}
                          {product.discount.type == "expiry" &&
                          product.nearest_expire_date ? (
                            <span>
                              {formatRemainingDays(
                                product.nearest_expire_date,
                                product.discount.type,
                                false
                              )}
                            </span>
                          ) : (
                            <></>
                          )}
                          {product.discount.type == "slow_moving" &&
                          product.discount.track_period &&
                          product.discount.track_unit ? (
                            <span className="ml-2">
                              ({product.discount.track_period}{" "}
                              {product.discount.track_unit} tracking)
                            </span>
                          ) : (
                            <></>
                          )}
                        </div>
                      ) : (
                        <></>
                      )}
                    </div>
                    <div className="ml-auto">
                      {status.value == "discounted" ? (
                        <div className="flex text-right">
                          <div>
                            <div>
                              <ShopifyText
                                variant="bodyMd"
                                color="subdued"
                                as="p"
                              >
                                <del>
                                  {i18n.formatCurrency(product.original_price, {
                                    currency,
                                    form: "short",
                                  })}
                                </del>
                              </ShopifyText>
                              <span className="ml-2">
                                {i18n.formatCurrency(product.price, {
                                  currency,
                                  form: "short",
                                })}
                              </span>
                            </div>
                            {product.discount &&
                            product.discount.type == "slow_moving" &&
                            product.discount.discount_end_at ? (
                              <div className="mt-2">
                                <ShopifyText
                                  fontWeight="semibold"
                                  variant="bodyMd"
                                  color="subdued"
                                  as="p"
                                >
                                  {Number(product.discount.discount_rate)}%{" "}
                                  {formatRemainingDays(
                                    product.discount.discount_end_at
                                  )}
                                </ShopifyText>
                              </div>
                            ) : (
                              <></>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="text-right">
                          <div>
                            <ShopifyText variant="bodyMd" as="p">
                              {i18n.formatCurrency(product.price, {
                                currency,
                                form: "short",
                              })}
                            </ShopifyText>
                          </div>
                          <div>
                            <ShopifyText
                              variant="bodyMd"
                              color="subdued"
                              as="p"
                            >
                              Quantity: {product.quantity}
                            </ShopifyText>
                          </div>
                          <div>
                            <ShopifyText variant="bodyMd" as="p">
                              Total:{" "}
                              {i18n.formatCurrency(
                                product.price * product.quantity,
                                { currency, form: "short" }
                              )}
                            </ShopifyText>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <></>
            )}
          </div>
          <div id="products-quantity-paginator">
            <Pagination
              hasPrevious={1 < page}
              onPrevious={() => {
                setPage(page - 1);
              }}
              hasNext={page < last_page!}
              onNext={() => {
                setPage(page + 1);
              }}
            />
          </div>
        </div>
      </ShopifyCard.Section>
    </ShopifyCard>
  );
}

export default AnalyticsProductListCard;
