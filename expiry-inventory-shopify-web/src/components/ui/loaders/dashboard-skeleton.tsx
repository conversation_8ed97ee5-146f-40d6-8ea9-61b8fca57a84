import { Layout, SkeletonPage, TextContainer, SkeletonDisplayText, SkeletonBodyText } from "@shopify/polaris";
import { ShopifyCard } from "@components/ui/shopify/card";
import { ShopifySkeletonBodyText } from "@components/ui/shopify/skeleton-body-text";

const DashboardSkeleton = () => {
  return (
    <SkeletonPage>
      <Layout sectioned={false}>
        <Layout.Section>
          <ShopifySkeletonBodyText lines={2}></ShopifySkeletonBodyText>
        </Layout.Section>
        <Layout.Section oneHalf>
          <ShopifySkeletonBodyText lines={1}></ShopifySkeletonBodyText>
        </Layout.Section>
        <Layout.Section oneHalf>
          <ShopifySkeletonBodyText lines={1}></ShopifySkeletonBodyText>
        </Layout.Section>
        <Layout.Section>
          <ShopifyCard>
            <ShopifyCard.Section>
              <TextContainer>
                <SkeletonDisplayText size="small" />
                <SkeletonBodyText lines={2} />
                <SkeletonBodyText lines={4} />
              </TextContainer>
            </ShopifyCard.Section>
          </ShopifyCard>
          <ShopifyCard>
            <ShopifyCard.Section>
              <TextContainer>
                <SkeletonDisplayText size="small" />
                <SkeletonBodyText lines={2} />
                <SkeletonBodyText lines={4} />
              </TextContainer>
            </ShopifyCard.Section>
          </ShopifyCard>
        </Layout.Section>
      </Layout>
    </SkeletonPage>
  );
}

export default DashboardSkeleton;