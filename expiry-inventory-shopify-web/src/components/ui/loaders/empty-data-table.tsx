import { SkeletonBodyText, SkeletonPage } from "@shopify/polaris";
import { ShopifyCard } from "../shopify/card";

function EmptyDataTable({ showPage = false }) {
    function TableRowsSkeleton() {
        return (
            <>
                <ShopifyCard.Section>
                    <SkeletonBodyText lines={2} />
                </ShopifyCard.Section>
                <ShopifyCard.Section>
                    <SkeletonBodyText lines={2} />
                </ShopifyCard.Section>
                <ShopifyCard.Section>
                    <SkeletonBodyText lines={2} />
                </ShopifyCard.Section>
                <ShopifyCard.Section>
                    <SkeletonBodyText lines={2} />
                </ShopifyCard.Section>
                <ShopifyCard.Section>
                    <SkeletonBodyText lines={2} />
                </ShopifyCard.Section>
                <ShopifyCard.Section>
                    <SkeletonBodyText lines={2} />
                </ShopifyCard.Section>
            </>
        );
    }
    return (
        <>
            {showPage ? (
                <SkeletonPage>
                    <ShopifyCard>
                        <TableRowsSkeleton />
                    </ShopifyCard>
                </SkeletonPage>
            ) : (
                <div>
                    <TableRowsSkeleton />
                </div>
            )}
        </>
    );
}

export default EmptyDataTable;