import { Layout, SkeletonPage, TextContainer, SkeletonDisplayText, SkeletonBodyText } from "@shopify/polaris";
import { ShopifyCard } from "@components/ui/shopify/card";

const OrderDetailsSkeleton = () => {
  return (
    <SkeletonPage primaryAction>
      <Layout>
        <Layout.Section>
          <SkeletonBodyText lines={3}></SkeletonBodyText>
        </Layout.Section>
        <Layout.Section>
          <ShopifyCard sectioned>
            <TextContainer>
              <SkeletonDisplayText size="small" />
              <SkeletonBodyText lines={2} />
            </TextContainer>
          </ShopifyCard>
        </Layout.Section>
        <Layout.Section secondary>
          <ShopifyCard>
            <ShopifyCard.Section>
              <TextContainer>
                <SkeletonDisplayText size="small" />
                <SkeletonBodyText lines={1} />
              </TextContainer>
            </ShopifyCard.Section>
            <ShopifyCard.Section>
              <SkeletonBodyText lines={1} />
            </ShopifyCard.Section>
          </ShopifyCard>
          <ShopifyCard>
            <ShopifyCard.Section>
              <TextContainer>
                <SkeletonDisplayText size="small" />
                <SkeletonBodyText lines={2} />
              </TextContainer>
            </ShopifyCard.Section>
            <ShopifyCard.Section>
              <SkeletonBodyText lines={2} />
            </ShopifyCard.Section>
          </ShopifyCard>
        </Layout.Section>
      </Layout>
    </SkeletonPage>
  );
}

export default OrderDetailsSkeleton;