import { Layout, SkeletonBodyText, SkeletonDisplayText, SkeletonPage, TextContainer } from "@shopify/polaris";
import { ShopifyCard } from "../shopify/card";
import { ShopifyText } from "../shopify/text";

const PlansSkeleton = () => {
    return (
        <SkeletonPage>
            <Layout>
                <Layout.Section>
                    <div className="mb-4">
                        <ShopifyText as="h1" variant="heading3xl" alignment="center">
                            Pick the plan that’s right for you.
                        </ShopifyText>
                    </div>
                </Layout.Section>
                <Layout.Section oneHalf>
                    <ShopifyCard sectioned>
                        <TextContainer>
                            <SkeletonDisplayText size="extraLarge" />
                            <div className="py-4">
                                <SkeletonBodyText lines={2} />
                            </div>
                            <SkeletonDisplayText size="medium" />
                            <div className="py-4">
                                <SkeletonBodyText lines={4} />
                            </div>
                            <SkeletonDisplayText size="large" />
                        </TextContainer>
                    </ShopifyCard>
                </Layout.Section>
                <Layout.Section oneHalf>
                    <ShopifyCard sectioned>
                        <TextContainer>
                            <SkeletonDisplayText size="extraLarge" />
                            <div className="py-4">
                                <SkeletonBodyText lines={2} />
                            </div>
                            <SkeletonDisplayText size="medium" />
                            <div className="py-4">
                                <SkeletonBodyText lines={4} />
                            </div>
                            <SkeletonDisplayText size="large" />
                        </TextContainer>
                    </ShopifyCard>
                </Layout.Section>
            </Layout>
        </SkeletonPage>
    );
}

export default PlansSkeleton;