import { Layout, SkeletonBodyText, SkeletonPage } from "@shopify/polaris";
import { ShopifyCard } from "../shopify/card";

const PreferencesSkeleton = () => {
    return (
        <SkeletonPage title="Settings" primaryAction>
            <Layout>
                <Layout.Section>
                    <ShopifyCard sectioned title="Batch expiry">
                        <SkeletonBodyText />
                    </ShopifyCard>
                    <ShopifyCard sectioned title="Batch discounts">
                        <SkeletonBodyText />
                    </ShopifyCard>
                    <ShopifyCard title="Staff notifications">
                        <ShopifyCard.Section>
                            <SkeletonBodyText lines={2} />
                        </ShopifyCard.Section>
                        <ShopifyCard.Section>
                            <SkeletonBodyText lines={1} />
                        </ShopifyCard.Section>
                    </ShopifyCard>
                    <ShopifyCard sectioned title="Inventory">
                        <SkeletonBodyText />
                    </ShopifyCard>
                    <ShopifyCard sectioned title="Expiry date on storefront">
                        <SkeletonBodyText />
                    </ShopifyCard>
                    <ShopifyCard sectioned title="Packing slip templates">
                        <SkeletonBodyText />
                    </ShopifyCard>
                    <ShopifyCard title="Subscription">
                        <ShopifyCard.Section>
                            <SkeletonBodyText lines={2} />
                        </ShopifyCard.Section>
                        <ShopifyCard.Section>
                            <SkeletonBodyText lines={2} />
                        </ShopifyCard.Section>
                        <ShopifyCard.Section>
                            <SkeletonBodyText lines={1} />
                        </ShopifyCard.Section>
                    </ShopifyCard>
                </Layout.Section>
            </Layout>
        </SkeletonPage>
    );
}

export default PreferencesSkeleton;