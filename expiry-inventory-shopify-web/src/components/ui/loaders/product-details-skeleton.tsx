import { Layout, SkeletonBodyText, SkeletonDisplayText, SkeletonPage, TextContainer } from "@shopify/polaris";
import { ShopifyCard } from "../shopify/card";

const ProductDetailsSkeleton = () => {
  return (
    //@ts-ignore
    <SkeletonPage primaryAction secondaryActions={1}>
      <Layout>
        <Layout.Section secondary>
          <ShopifyCard sectioned>
            <TextContainer>
              <SkeletonDisplayText size="small" />
              <SkeletonBodyText lines={2} />
            </TextContainer>
          </ShopifyCard>
          <ShopifyCard sectioned>
            <TextContainer>
              <SkeletonDisplayText size="small" />
              <SkeletonBodyText lines={3} />
            </TextContainer>
          </ShopifyCard>
          <ShopifyCard sectioned>
            <TextContainer>
              <SkeletonDisplayText size="small" />
              <SkeletonBodyText lines={4} />
            </TextContainer>
          </ShopifyCard>
        </Layout.Section>
        <Layout.Section>
          <ShopifyCard>
            <ShopifyCard.Section>
              <TextContainer>
                <SkeletonDisplayText size="small" />
                <SkeletonBodyText lines={4} />
              </TextContainer>
            </ShopifyCard.Section>
            <ShopifyCard.Section>
              <SkeletonBodyText lines={3} />
            </ShopifyCard.Section>
          </ShopifyCard>
        </Layout.Section>
      </Layout>
    </SkeletonPage>
  );
}

export default ProductDetailsSkeleton;