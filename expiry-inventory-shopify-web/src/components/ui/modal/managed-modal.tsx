import Modal, { ModalContentLoader } from "@components/ui/modal/modal";
import dynamic from "next/dynamic";
import {
  useModalAction,
  useModalState,
} from "@components/ui/modal/modal.context";
const DiscountApprovalView = dynamic(
  () => import("@components/discount/discount-approval-modal-view"),
  { loading: ModalContentLoader }
);
const DiscountPendingView = dynamic(
  () => import("@components/discount/discount-pending-modal-view"),
  { loading: ModalContentLoader }
);
const EditInventoryBatchModalView = dynamic(
  () => import("@components/inventory-batch/edit-inventory-batch-modal-view"),
  { loading: ModalContentLoader }
);
const ClearBatchesView = dynamic(
  () => import("@components/inventory-batch/clear-batch-modal-view"),
  { loading: ModalContentLoader }
);
const ForgotPasswordView = dynamic(
  () => import("@components/user/forgot-password-view"),
  { loading: ModalContentLoader }
);
const MetaPresetView = dynamic(
  () => import("@components/meta/meta-preset-modal-view"),
  { loading: ModalContentLoader }
);
const QuantityTransferView = dynamic(
  () => import("@components/inventory-batch/quantity-transfer-view"),
  { loading: ModalContentLoader }
);
const BatchVerificationView = dynamic(
  () => import("@components/inventory-batch/batch-verification-modal-view"),
  { loading: ModalContentLoader }
);
const ManagedModal: React.FC = () => {
  const { isOpen, view, options } = useModalState();
  const { closeModal } = useModalAction();

  return (
    <Modal
      open={isOpen}
      title={options?.modalTitle}
      confirmBtnText={options?.confirmBtnText}
      onConfirm={options?.onConfirm}
      hideCancelBtn={options?.hideCancelBtn}
      cancelBtnText={options?.cancelBtnText}
      onClose={options?.onDismiss ?? closeModal}
    >
      {view === "DISCOUNT_APPROVAL_VIEW" && <DiscountApprovalView />}
      {view === "DISCOUNT_PENDING_VIEW" && <DiscountPendingView />}
      {view === "EDIT_INVENTORY_BATCH_VIEW" && <EditInventoryBatchModalView />}
      {view === "CLEAR_BATCHES_VIEW" && <ClearBatchesView />}
      {view === "FORGOT_PASSWORD_VIEW" && <ForgotPasswordView />}
      {view === "META_PRESET_VIEW" && <MetaPresetView />}
      {view === "QUANTITY_TRANSFER_VIEW" && <QuantityTransferView />}
      {view === "BATCH_VERIFICATION_VIEW" && <BatchVerificationView />}
    </Modal>
  );
};

export default ManagedModal;
