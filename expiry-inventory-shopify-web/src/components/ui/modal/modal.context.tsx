import React from "react";

type MODAL_VIEWS =
  | "DISCOUNT_APPROVAL_VIEW"
  | "DISCOUNT_PENDING_VIEW"
  | "EDIT_INVENTORY_BATCH_VIEW"
  | "CLEAR_BATCHES_VIEW"
  | "FORGOT_PASSWORD_VIEW"
  | "META_PRESET_VIEW"
  | "QUANTITY_TRANSFER_VIEW"
  | "BATCH_VERIFICATION_VIEW";

type MODAL_OPTIONS = {
  modalTitle?: string;
  confirmBtnText?: string;
  onConfirm?: any;
  onDismiss?: any;
  cancelBtnText?: string;
  hideCancelBtn?: boolean;
};

interface State {
  view?: MODAL_VIEWS;
  data?: any;
  options?: MODAL_OPTIONS;
  isOpen: boolean;
}
type Action =
  | {
      type: "open";
      view?: MODAL_VIEWS;
      payload?: any;
      options?: MODAL_OPTIONS;
    }
  | { type: "close" };

const initialState: State = {
  view: undefined,
  isOpen: false,
  data: null,
  options: undefined,
};

function modalReducer(state: State, action: Action): State {
  switch (action.type) {
    case "open":
      return {
        ...state,
        view: action.view,
        data: action.payload,
        options: action.options,
        isOpen: true,
      };
    case "close":
      return {
        ...state,
        view: undefined,
        data: null,
        options: undefined,
        isOpen: false,
      };
    default:
      throw new Error("Unknown Modal Action!");
  }
}

const ModalStateContext = React.createContext<State>(initialState);
ModalStateContext.displayName = "ModalStateContext";
const ModalActionContext = React.createContext<
  React.Dispatch<Action> | undefined
>(undefined);
ModalActionContext.displayName = "ModalActionContext";

export const ModalProvider: React.FC = ({ children }) => {
  const [state, dispatch] = React.useReducer(modalReducer, initialState);
  return (
    <ModalStateContext.Provider value={state}>
      <ModalActionContext.Provider value={dispatch}>
        {children}
      </ModalActionContext.Provider>
    </ModalStateContext.Provider>
  );
};

export function useModalState() {
  const context = React.useContext(ModalStateContext);
  if (context === undefined) {
    throw new Error(`useModalState must be used within a ModalProvider`);
  }
  return context;
}

export function useModalAction() {
  const dispatch = React.useContext(ModalActionContext);
  if (dispatch === undefined) {
    throw new Error(`useModalAction must be used within a ModalProvider`);
  }
  return {
    openModal(view?: MODAL_VIEWS, payload?: unknown, options?: MODAL_OPTIONS) {
      dispatch({ type: "open", view, payload, options });
    },
    closeModal() {
      dispatch({ type: "close" });
    },
  };
}
