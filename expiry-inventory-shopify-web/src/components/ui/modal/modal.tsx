import React, { FC } from 'react';
import { Modal as PolarisModal } from "@shopify/polaris";
import Loader from '@components/common/loader';

export const ModalContentLoader = () => (
  <PolarisModal.Section>
    <Loader />
  </PolarisModal.Section>
)

type ModalProps = {
  open?: boolean;
  title?: string;
  children?: React.ReactNode;
  confirmBtnText?: string;
  onConfirm: () => void;
  hideCancelBtn?: boolean;
  cancelBtnText?: string;
  onClose: () => void;
};

const Modal: FC<ModalProps> = ({
  children,
  open,
  title,
  confirmBtnText = "Save",
  onConfirm,
  hideCancelBtn = false,
  cancelBtnText = "Cancel",
  onClose,
}) => {
  return (
    <PolarisModal
      open={!!open}
      title={title}
      onClose={onClose}
      {...(onConfirm ? {
        primaryAction: {
          content: confirmBtnText,
          onAction: onConfirm,
        }
      } : {})}
      {...(!hideCancelBtn ? {
        secondaryActions: [{
          content: cancelBtnText,
          onAction: onClose,
        }]
      } : {})}
    >
      {children}
    </PolarisModal>
  );
};

export default Modal;
