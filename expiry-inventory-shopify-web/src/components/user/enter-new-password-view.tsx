import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Button, FormLayout, Modal, TextField } from '@shopify/polaris';

interface Props {
  onSubmit: (values: NewPasswordFormValues) => void;
  loading: boolean;
}

type NewPasswordFormValues = {
  password: string;
  password_confirmation: string;
};

const EnterNewPasswordView = ({ onSubmit, loading }: Props) => {
  const {
    watch,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm<NewPasswordFormValues>({
    defaultValues: {
      password: "",
      password_confirmation: "",
    },
    resolver: yupResolver(yup.object().shape({
      password: yup.string().required("Password is required").min(8, "Password should consist of at least 8 characters"),
      password_confirmation: yup.string().required("Confirm password is required").oneOf([yup.ref('password')], 'Passwords do not match')
    }))
  });

  const formValues = watch();
  function setValues(value: any, field: (keyof NewPasswordFormValues)) {
    setValue(field, value);
  }

  return (
    <Modal.Section>
      <p className="mb-4">Enter new password</p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <FormLayout>
          <FormLayout.Group>
            <TextField
              id="password"
              type="password"
              label="New Password"
              autoComplete=""
              error={errors.password?.message}
              value={formValues.password}
              onChange={setValues}
            />
            <TextField
              id="password_confirmation"
              type="password"
              label="Confirm Password"
              autoComplete=""
              error={errors.password_confirmation?.message}
              value={formValues.password_confirmation}
              onChange={setValues}
            />
          </FormLayout.Group>
          <div className="text-right py-4 space-x-4">
            <Button
              primary
              submit
              loading={loading}
            >
              Submit
            </Button>
          </div>
        </FormLayout>
      </form>
    </Modal.Section>
  );
};

export default EnterNewPasswordView;
