import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Button, FormLayout, Modal, TextField } from '@shopify/polaris';
import ResendCodeButton from './resend-code-button';

interface Props {
  onSubmit: (values: { code: string }) => void;
  loading: boolean;
}

const EnterTokenView = ({ onSubmit, loading }: Props) => {
  const {
    watch,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm<{ code: string }>({
    resolver: yupResolver(
      yup.object().shape({
        code: yup.string().required("Code is required"),
      })
    ),
  });

  const formValues = watch();
  function setValues(value: any, field: (keyof { code: string; })) {
    setValue(field, value);
  }

  return (
    <Modal.Section>
      <p className="mb-4">Enter password to confirm batches deletion</p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <FormLayout>
          <FormLayout.Group>
            <div className="flex items-end gap-6">
              <TextField
                id="code"
                type="text"
                label="Code"
                autoComplete=""
                error={errors.code?.message}
                value={formValues.code}
                onChange={setValues}
              />
              <ResendCodeButton />
            </div>
          </FormLayout.Group>
          <div className="text-right py-4 space-x-4">
            <Button
              primary
              submit
              loading={loading}
            >
              Submit
            </Button>
          </div>
        </FormLayout>
      </form>
    </Modal.Section>
  );
};

export default EnterTokenView;
