import { ModalContentLoader } from "@components/ui/modal/modal";
import { useModalAction } from "@components/ui/modal/modal.context";
import { useForgetPasswordMutation } from "@data/merchant/use-forgot-password.mutation";
import { useResetPasswordMutation } from "@data/merchant/use-reset-password.mutation";
import { useVerifyForgetPasswordTokenMutation } from "@data/merchant/use-verify-forget-password-token.mutation";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { Banner } from "@shopify/polaris";
import { renderErrors, showToast, toastOptions } from "@utils/helpers";
import dynamic from "next/dynamic";
import { useState } from "react";

const RequestEmailTokenView = dynamic(
  () => import('./request-email-token-view')
);
const EnterTokenView = dynamic(
  () => import('./enter-token-view')
);
const EnterNewPasswordView = dynamic(
  () => import('./enter-new-password-view')
);

const ForgotPassword: React.FC<{ email: string | null; }> = ({ email }) => {
  const { openModal } = useModalAction();
  const { mutate: forgetPassword, isLoading } = useForgetPasswordMutation();
  const { mutate: verifyToken, isLoading: verifying } = useVerifyForgetPasswordTokenMutation();
  const { mutate: resetPassword, isLoading: resetting } = useResetPasswordMutation();
  const [errorMsg, setErrorMsg] = useState<string | null | undefined>('');
  const [isEmailSent, setIsEmailSent] = useState(false);
  const [verifiedToken, setVerifiedToken] = useState<string | null>(null);

  function handleRequestForgetPassword() {
    forgetPassword(undefined, {
      onSuccess: () => {
        setIsEmailSent(true);
      },
      onError: (response: any) => {
        const errorData = response.data;
        showToast({ message: renderErrors(errorData) ?? "Code request failed", duration: 2000, isError: true });
      }
    });
  }
  function handleTokenSubmit({ code }: { code: string; }) {
    setErrorMsg(null);
    verifyToken({
      code
    }, {
      onSuccess: () => {
        setVerifiedToken(code);
      },
      onError: (response: any) => {
        const errorData = response.data;
        showToast({ message: renderErrors(errorData) ?? "Verification is failed", duration: 2000, isError: true });
      }
    });
  }
  function handleResetPassword({ password, password_confirmation }: { password: string; password_confirmation: string; }) {
    setErrorMsg(null);
    resetPassword({
      code: verifiedToken!,
      password,
      password_confirmation
    }, {
      onSuccess: () => {
        showToast(toastOptions.PASSWORD_RESET_SUCCESS);
        openModal("CLEAR_BATCHES_VIEW", null, { modalTitle: "Delete all batches", hideCancelBtn: true });
      },
      onError: (response: any) => {
        const errorData = response.data;
        showToast({ message: renderErrors(errorData) ?? "Reset password is failed", duration: 2000, isError: true });
      }
    });
  }

  return (
    <>
      {errorMsg && (
        <Banner
          title="Error"
          status="critical"
        >
          <p>{errorMsg}</p>
        </Banner>
      )}
      {!isEmailSent ? (
        <RequestEmailTokenView email={email} loading={isLoading} onSubmit={handleRequestForgetPassword} />
      ) : <></>}
      {(isEmailSent && !verifiedToken) ? (
        <EnterTokenView loading={verifying} onSubmit={handleTokenSubmit} />
      ) : <></>}
      {(isEmailSent && verifiedToken) ? (
        <EnterNewPasswordView loading={resetting} onSubmit={handleResetPassword} />
      ) : <></>}
    </>
  )
}

const ForgotPasswordView = () => {
  const {
    data,
    isLoading
  } = useSettingsQuery();
  if(isLoading || !data) {
    return (
      <ModalContentLoader />
    );
  }
  return (
    <ForgotPassword email={data.shop ? data.shop.email : null} />
  );
}

export default ForgotPasswordView;