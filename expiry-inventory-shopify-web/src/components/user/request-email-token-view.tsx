import { Button, Modal } from "@shopify/polaris";

interface Props {
  email: string | null;
  onSubmit: () => void;
  loading: boolean;
}

const RequestEmailTokenView = ({ email, onSubmit, loading }: Props) => {
  return (
    <Modal.Section>
      <p className="mb-4">Press below <strong>Request</strong> button to request for a verification code to be sent to your email.</p>
      {email ? <p className="mb-4">An email will be sent to this email address: <strong>{email}</strong></p> : <></>}
      <Button
        primary
        onClick={onSubmit}
        loading={loading}
      >
        Request
      </Button>
    </Modal.Section>
  )
}

export default RequestEmailTokenView;