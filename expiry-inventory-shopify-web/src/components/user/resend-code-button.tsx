import { useForgetPasswordMutation } from '@data/merchant/use-forgot-password.mutation';
import { Button } from '@shopify/polaris';
import moment from 'moment';
import React from 'react';

interface Props {
}

const ResendCodeButton = ({}: Props) => {
  const {
    mutate: resendForgetPasswordRequest,
    isLoading: resendForgetPasswordLoading,
    canRequestAt: forgetPasswordCanRequestAt,
  } = useForgetPasswordMutation();

  // interval every second on the button after request code
  const [seconds, setSeconds] = React.useState(0);
  React.useEffect(() => {
    const canRequestAt = forgetPasswordCanRequestAt;
    if (canRequestAt) {
      setSeconds(moment.unix(canRequestAt).diff(moment(), 'seconds'));
      const interval = setInterval(() => {
        setSeconds((seconds) => seconds - 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [forgetPasswordCanRequestAt]);
  return (
    <Button
      loading={resendForgetPasswordLoading}
      disabled={resendForgetPasswordLoading || seconds > 0}
      onClick={() => resendForgetPasswordRequest()}
    >
      {seconds > 0
        ? 'Request again in ' + seconds + 's'
        : "Resend code"}
    </Button>
  );
};

export default ResendCodeButton;
