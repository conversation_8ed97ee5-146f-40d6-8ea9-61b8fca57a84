import React from 'react';
import { useRouter } from 'next/router';

export interface State {
  shop: string | null;
  host: string | null;
}

const initialState = {
  shop: null,
  host: null
};

type Action =
  | {
      type: 'SET_SHOP';
      value: string | null;
    }
  | {
      type: 'SET_HOST';
      value: string | null;
    };

export const ShopifyContext = React.createContext<State | any>(initialState);

ShopifyContext.displayName = 'ShopifyContext';

function shopifyReducer(state: State, action: Action) {
  switch (action.type) {
    case 'SET_SHOP': {
      return {
        ...state,
        shop: action.value,
      };
    }
    case 'SET_HOST': {
      return {
        ...state,
        host: action.value,
      };
    }
  }
}

export const ShopifyProvider: React.FC = (props) => {
  const [state, dispatch] = React.useReducer(shopifyReducer, {
    ...initialState,
  });
  const router = useRouter();

  React.useEffect(() => {
    if (router.query?.host) {
      setHost(router.query.host as string);
    }
    if (router.query?.shop) {
      setShop(router.query.shop as string);
    }
  }, [router.isReady, router.query]);

  const setShop = (_value: string | null) =>
    dispatch({ type: 'SET_SHOP', value: _value });
  const setHost = (_value: string | null) =>
    dispatch({ type: 'SET_HOST', value: _value });

  const value = React.useMemo(
    () => ({
      ...state,
      setShop,
      setHost,
    }),
    [state]
  );
  return <ShopifyContext.Provider value={value} {...props} />;
};

export const useShopify = () => {
  const context = React.useContext(ShopifyContext);
  if (context === undefined) {
    throw new Error(`useShopify must be used within a ShopifyProvider`);
  }
  return context;
};

export const ManagedShopifyContext: React.FC = ({ children }) => (
  <ShopifyProvider>
    {children}
  </ShopifyProvider>
);
