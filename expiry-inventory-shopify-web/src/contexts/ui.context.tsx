import React from 'react';
import { Location } from '@ts-types/types';
import { useLocalStorage } from '@utils/use-local-storage';
import { useQueryClient } from 'react-query';
import { useLocationsQuery } from '@data/location/use-locations.query';

export interface State {
    locations: Location[];
    location: Location | null;
    inventory_status: string[] | null;
    batched_status: string[] | null;
}

const initialState = {
  locations: [],
  location: null,
  inventory_status: [],
  batched_status: [],
};

type Action =
  | {
      type: 'SET_LOCATION';
      value: Location | null;
    }
  | {
      type: 'SET_LOCATIONS';
      value: Location[];
    }
  | {
      type: 'SET_INVENTORY_STATUS';
      value: string[] | null;
    }
  | {
      type: 'SET_BATCHED_STATUS';
      value: string[] | null;
    };

export const UIContext = React.createContext<State | any>(initialState);

UIContext.displayName = 'UIContext';

function uiReducer(state: State, action: Action) {
  switch (action.type) {
    case 'SET_LOCATION': {
      return {
        ...state,
        location: action.value,
      };
    }
    case 'SET_LOCATIONS': {
      return {
        ...state,
        locations: action.value,
        ...((action.value.length > 0 && (!state.location || !action.value.find(({ id }) => state.location!.id == id))) ? { location: action.value[0] } : {}),
      };
    }
    case 'SET_INVENTORY_STATUS': {
      return {
        ...state,
        inventory_status: action.value,
      };
    }
    case 'SET_BATCHED_STATUS': {
      return {
        ...state,
        batched_status: action.value,
      };
    }
  }
}

export const UIProvider: React.FC = (props) => {
  const [savedData, saveData] = useLocalStorage(
    `expiry_data`,
    JSON.stringify(initialState)
  );
  const [state, dispatch] = React.useReducer(uiReducer, {
    ...savedData ? JSON.parse(savedData!) : initialState,
  });
  const { data } = useLocationsQuery();

  React.useEffect(() => {
    saveData(JSON.stringify(state));
  }, [state, saveData]);

  React.useEffect(() => {
    if (data) {
        setLocations(data);
    }
  }, [data]);

  const setLocations = (_value: Location[]) =>
    dispatch({ type: 'SET_LOCATIONS', value: _value });
  const setLocation = (_value: Location | null) =>
    dispatch({ type: 'SET_LOCATION', value: _value });
  const setInventoryStatus = (_value: string[] | null) =>
    dispatch({ type: 'SET_INVENTORY_STATUS', value: _value });
  const setBatchedStatus = (_value: string[] | null) =>
    dispatch({ type: 'SET_BATCHED_STATUS', value: _value });

  const value = React.useMemo(
    () => ({
      ...state,
      setLocation,
      setLocations,
      setInventoryStatus,
      setBatchedStatus,
    }),
    [state]
  );
  return <UIContext.Provider value={value} {...props} />;
};

export const useUI = () => {
  const context = React.useContext(UIContext);
  if (context === undefined) {
    throw new Error(`useUI must be used within a UIProvider`);
  }
  return context;
};

export const ManagedUIContext: React.FC = ({ children }) => (
    <UIProvider>
        {children}
    </UIProvider>
);
