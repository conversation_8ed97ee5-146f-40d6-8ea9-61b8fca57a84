import { BatchProductAnalyticsResponse, ChartDataByDate, Settings } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { includeQueryIntoUrl } from "@utils/helpers";
import { useMemo } from "react";
import { useQuery } from "react-query";
import { useAuthenticatedFetch } from "@hooks/useAuthenticatedFetch";

export const useAnalyticsQuery = (options?: any) => {
    const authenticatedFetch = useAuthenticatedFetch();
    const fetchUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_ANALYTICS}`;

    const fetch = useMemo(() => {
        return async () => {
            const withQueryUrl = includeQueryIntoUrl(fetchUrl, options);
            const response = await authenticatedFetch(withQueryUrl);
            const { data } = await response.json();
            return data;
        };
    }, [fetchUrl, JSON.stringify(options)]);

    return useQuery<ChartDataByDate[]>([API_ENDPOINTS.INVENTORY_ANALYTICS, options], fetch, {
        refetchOnWindowFocus: false,
    });
}

export const useProductAnalyticsQuery = (options?: any) => {
    const authenticatedFetch = useAuthenticatedFetch();
    const fetchUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_PRODUCT_ANALYTICS}`;

    const fetch = useMemo(() => {
        return async () => {
            const withQueryUrl = includeQueryIntoUrl(fetchUrl, options);
            const response = await authenticatedFetch(withQueryUrl);
            const { data } = await response.json();
            return data;
        };
    }, [fetchUrl, JSON.stringify(options)]);

    return useQuery<BatchProductAnalyticsResponse>([API_ENDPOINTS.INVENTORY_PRODUCT_ANALYTICS, options], fetch, {
        refetchOnWindowFocus: false,
    });
}