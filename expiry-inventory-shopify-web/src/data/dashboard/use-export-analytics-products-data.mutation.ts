import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation } from "react-query";

interface ExportAnalyticsProductsDataMutationProps {
  type: string;
  days: number;
}

export const useExportAnalyticsProductsDataMutation = () => {
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_ANALYTICS_PRODUCTS_EXPORT}`,
    fetchInitOptions: {
      method: "POST"
    }
  });
  return useMutation((input: ExportAnalyticsProductsDataMutationProps) => fetch(null, input), {});
}