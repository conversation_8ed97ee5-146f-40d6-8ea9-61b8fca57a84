import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useMemo } from "react";
import { useMutation } from "react-query";
import { useAuthenticatedFetch } from "@hooks/useAuthenticatedFetch";

export const useCancelDiscountMutation = (discount_id: string | number) => {
    const authenticatedFetch = useAuthenticatedFetch();
    const fetchUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.DISCOUNT}/${discount_id}/cancel`;

    const fetch = useMemo(() => {
        return async () => {
          const response = await authenticatedFetch(fetchUrl, { method: "POST" });
          const { data } = await response.json();
          return data;
        };
    }, [fetchUrl, discount_id]);

    return useMutation(fetch);
  };
  