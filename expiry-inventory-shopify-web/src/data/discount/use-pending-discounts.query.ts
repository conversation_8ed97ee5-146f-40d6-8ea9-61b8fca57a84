import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useMemo } from "react";
import { useQuery } from "react-query";
import { useAuthenticatedFetch } from "@hooks/useAuthenticatedFetch";
import { Discount } from "@ts-types/types";

export const usePendingDiscountsQuery = (queryOptions?: { fetchInit?: object; reactQueryOptions?: any }) => {
    const { fetchInit, reactQueryOptions } = queryOptions ?? {};
    const authenticatedFetch = useAuthenticatedFetch();
    const fetchUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.PENDING_DISCOUNTS}`;

    const fetch = useMemo(() => {
        return async () => {
          const response = await authenticatedFetch(fetchUrl, fetchInit);
          const { data } = await response.json();
          return data;
        };
    }, [fetchUrl, JSON.stringify(fetchInit)]);

    return useQuery<Discount[]>(API_ENDPOINTS.PENDING_DISCOUNTS, fetch, {
        ...reactQueryOptions,
        refetchOnWindowFocus: false,
    });
}