import { BaseQueryOptions, ImportHistoryPaginationData } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useMemo } from "react";
import { useQuery } from "react-query";
import { useAuthenticatedFetch } from "@hooks/useAuthenticatedFetch";

export const useImportHistoriesQuery = (queryOptions?: { fetchInit?: object; reactQueryOptions?: any; query?: BaseQueryOptions; }) => {
    const { fetchInit, reactQueryOptions, query = {} } = queryOptions ?? {};
    const authenticatedFetch = useAuthenticatedFetch();
    let fetchUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.IMPORT_HISTORY}?page=${query ? query.page ?? 1 : 1}`;
    if(query.order_by) {
        fetchUrl += `&order_by=${query.order_by}`;
    }
    if(query.order_direction) {
        fetchUrl += `&order_direction=${query.order_direction}`;
    }

    const fetch = useMemo(() => {
        return async () => {
          const response = await authenticatedFetch(fetchUrl, fetchInit);
          const { data } = await response.json();
          return data;
        };
    }, [fetchUrl, JSON.stringify(fetchInit)]);
    
    return useQuery<ImportHistoryPaginationData>([API_ENDPOINTS.IMPORT_HISTORY, query], fetch, {
        ...reactQueryOptions,
        refetchOnWindowFocus: false,
    });
}