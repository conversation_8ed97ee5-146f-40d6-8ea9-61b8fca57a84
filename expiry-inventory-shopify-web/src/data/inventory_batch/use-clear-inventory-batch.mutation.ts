import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

interface InventoryBatchDeleteAllMutationProps {
  password: string;
}

export const useInventoryBatchDeleteAllMutation = () => {
  const queryClient = useQueryClient();
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_BATCH_DELETE_ALL}`,
    fetchInitOptions: {
      method: "DELETE"
    }
  });
  return useMutation((input: InventoryBatchDeleteAllMutationProps) => fetch(null, input), {
    onSuccess: () => {
      queryClient.invalidateQueries(API_ENDPOINTS.INVENTORY_BATCH);
    }
  });
}