import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

interface CreateVerificationInput {
  inventory_batch_id: string | number;
  quantity: number;
  adjusted_by: string;
}

export const useCreateInventoryBatchVerificationMutation = () => {
  const queryClient = useQueryClient();
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_BATCH}`,
    fetchInitOptions: {
      method: "POST",
    },
  });
  return useMutation((input: CreateVerificationInput) =>
    fetch({ appendPath: `/${input.inventory_batch_id}/verifications` }, input)
  , {
    onSuccess: (_, input) => {
      queryClient.invalidateQueries([
        API_ENDPOINTS.INVENTORY_BATCH,
        input.inventory_batch_id,
        "verifications",
      ]);
    },
  });
};

