import { InventoryBatchInput } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

interface InventoryBatchCreateMutationProps {
  variables: InventoryBatchInput;
}

export const useCreateInventoryBatchMutation = () => {
  const queryClient = useQueryClient();
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_BATCH}`,
    fetchInitOptions: {
      method: "POST"
    }
  });
  return useMutation((input: InventoryBatchCreateMutationProps) => fetch(null, input.variables), {
    onSuccess: () => {
      queryClient.invalidateQueries(API_ENDPOINTS.INVENTORY_BATCH);
    }
  });
}