import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

export const useDeleteInventoryBatchMutation = () => {
  const queryClient = useQueryClient();
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_BATCH}`,
    fetchInitOptions: {
      method: "DELETE"
    }
  });
  return useMutation((id: string | number) => fetch({ appendPath: `/${id}` }), {
    onSuccess: () => {
      queryClient.invalidateQueries(API_ENDPOINTS.INVENTORY_BATCH);
    }
  });
}