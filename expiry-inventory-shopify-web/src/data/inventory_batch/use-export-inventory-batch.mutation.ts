import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation } from "react-query";

interface InventoryBatchExportMutationProps {
  from: Date;
  to: Date;
  status: string | null;
  location: string | number | null;
}

export const useInventoryBatchExportMutation = () => {
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_BATCH_EXPORT}`,
    fetchInitOptions: {
      method: "POST"
    }
  });
  return useMutation((input: InventoryBatchExportMutationProps) => fetch(null, input), {});
}