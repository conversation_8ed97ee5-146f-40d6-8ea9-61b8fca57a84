import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation } from "react-query";

export const useInventoryBatchImportMutation = () => {
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_BATCH_IMPORT}`,
    fetchInitOptions: {
      is_form_data: true,
      method: "POST",
    }
  });
  return useMutation((input: FormData) => fetch(null, input), {});
}