import { InventoryBatchVerification } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useQuery } from "react-query";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";

export const useInventoryBatchVerificationsQuery = (
  id: string | number,
  queryOptions?: { reactQueryOptions?: any }
) => {
  const { reactQueryOptions } = queryOptions ?? {};
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_BATCH}/${id}/verifications`,
  });

  return useQuery<Array<InventoryBatchVerification>>(
    [API_ENDPOINTS.INVENTORY_BATCH, id, "verifications"],
    fetch,
    {
      ...reactQueryOptions,
      refetchOnWindowFocus: false,
      retry: 3,
    }
  );
};

