import { InventoryBatchInput } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

interface InventoryBatchUpdateMutationProps {
  id: string | number;
  variables: InventoryBatchInput;
}

export const useUpdateInventoryBatchMutation = () => {
  const queryClient = useQueryClient();
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.INVENTORY_BATCH}`,
    fetchInitOptions: {
      method: "PUT"
    }
  });
  return useMutation((input: InventoryBatchUpdateMutationProps) => fetch({ appendPath: `/${input.id}` }, input.variables), {
    onSuccess: () => {
      queryClient.invalidateQueries(API_ENDPOINTS.INVENTORY_BATCH);
    }
  });
}