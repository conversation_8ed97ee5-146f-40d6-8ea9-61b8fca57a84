import { Location } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useMemo } from "react";
import { useQuery } from "react-query";
import { useAuthenticatedFetch } from "@hooks/useAuthenticatedFetch";

export const useLocationsQuery = (queryOptions?: {
  query?: object;
  fetchInit?: object;
  reactQueryOptions?: any;
}) => {
  const { fetchInit, reactQueryOptions, query } = queryOptions ?? {};
  const authenticatedFetch = useAuthenticatedFetch();
  let requestURL = "";
  if (query) {
    requestURL += "?";
    requestURL += Object.keys(query)
      // @ts-ignore
      .map((key) => key + "=" + query[key])
      .join("&");
  }
  const fetchUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.LOCATION}${requestURL}
  `;

  const fetch = useMemo(() => {
    return async () => {
      const response = await authenticatedFetch(fetchUrl, fetchInit);
      const { data } = await response.json();
      return data;
    };
  }, [fetchUrl, JSON.stringify(fetchInit), requestURL]);

  return useQuery<Location[]>(API_ENDPOINTS.LOCATION + requestURL, fetch, {
    ...reactQueryOptions,
    refetchOnWindowFocus: false,
    retry: 3,
  });
};
