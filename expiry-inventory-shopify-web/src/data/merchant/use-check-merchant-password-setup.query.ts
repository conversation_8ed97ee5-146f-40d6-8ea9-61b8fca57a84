import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useQuery } from "react-query";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";

export const useCheckMerchantPasswordSetupQuery = (queryOptions?: { fetchInit?: object; reactQueryOptions?: any }) => {
  const { fetchInit, reactQueryOptions } = queryOptions ?? {};
  const fetch = useShopifyFetch({ url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.MERCHANT_PASSWORD_CHECK}`, fetchInitOptions: fetchInit});

  return useQuery<{ is_password_setup_required: boolean; }>(API_ENDPOINTS.MERCHANT_PASSWORD_CHECK, fetch, {
    ...reactQueryOptions,
    refetchOnWindowFocus: false,
  });
}