import { useMutation } from 'react-query';
import moment from 'moment';
import { useLocalStorage } from 'react-use';
import { useShopifyFetch } from '@utils/api/use-shopify-fetch';
import { API_ENDPOINTS } from '@utils/api/endpoints';

export const useForgetPasswordMutation = () => {
  const [canRequestAt, setCanRequestAt] = useLocalStorage<number | null>(
    'token-request-at',
    null
  );
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.MERCHANT_SEND_EMAIL_VERIFY}`,
    fetchInitOptions: {
      method: "POST"
    }
  });
  return {
    canRequestAt,
    ...useMutation(
      () => fetch(),
      {
        onSuccess: (res) => {
          setCanRequestAt(
            moment().add(res.data ? (res.data.request_timeout_period ?? 30) : 30, 'seconds').unix()
          );
        },
      }
    ),
  };
};
