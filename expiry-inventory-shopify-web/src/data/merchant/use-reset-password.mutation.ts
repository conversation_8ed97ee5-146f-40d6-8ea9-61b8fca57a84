import { useMutation } from 'react-query';
import { API_ENDPOINTS } from '@utils/api/endpoints';
import { useShopifyFetch } from '@utils/api/use-shopify-fetch';

interface ResetPasswordInputType {
  code: string;
  password: string;
  password_confirmation: string;
}

export const useResetPasswordMutation = () => {
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.MERCHANT_RESET_PASSWORD}`,
    fetchInitOptions: {
      method: "POST"
    }
  });
  return useMutation((input: ResetPasswordInputType) => fetch(null, input));
};
