import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

interface SetupMerchantPasswordMutationProps {
  password: string;
  password_confirmation: string;
}

export const useSetupMerchantPasswordMutation = () => {
  const queryClient = useQueryClient();
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.MERCHANT_PASSWORD_SETUP}`,
    fetchInitOptions: {
      method: "POST"
    }
  });
  return useMutation((input: SetupMerchantPasswordMutationProps) => fetch(null, input), {
    onSuccess: () => {
      queryClient.invalidateQueries(API_ENDPOINTS.MERCHANT_PASSWORD_CHECK);
    }
  });
}