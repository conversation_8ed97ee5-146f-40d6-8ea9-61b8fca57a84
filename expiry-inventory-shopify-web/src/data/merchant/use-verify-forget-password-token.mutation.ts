import { API_ENDPOINTS } from '@utils/api/endpoints';
import { useShopifyFetch } from '@utils/api/use-shopify-fetch';
import { useMutation } from 'react-query';

interface VerifyPasswordInputType {
  code: string;
}

export const useVerifyForgetPasswordTokenMutation = () => {
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.MERCHANT_VERIFY_FORGOT_PASSWORD}`,
    fetchInitOptions: {
      method: "POST"
    }
  });
  return useMutation((input: VerifyPasswordInputType) => fetch(null, input));
};
