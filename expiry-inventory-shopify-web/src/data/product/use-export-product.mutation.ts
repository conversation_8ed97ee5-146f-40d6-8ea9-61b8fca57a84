import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation } from "react-query";

export const useProductExportMutation = () => {
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.PRODUCT_EXPORT}`,
    fetchInitOptions: {
      method: "POST"
    }
  });
  return useMutation((input: any) => fetch(null, input), {});
}