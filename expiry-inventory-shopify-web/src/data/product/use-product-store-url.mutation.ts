import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useMutation } from "react-query";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";

export const useProductStoreURLMutation = () => {
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.PRODUCT_PREVIEW}`,
    fetchInitOptions: {
      method: "GET"
    }
  });
  return useMutation((id: string | number) => fetch({ appendPath: `/${id}` }));
}