import { Product } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useQuery } from "react-query";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";

export const useProductQuery = (id: string | number, queryOptions?: { query?: object; fetchInit?: object; reactQueryOptions?: any }) => {
  const { query, fetchInit, reactQueryOptions } = queryOptions ?? {};
  const fetch = useShopifyFetch({ url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.PRODUCT}/${id}`, query, fetchInitOptions: fetchInit});

  return useQuery<Product>([API_ENDPOINTS.PRODUCT, id], fetch, {
    ...reactQueryOptions,
    refetchOnWindowFocus: false,
  });
}