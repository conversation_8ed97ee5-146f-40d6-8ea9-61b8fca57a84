import { useMutation, useQueryClient } from "react-query";
import { Settings } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import _store from "@utils/redux/store";
import { setShop } from "@utils/redux/features/shop/shopSlice";

interface SettingsMutationProps {
  variables: Settings;
}

export const useSettingsMutation = () => {
  const queryClient = useQueryClient();
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.PREFERENCE}`,
    fetchInitOptions: {
      method: "PUT"
    }
  });
  return useMutation((input: SettingsMutationProps) => fetch(null, input.variables), {
    onSuccess: (data, { variables }) => {
      _store.store.dispatch(setShop({settings: variables}));
      queryClient.invalidateQueries(API_ENDPOINTS.PREFERENCE);
    }
  });
}