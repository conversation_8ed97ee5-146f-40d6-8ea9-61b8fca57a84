import { Settings } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useQuery } from "react-query";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";

export const useSettingsQuery = (queryOptions?: { fetchInit?: object; reactQueryOptions?: any }) => {
  const { fetchInit, reactQueryOptions } = queryOptions ?? {};
  const fetch = useShopifyFetch({ url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.PREFERENCE}`, fetchInitOptions: fetchInit});

  return useQuery<Settings>(API_ENDPOINTS.PREFERENCE, fetch, {
    ...reactQueryOptions,
    refetchOnWindowFocus: false,
  });
}