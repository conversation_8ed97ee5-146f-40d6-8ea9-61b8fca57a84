import { API_ENDPOINTS } from "@utils/api/endpoints";
import { fetchAPI } from "@utils/helpers";
import { useMutation } from "react-query";

export const useShopifyAuthCallbackMutation = () => {
  return useMutation(
    (input: any) => fetchAPI("POST", API_ENDPOINTS.SHOPIFY_AUTH_CALLBACK, input, false),
    {
      // Always refetch after error or success:
      onSettled: (_, e) => {
      },
    }
  );
};
