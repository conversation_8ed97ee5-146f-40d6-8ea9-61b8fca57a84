import { API_ENDPOINTS } from "@utils/api/endpoints";
import { fetchAPI } from "@utils/helpers";
import { useMutation } from "react-query";

interface OAuthInputProps {
  host?: string;
  input: any;
};

export const useShopifyOAuthMutation = () => {
  return useMutation(
    ({ host, input }: OAuthInputProps) => fetchAPI("POST", API_ENDPOINTS.SHOPIFY_OAUTH, input, false, host),
    {
      // Always refetch after error or success:
      onSettled: (_, e) => {
      },
    }
  );
};
