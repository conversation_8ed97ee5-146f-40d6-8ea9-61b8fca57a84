import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useMemo } from "react";
import { useQuery } from "react-query";
import { useAuthenticatedFetch } from "@hooks/useAuthenticatedFetch";
import { Theme } from "@ts-types/types";

export const useThemeQuery = (theme_id: string | number) => {
    const authenticatedFetch = useAuthenticatedFetch();
    const fetchUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.THEMES}/${theme_id}`;
    
    const fetch = useMemo(() => {
        return async () => {
          const response = await authenticatedFetch(fetchUrl);
          const { data } = await response.json();
          return data;
        };
    }, [fetchUrl, theme_id]);

    return useQuery<Theme>(`${API_ENDPOINTS.THEMES}/${theme_id}`, fetch, {
        refetchOnWindowFocus: false,
    });
}