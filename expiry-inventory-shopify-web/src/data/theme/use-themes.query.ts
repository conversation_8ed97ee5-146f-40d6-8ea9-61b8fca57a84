import { Theme } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useMemo } from "react";
import { useQuery } from "react-query";
import { useAuthenticatedFetch } from "@hooks/useAuthenticatedFetch";

export const useThemesQuery = (queryOptions?: { fetchInit?: object; reactQueryOptions?: any }) => {
    const { fetchInit, reactQueryOptions } = queryOptions ?? {};
    const authenticatedFetch = useAuthenticatedFetch();
    const fetchUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.THEMES}`;

    const fetch = useMemo(() => {
        return async () => {
          const response = await authenticatedFetch(fetchUrl, fetchInit);
          const { data } = await response.json();
          return data;
        };
    }, [fetchUrl, JSON.stringify(fetchInit)]);

    return useQuery<Theme[]>(API_ENDPOINTS.THEMES, fetch, {
        ...reactQueryOptions,
        staleTime: 5 * 60 * 1000, // 5 minutes
        refetchOnWindowFocus: false,
    });
}