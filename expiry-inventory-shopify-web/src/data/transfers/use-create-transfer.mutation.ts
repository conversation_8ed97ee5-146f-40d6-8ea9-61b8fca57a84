import { InventoryBatchTransferInput } from "@ts-types/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

interface CreateTransferMutationProps {
  variables: InventoryBatchTransferInput;
}

export const useCreateTransferMutation = () => {
  const queryClient = useQueryClient();
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.TRANSFERS_STORE}`,
    fetchInitOptions: {
      method: "POST",
    },
  });
  
  return useMutation(
    (input: CreateTransferMutationProps) =>
      fetch(null, input.variables),
    {
      retry: 3,
      onSuccess: (_, input) => {
        queryClient.invalidateQueries([
          API_ENDPOINTS.INVENTORY_BATCH,
          input.variables.source_batch_id,
        ]);
        queryClient.invalidateQueries([API_ENDPOINTS.TRANSFERS]);
      },
    }
  );
};
