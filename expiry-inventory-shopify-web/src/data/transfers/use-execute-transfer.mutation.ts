import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

export const useExecuteTransferMutation = () => {
  const queryClient = useQueryClient();
  
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.TRANSFERS}`,
    fetchInitOptions: {
      method: "POST",
    },
  });

  return useMutation(
    (transferId: string | number) => {
      return fetch({ appendPath: `/${transferId}/execute` });
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries([API_ENDPOINTS.TRANSFERS]);
        queryClient.invalidateQueries([API_ENDPOINTS.INVENTORY_BATCH]);
      },
    }
  );
};
