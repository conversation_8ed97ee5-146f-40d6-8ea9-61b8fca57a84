import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useQuery } from "react-query";

interface TransfersQueryParams {
  page?: number;
  status?: string;
  is_draft?: boolean;
}

export const useTransfersQuery = (params: TransfersQueryParams = {}) => {
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.TRANSFERS}`,
    fetchInitOptions: {
      method: "GET",
    },
    query: params,
  });

  return useQuery(
    [API_ENDPOINTS.TRANSFERS, params],
    fetch,
    {
      refetchOnWindowFocus: false,
      retry: 3,
    }
  );
};
