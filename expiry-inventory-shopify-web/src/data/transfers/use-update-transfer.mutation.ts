import { API_ENDPOINTS } from "@utils/api/endpoints";
import { useShopifyFetch } from "@utils/api/use-shopify-fetch";
import { useMutation, useQueryClient } from "react-query";

interface UpdateTransferInput {
  id: string | number;
  quantity?: number;
  notes?: string;
  source_batch_id?: string | number;
  destination_batch_id?: string | number;
  source_quantity_adjustment?: boolean;
  destination_quantity_adjustment?: boolean;
}

export const useUpdateTransferMutation = () => {
  const queryClient = useQueryClient();
  
  const fetch = useShopifyFetch({
    url: `${process.env.NEXT_PUBLIC_BACKEND_URL}${API_ENDPOINTS.TRANSFERS}`,
    fetchInitOptions: {
      method: "PUT",
    },
  });

  return useMutation(
    (input: UpdateTransferInput) => {
      const { id, ...data } = input;
      return fetch({ appendPath: `/${id}` }, data);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries([API_ENDPOINTS.TRANSFERS]);
      },
    }
  );
};
