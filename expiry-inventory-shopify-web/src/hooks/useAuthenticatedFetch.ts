import { authenticatedFetch } from "@shopify/app-bridge/utilities";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { AppBridgeState, ClientApplication } from "@shopify/app-bridge";

const app_env = process.env.NEXT_PUBLIC_APP_ENV;

/**
 * @returns {Function} fetch function
 */
export function useAuthenticatedFetch() {
  const app = useAppBridge();
  const fetchFunction = authenticatedFetch(app);

  return async (uri: string, options?: any) => {
    const response = await fetchFunction(uri, {
      ...options,
      headers: {
        ...options?.headers,
        ...(app_env == "dev"
          ? {
              "ngrok-skip-browser-warning": "1",
              "User-Agent": "Bypass-Ngrok",
            }
          : {}),
      },
    });
    checkHeadersForReauthorization(response.headers, app);
    return response;
  };
}

function checkHeadersForReauthorization(
  headers: Headers,
  app: ClientApplication<AppBridgeState>
) {
  if (headers.get("X-Shopify-API-Request-Failure-Reauthorize") === "1") {
    const authUrlHeader =
      headers.get("X-Shopify-API-Request-Failure-Reauthorize-Url") ||
      `/api/auth`;

    const redirect = Redirect.create(app);
    redirect.dispatch(
      Redirect.Action.REMOTE,
      authUrlHeader.startsWith("/")
        ? `https://${window.location.host}${authUrlHeader}`
        : authUrlHeader
    );
  }
}
