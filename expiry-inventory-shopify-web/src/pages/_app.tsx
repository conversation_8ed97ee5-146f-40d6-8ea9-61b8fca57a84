import type { AppProps } from "next/app";
import { Provider as ReduxProvider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { AppProvider as PolarisAppProvider } from "@shopify/polaris";
import enTranslations from "@shopify/polaris/locales/en.json";
import { I18nContext, I18nManager } from "@shopify/react-i18n";

import _store from "@utils/redux/store";
import ManagedModal from "@components/ui/modal/managed-modal";
import { QueryClient, QueryClientProvider } from "react-query";
import { Hydrate } from "react-query/hydration";
import { queryClientConfig } from "@settings/query.settings";

import "@shopify/polaris/build/esm/styles.css";
import "@uiw/react-textarea-code-editor/dist.css";
import "flatpickr/dist/themes/light.css";
import "@styles/base.css";
import "@styles/print.css";
import { useRef } from "react";
import { ModalProvider } from "@components/ui/modal/modal.context";
import NextLink from "next/link";
import { ManagedShopifyContext } from "@contexts/shopify.context";
import { LinkLikeComponentProps } from "@shopify/polaris/build/ts/latest/src/utilities/link";

const Noop: React.FC = ({ children }) => <>{children}</>;

const CustomApp = ({ Component, pageProps, router }: AppProps) => {
  const queryClientRef = useRef<any>();
  if (!queryClientRef.current) {
    queryClientRef.current = new QueryClient(queryClientConfig);
  }
  const Layout = (Component as any).Layout || Noop;
  const hideFooter = (Component as any).HideFooter || false;

  const locale = "en";
  const i18nManager = new I18nManager({
    locale,
  });

  return (
    <>
      <QueryClientProvider client={queryClientRef.current}>
        <Hydrate state={pageProps.dehydratedState}>
          <ReduxProvider store={_store.store}>
            <PersistGate loading={null} persistor={_store.persistor}>
              <PolarisAppProvider
                i18n={{
                  ...enTranslations,
                  Polaris: {
                    Frame: {
                      skipToContent: "Skip to content",
                    },
                    ContextualSaveBar: {
                      save: "Save",
                      discard: "Discard",
                    },
                  },
                }}
                linkComponent={Link}
              >
                <I18nContext.Provider value={i18nManager}>
                  <ModalProvider>
                    <ManagedShopifyContext>
                      <Layout pageProps={pageProps} hideFooter={hideFooter}>
                        <Component {...pageProps} key={router.route} />
                        <ManagedModal />
                      </Layout>
                    </ManagedShopifyContext>
                  </ModalProvider>
                </I18nContext.Provider>
              </PolarisAppProvider>
            </PersistGate>
          </ReduxProvider>
        </Hydrate>
      </QueryClientProvider>
    </>
  );
};

const IS_EXTERNAL_LINK_REGEX = /^(?:[a-z][a-z\d+.-]*:|\/\/)/;

function Link({
  children,
  url = "",
  external,
  ref,
  ...rest
}: LinkLikeComponentProps) {
  if (external || IS_EXTERNAL_LINK_REGEX.test(url)) {
    rest.target = "_blank";
    rest.rel = "noopener noreferrer";
    return (
      <a href={url} {...rest}>
        {children}
      </a>
    );
  }

  return (
    <NextLink href={url} {...rest}>
      {children}
    </NextLink>
  );
}

export default CustomApp;
