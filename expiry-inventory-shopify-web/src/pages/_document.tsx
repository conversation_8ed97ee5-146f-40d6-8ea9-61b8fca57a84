import Document, {
    Html,
    Head,
    Main,
    NextScript,
    DocumentContext,
  } from 'next/document';
  
  export default class CustomDocument extends Document {
    static async getInitialProps(ctx: DocumentContext) {
      return await Document.getInitialProps(ctx);
    }
    render() {
      return (
        <Html>
          <Head>
            <link rel="apple-touch-icon" href="/logo192.png" />
            <link rel="manifest" href="/manifest.json" />
            <link
              rel="stylesheet"
              href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,greek,latin-ext,vietnamese"
            />
          </Head>
          <body id="root">
            <Main />
            <NextScript />
          </body>
        </Html>
      );
    }
  }
  