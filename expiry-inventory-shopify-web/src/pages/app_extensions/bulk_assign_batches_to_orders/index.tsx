import type { GetServerSideProps } from "next";
import { Page } from "@shopify/polaris";
import Layout from "@components/layouts/layout";
import OrderListing from "@components/order/order-listing";

function OrderExtensionPage(props: any) {
    return (
        <Page title="Orders">
            <OrderListing {...props} />
        </Page>
    );
}

export default OrderExtensionPage;

OrderExtensionPage.Layout = Layout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};