import type { GetServerSideProps } from "next";
import React from "react";
import OrderDetails from "@components/order/order-details";
import Layout from "@components/layouts/layout";

function OrderExtensionDetailsPage(props: any) {
    return (
        <OrderDetails {...props} />
    );
}

export default OrderExtensionDetailsPage;

OrderExtensionDetailsPage.Layout = Layout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};