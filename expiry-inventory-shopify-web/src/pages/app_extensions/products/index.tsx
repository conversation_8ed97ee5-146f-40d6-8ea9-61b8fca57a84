import type { GetServerSideProps } from "next";
import React from "react";
import PageLayout from "@components/layouts/layout";
import ProductDetails from "@components/product/product-details";

function ProductExtensionPage(props: any) {
    const query = props.query;
    return (
        <ProductDetails product_id={query.id} {...props} />
    );
}

export default ProductExtensionPage;

ProductExtensionPage.Layout = PageLayout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};