import type { GetServerSideProps } from "next";
import React from "react";
import PageLayout from "@components/layouts/layout";
import ProductDetails from "@components/product/product-details";

function VariantExtensionPage(props: any) {
    const query = props.query;
    return (
        <ProductDetails variant_id={query.id} {...props} />
    );
}

export default VariantExtensionPage;

VariantExtensionPage.Layout = PageLayout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};