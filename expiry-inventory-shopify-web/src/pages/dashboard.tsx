import React, { useState } from "react";
import { Page, Button, Layout, ButtonGroup } from "@shopify/polaris";

import AppLayout from "@components/layouts/layout";
import DashboardSkeleton from "@components/ui/loaders/dashboard-skeleton";
import UpgradePlanBanner from "@components/plan/plan-upgrade-banner";
import PopActionList from "@components/common/pop-action-list";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import DiscountApprovalBanner from "@components/discount/discount-approval-banner";
import AnalyticsCharts from "@components/common/analytics-chart";
import AnalyticsProductListCard from "@components/ui/cards/analytics-product-list-card";
import { setMerchantSettings } from "@utils/helpers";
import CustomDayFilterInput from "@components/dashboard/custom-day-filter-input";
import ExportDashboardDataModal from "@components/dashboard/export-dashboard-data-modal";
import ExportDashboardProductsDataModal from "@components/dashboard/export-dashboard-products-data-modal";
import { PLAN_TYPE_ENUM } from "@utils/constants";

function DashboardPage() {
    const status_options = [
        {
            name: "expiring soon",
            value: "expiring_soon",
        },
        {
            name: "past best before",
            value: "best_before",
        },
        {
            name: "upcoming best before",
            value: "upcoming_best_before",
        },
        {
            name: "expired",
            value: "expired",
        },
    ];
    const days = [7, 14, 30, 60, 90, "custom"];
    const [selected_status, setSelectedStatus] = useState(status_options[0]);
    const [customDay, setCustomDay] = useState<string | number>(30);
    const [selected_days, setSelectedDays] = useState<string | number>(30);
    const {
        data: settings,
        isLoading
    } = useSettingsQuery();
    const [export_dashboard_modal_active, setExportDashboardModalActive] = useState(false);
    const [export_dashboard_products_modal_active, setExportDashboardProductsModalActive] = useState(false);

    function formDayOptionString(value: string | number) {
        if (value === "custom") {
            return value as string;
        }
        if(selected_status.value == "expired" || selected_status.value == "best_before") {
            return `Last ${value} days`;
        }
        else if(selected_status.value == "expiring_soon" || selected_status.value == "upcoming_best_before") {
            return `Next ${value} days`;
        }
        return value as string;
    }

    function handleSelectDay(selected_day: any) {
        if (selected_day.value === "custom") {
            setCustomDay(settings?.custom_dashboard_day_filter ?? 30);
        }
        else {
            setCustomDay(selected_day.value);
        }
        setSelectedDays(selected_day.value);
    }

    function handleSaveDayFilter(value: number) {
        setCustomDay(value);
        setMerchantSettings({ custom_dashboard_day_filter: value }, settings);
    }

    function handleExportAnalyticsFigure() {
        setExportDashboardModalActive(!export_dashboard_modal_active);
    }

    function handleExportAnalyticsProductsFigure() {
        setExportDashboardProductsModalActive(!export_dashboard_products_modal_active);
    }

    function handleCloseExportDashboardModal() {
        setExportDashboardModalActive(false);
    }

    function handleCloseExportDashboardProductsModal() {
        setExportDashboardProductsModalActive(false);
    }

    return (
        <>
            {(isLoading || !settings) ? (
                <DashboardSkeleton />
            ) : (
                <Page title="Dashboard">
                    <Layout sectioned={false}>
                        <Layout.Section>
                            {(settings && settings.plan == PLAN_TYPE_ENUM.FREE)
                                ? <UpgradePlanBanner />
                                : <DiscountApprovalBanner />
                            }
                        </Layout.Section>
                        <Layout.Section>
                            <ButtonGroup segmented>
                                {
                                    status_options.map((option, index) => {
                                        return (
                                            <Button key={index} pressed={selected_status.value == option.value} primary onClick={() => setSelectedStatus(option)}>
                                                {/* @ts-ignore */}
                                                <span className="capitalize">{option.name}</span>
                                            </Button>
                                        );
                                    })
                                }
                            </ButtonGroup>
                        </Layout.Section>
                        <Layout.Section oneThird>
                            <div className="flex items-center justify-end gap-2">
                                <Button primary onClick={handleExportAnalyticsFigure}>
                                    Export
                                </Button>
                                <div>
                                    <PopActionList
                                        options={days.map((day) => ({ name: formDayOptionString(day), value: day }))}
                                        contentName="name"
                                        data_display={formDayOptionString(selected_days)}
                                        setData={handleSelectDay}
                                    />
                                </div>
                                {selected_days == "custom" ? (
                                    <CustomDayFilterInput
                                        current_day={customDay}
                                        onSave={handleSaveDayFilter}
                                    />
                                ) : <></>}
                            </div>
                        </Layout.Section>
                        <Layout.Section>
                            <AnalyticsCharts
                                status={selected_status}
                                days={(selected_days == "custom" ? customDay : selected_days) as number}
                                currency={settings.shop ? settings.shop.currency : "USD"}
                            />
                            <div className="flex gap-6 mt-6">
                                <div className={`w-full ${(selected_status.value == "expiring_soon" || selected_status.value == "best_before" || selected_status.value == "upcoming_best_before") ? "sm:w-1/2" : ""}`}>
                                    <AnalyticsProductListCard
                                        status={selected_status}
                                        days={(selected_days == "custom" ? customDay : selected_days) as number}
                                        currency={settings.shop ? settings.shop.currency : "USD"}
                                        onExport={handleExportAnalyticsProductsFigure}
                                    />
                                </div>
                                {(selected_status.value == "expiring_soon" || selected_status.value == "best_before" || selected_status.value == "upcoming_best_before") ? (
                                    <div className="w-full sm:w-1/2">
                                        <AnalyticsProductListCard
                                            status={{
                                                name: "discounted",
                                                value: "discounted",
                                            }}
                                            days={(selected_days == "custom" ? customDay : selected_days) as number}
                                            currency={settings.shop ? settings.shop.currency : "USD"}
                                        />
                                    </div>
                                ) : <></>}
                            </div>
                        </Layout.Section>
                    </Layout>
                    <ExportDashboardDataModal
                        active={export_dashboard_modal_active}
                        type={selected_status.value}
                        days={(selected_days == "custom" ? customDay : selected_days) as number}
                        onDismiss={handleCloseExportDashboardModal}
                    ></ExportDashboardDataModal>
                    <ExportDashboardProductsDataModal
                        active={export_dashboard_products_modal_active}
                        type={selected_status.value}
                        days={(selected_days == "custom" ? customDay : selected_days) as number}
                        onDismiss={handleCloseExportDashboardProductsModal}
                    ></ExportDashboardProductsDataModal>
                </Page>
            )}
        </>
    );
}

export default DashboardPage;

DashboardPage.Layout = AppLayout;