import React from "react";
import { Page, DataTable } from "@shopify/polaris";
import EmptyDataTable from "@components/ui/loaders/empty-data-table";
import NoResult from "@components/common/no-result";
import Layout from "@components/layouts/layout";
import { ShopifyCard } from "@components/ui/shopify/card";
import { ROUTES } from "@utils/routes";
import { usePendingDiscountsQuery } from "@data/discount/use-pending-discounts.query";
import DiscountActivationActionButton from "@components/discount/discount-activation-action-button";
import { useI18n } from "@shopify/react-i18n";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import PageLoading from "@components/common/page-loading";

function DiscountsPage() {
    const [i18n] = useI18n();
    const { data: settings, isLoading: settingsLoading } = useSettingsQuery();
    const { data, isLoading, isRefetching, refetch } = usePendingDiscountsQuery();

    if(settingsLoading || !settings) return <PageLoading />;

    const { shop: { currency } } = settings;

    return (
        <Page
            breadcrumbs={[{content: "Dashboard", url: ROUTES.DASHBOARD}]}
            title={`Pending Discounts`}
        >
            <p className="mb-4">
                <span>Showing discounts pending for activation</span>
            </p>
            <ShopifyCard>
                <div>
                    {
                        (isLoading || isRefetching || !data) ? <EmptyDataTable /> : <></>
                    }
                    {
                        (!isLoading && !isRefetching && data && data.length <= 0) ? <NoResult /> : <></>
                    }
                    {
                        (!isLoading && !isRefetching && data && data.length > 0) ?
                            <DataTable
                                hideScrollIndicator
                                columnContentTypes={[
                                    "text",
                                    "numeric",
                                    "numeric",
                                    "numeric",
                                    "text",
                                    "numeric",
                                ]}
                                headings={[
                                    "Product",
                                    "Price",
                                    "Discounted price",
                                    "Quantity",
                                    "Type",
                                    "Actions",
                                ]}
                                rows={
                                    data.map((row) => {
                                        return [
                                            row.product.name,
                                            <del>{i18n.formatCurrency(row.product.price, { currency, form: 'short' })}</del>,
                                            i18n.formatCurrency(row.discounted_price, { currency, form: 'short' }),
                                            row.batch_item?.quantity ?? "-",
                                            row.type === "expiry" ? "Expiry" : "Slow moving",
                                            <div className="flex justify-end gap-2">
                                                <DiscountActivationActionButton discount_id={row.id} type="approve" onAction={refetch} />
                                                <DiscountActivationActionButton discount_id={row.id} type="reject" onAction={refetch} />
                                            </div>
                                        ];
                                    })
                                }
                                // footerContent={<p>Showing <b>{paginationData.from} - {paginationData.to}</b> of <b>{paginationData.total}</b> discounts</p>}
                            />
                        : <></>
                    }
                </div>
            </ShopifyCard>
            {/* <div id="discount-paginator">
                <Pagination
                    hasPrevious={1 < page}
                    onPrevious={() => {
                        setPage(page - 1);
                    }}
                    hasNext={page < paginationData.last_page}
                    onNext={() => {
                        setPage(page + 1);
                    }}
                />
            </div> */}
        </Page>
    );
}

export default DiscountsPage;

DiscountsPage.Layout = Layout;