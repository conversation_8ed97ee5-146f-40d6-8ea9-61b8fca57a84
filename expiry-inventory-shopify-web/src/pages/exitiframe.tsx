import { Redirect } from "@shopify/app-bridge/actions";
import { useAppBridge, Loading } from "@shopify/app-bridge-react";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import AppLayout from "@components/layouts/layout";
import { Layout, Banner, Page } from "@shopify/polaris";

Exitiframe.Layout = AppLayout;

export default function Exitiframe() {
    const app = useAppBridge();
    const { query } = useRouter();
    const [showWarning, setShowWarning] = useState(false);

    useEffect(() => {
        if (!!app && !!query) {
            const redirectUri = query.redirectUri;
            const url = new URL(decodeURIComponent(redirectUri as string));

            if (
                [location.hostname, "admin.shopify.com"].includes(url.hostname) ||
                url.hostname.endsWith(".myshopify.com")
            ) {
                const redirect = Redirect.create(app);
                redirect.dispatch(
                    Redirect.Action.REMOTE,
                    decodeURIComponent(redirectUri as string)
                );
            }
            else {
                setShowWarning(true);
            }
        }
    }, [app, query]);

    return showWarning ? (
        <Page narrowWidth>
          <Layout>
            <Layout.Section>
              <div style={{ marginTop: "100px" }}>
                <Banner title="Redirecting outside of Shopify" status="warning">
                  Apps can only use /exitiframe to reach Shopify or the app itself.
                </Banner>
              </div>
            </Layout.Section>
          </Layout>
        </Page>
    ) : (
        <Loading />
    );
}
