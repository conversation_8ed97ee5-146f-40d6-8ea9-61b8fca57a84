import React, { useEffect, useState } from "react";
import { Page } from "@shopify/polaris";
import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import Layout from "@components/layouts/layout";
import { GetServerSideProps } from "next/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { InventoryHistory, InventoryHistoryResponse, Product } from "@ts-types/types";
import LocationPicker from "@components/common/location-picker";
import { useUI } from "@contexts/ui.context";
import { ROUTES } from "@utils/routes";
import InventoryHistoryTable from "@components/inventory-batch/inventory-history-table";

function InventoryHistoryPage({ query }: any) {
    const { product_id } = query;
    const { location: selected_location } = useUI();
    const [is_loading, setIsLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [paginationData, setPaginationData] = useState<any>({last_page: 1});
    const [history_rows, setHistoryRows] = useState<Array<InventoryHistory>>([]);
    const [product_details, setProductDetails] = useState<Product | null>(null);

    useEffect(() => {
        getInventoryHistories();
    }, [selected_location, page]);

    function getInventoryHistories() {
        const searchPayload = {
            page,
            ...(product_id ? { product: product_id[0] } : {}),
            ...(selected_location ? { location: String(selected_location.id) } : {}),
        };
        setIsLoading(true);
        fetchAPI("GET", API_ENDPOINTS.INVENTORY_BATCH_HISTORY, searchPayload)
            .then((response) => {
                const history_data = response as InventoryHistoryResponse;
                if(history_data.product) {
                    setProductDetails(history_data.product);
                }
                setPaginationData({
                    from: history_data.history.from,
                    to: history_data.history.to,
                    total: history_data.history.total,
                    last_page: history_data.history.last_page
                })
                setHistoryRows(history_data.history.data);
            })
            .catch((error) => {
                showToast(toastOptions.BATCH_HISTORY_GET_FAILED);
            })
            .finally(() => {
                setTimeout(() => {
                    setIsLoading(false);
                }, 2000);
            });
    }

    return (
        <Page
            breadcrumbs={[{content: "Products", url: ROUTES.PRODUCTS}]}
            title={`Inventory batch history`}
            primaryAction={<LocationPicker />}
        >
            {product_details ? (
                <p className="mb-4">
                    <span>Showing inventory batch history for </span>
                    <span className="font-semibold">{product_details.display_name}</span>
                </p>
            ) : <></>}
            <InventoryHistoryTable
                isLoading={is_loading}
                data={history_rows}
                currentPage={page}
                paginationData={paginationData}
                onPageChange={setPage}
                onReload={getInventoryHistories}
            />
        </Page>
    );
}

export default InventoryHistoryPage;

InventoryHistoryPage.Layout = Layout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};