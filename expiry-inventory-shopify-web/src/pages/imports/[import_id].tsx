import React, { useEffect, useState } from "react";
import { Page } from "@shopify/polaris";
import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import Layout from "@components/layouts/layout";
import { GetServerSideProps } from "next/types";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { ImportHistory, InventoryHistory, InventoryHistoryResponse } from "@ts-types/types";
import { useUI } from "@contexts/ui.context";
import { ROUTES } from "@utils/routes";
import InventoryHistoryTable from "@components/inventory-batch/inventory-history-table";
import moment from "moment";

function ImportHistoryDetailsPage({ query }: any) {
    const { import_id } = query;
    const { location: selected_location } = useUI();
    const [is_loading, setIsLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [paginationData, setPaginationData] = useState<any>({last_page: 1});
    const [history_rows, setHistoryRows] = useState<Array<InventoryHistory>>([]);
    const [import_history_details, setImportHistoryDetails] = useState<ImportHistory | null>(null);

    useEffect(() => {
        getInventoryHistories();
    }, [selected_location, page]);

    function getInventoryHistories() {
        const searchPayload = {
            page,
            ...(import_id ? { import_history: import_id[0] } : {}),
        };
        setIsLoading(true);
        fetchAPI("GET", API_ENDPOINTS.INVENTORY_BATCH_HISTORY, searchPayload)
            .then((response) => {
                const history_data = response as InventoryHistoryResponse;
                if(history_data.import) {
                    setImportHistoryDetails(history_data.import);
                }
                setPaginationData({
                    from: history_data.history.from,
                    to: history_data.history.to,
                    total: history_data.history.total,
                    last_page: history_data.history.last_page
                })
                setHistoryRows(history_data.history.data);
            })
            .catch((error) => {
                showToast(toastOptions.BATCH_HISTORY_GET_FAILED);
            })
            .finally(() => {
                setTimeout(() => {
                    setIsLoading(false);
                }, 2000);
            });
    }
    return (
        <Page
            breadcrumbs={[{content: "Import History", url: ROUTES.IMPORTS}]}
            title={`Import history`}
        >
            {import_history_details ? (
                <p className="mb-4">
                    <span>Showing inventory batch changes for the import on  </span>
                    <span className="font-semibold">{moment(import_history_details.created_at).format("DD/MM/YYYY hh:mm A")}</span>
                </p>
            ) : <></>}
            <InventoryHistoryTable
                isLoading={is_loading}
                data={history_rows}
                currentPage={page}
                paginationData={paginationData}
                onPageChange={setPage}
                onReload={getInventoryHistories}
            />
        </Page>
    );
}

export default ImportHistoryDetailsPage;

ImportHistoryDetailsPage.Layout = Layout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};