import { useState } from "react";
import { useRouter } from "next/router";
import Layout from "@components/layouts/layout";
import { ShopifyCard } from "@components/ui/shopify/card";
import { useImportHistoriesQuery } from "@data/import/use-imports.query";
import { Badge, DataTable, Page, Pagination } from "@shopify/polaris";
import EmptyDataTable from "@components/ui/loaders/empty-data-table";
import NoResult from "@components/common/no-result";
import moment from "moment";
import { ROUTES } from "@utils/routes";
import { Status } from "@shopify/polaris/build/ts/latest/src/components/Badge";

const statusColors: Array<{key: string; value: Status}> = [
    {
        key: "success",
        value: "success"
    },
    {
        key: "pending",
        value: "attention"
    },
    {
        key: "failed",
        value: "critical"
    },
];

function getStatusColor(status: string) {
    return statusColors.find(({ value }) => value == status)?.value!;
}

function ImportHistoryPage() {
    const [page, setPage] = useState(1);
    const [orderBy, setOrderBy] = useState({
        order_by: "id",
        order_direction: "DESC"
    })
    const router = useRouter();
    const { data: importsData, isLoading } = useImportHistoriesQuery({
        query: {
            page,
            ...orderBy
        }
    });
    function handleViewImportHistoryDetails(id: string | number) {
        router.push(`${ROUTES.IMPORTS}/${id}`);
    }
    function handleHistorySort(index: number, direction: string) {
        let field = null;
        switch(index) {
            case 0:
                field = "id";
                break;
            case 4:
                field = "created_at";
                break;
            default:
                return;
        }
        setOrderBy({
            order_by: field,
            order_direction: direction == "ascending" ? "ASC" : "DESC"
        });
    }
    return (
        <Page
            breadcrumbs={[{content: "Inventory Batch", url: ROUTES.INVENTORY_BATCHES}]}
            title="Import History"
        >
            <ShopifyCard>
                <div>
                    {
                        isLoading ? <EmptyDataTable /> : <></>
                    }
                    {
                        (!isLoading && (!importsData || importsData.data.length <= 0))
                            ? <NoResult />
                            : <></>
                    }
                    {
                        importsData ? importsData.data.length > 0 ? (
                            <div className={isLoading ? "hidden" : ""}>
                                <DataTable
                                    hideScrollIndicator
                                    columnContentTypes={[
                                        "text",
                                        "text",
                                        "text",
                                        "text",
                                        "text",
                                    ]}
                                    headings={[
                                        "Import ID",
                                        "Filename",
                                        "Rows Count",
                                        "Status",
                                        "Import At"
                                    ]}
                                    rows={
                                        importsData.data.map((import_history) => {
                                            return [
                                                <a className="underline" onClick={() => handleViewImportHistoryDetails(import_history.id)}>{import_history.id}</a>,
                                                import_history.path,
                                                import_history.rows_count,
                                                (
                                                    <Badge status={getStatusColor(import_history.status)}>
                                                        {import_history.status}
                                                    </Badge>
                                                ),
                                                moment(import_history.created_at).format("DD/MM/YYYY hh:mm A")
                                            ];
                                        })
                                    }
                                    sortable={[true, false, false, false, true]}
                                    defaultSortDirection="descending"
                                    onSort={handleHistorySort}
                                />
                            </div>
                        ) : <></>
                    : <></>}
                </div>
            </ShopifyCard>
            <div id="import-history-paginator">
                <Pagination
                    hasPrevious={1 < page}
                    onPrevious={() => {
                        setPage(page - 1);
                    }}
                    hasNext={importsData ? page < importsData.last_page : false}
                    onNext={() => {
                        setPage(page + 1);
                    }}
                />
            </div>
        </Page>
    )
}

export default ImportHistoryPage;

ImportHistoryPage.Layout = Layout;