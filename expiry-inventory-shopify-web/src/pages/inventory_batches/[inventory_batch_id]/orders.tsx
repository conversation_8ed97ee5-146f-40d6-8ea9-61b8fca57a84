import type { GetServerSideProps } from "next";
import React, { useEffect, useState } from "react";
import { Page, Link, DataTable, Badge } from "@shopify/polaris";
import { fetchAPI, formatDate, showToast, sumArray, toastOptions } from "@utils/helpers";
import EmptyDataTable from "@components/ui/loaders/empty-data-table";
import NoResult from "@components/common/no-result";
import PageLoading from "@components/common/page-loading";
import Layout from "@components/layouts/layout";
import { ROUTES } from "@utils/routes";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { InventoryBatch, InventoryBatchOrdersResponse, Order, Settings } from "@ts-types/types";
import { ShopifyCard } from "@components/ui/shopify/card";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { ShopifyText } from "@components/ui/shopify/text";
import { PLAN_TYPE_ENUM } from "@utils/constants";

function InventoryBatchOrderPage(props: any) {
    const { inventory_batch_id } = props.query;
    const [is_loading, setIsLoading] = useState(true);
    const [page, setPage] = useState(1);
    // const [total_pages, setTotalPage] = useState(1);
    const [batch_details, setBatchDetails] = useState<InventoryBatch | null>(null);
    const [order_rows, setOrderRows] = useState<Array<Order>>([]);
    const app = useAppBridge();
    const {
        data: settings,
        isLoading,
    } = useSettingsQuery();

    useEffect(() => {
        getInventoryBatchesData();
    }, [page]);

    function getInventoryBatchesData() {
        setIsLoading(true);
        fetchAPI("GET", `${API_ENDPOINTS.INVENTORY_BATCH}/${inventory_batch_id}/orders`)
            .then((response) => {
                setBatchDetails((response as InventoryBatchOrdersResponse).inventory_batch);
                setOrderRows((response as InventoryBatchOrdersResponse).orders);
            })
            .catch((error) => {
                showToast(toastOptions.BATCH_ORDERS_GET_FAILED);
            })
            .finally(() => {
                setTimeout(() => {
                    setIsLoading(false);
                }, 2000);
            });
    }

    function renderOrderLink(order: Order) {
        return (<Link url={`${ROUTES.ORDERS}/${order.id}`} removeUnderline>Order #{order.order_number}</Link>);
    }

    function viewPlans() {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.PLANS);
    }

    if(!settings) {
        return <PageLoading />;
    }

    if(settings.plan == PLAN_TYPE_ENUM.FREE) {
        viewPlans();
        return <PageLoading />;
    }

    if(is_loading) {
        return <EmptyDataTable showPage={true} />;
    }

    return (
        <Page
            breadcrumbs={[{content: "Inventory Batch", url: ROUTES.INVENTORY_BATCHES}]}
            title={batch_details ? (batch_details.name ?? `Batch #${batch_details.id}`) : "Inventory Batch"}
        >
            <ShopifyCard>
                <div>
                    {
                        !is_loading && (!order_rows || order_rows.length <= 0) ? <NoResult /> : <></>
                    }
                    {
                        (!is_loading && order_rows.length > 0) ?
                            <DataTable
                                hideScrollIndicator
                                columnContentTypes={[
                                    "text",
                                    "text",
                                    "text",
                                    "text",
                                    "numeric",
                                ]}
                                headings={[
                                    "Order",
                                    "Date",
                                    "Customer",
                                    "Fulfillment",
                                    "Quantity",
                                ]}
                                rows={
                                    order_rows.map((order) => {
                                        return [
                                            renderOrderLink(order),
                                            order.created_at ? formatDate(order.created_at) : "—",
                                            <div>
                                                <ShopifyText as="p" variant="bodyMd" fontWeight="semibold">
                                                    {order.customer && order.customer ? order.customer.name : (order.billing_address ? order.billing_address.name : "")}
                                                </ShopifyText>
                                                {order.customer && order.customer.email ? <p>{order.customer.email}</p> : <></>}
                                                {order.customer && order.customer.phone ? <p>{order.customer.phone}</p> : <></>}
                                            </div>,
                                            <div className="capitalize"><Badge status={order.fulfillment_status == "fulfilled" ? "success" : "attention"}>{order.fulfillment_status}</Badge></div>,
                                            sumArray(order.items, "quantity"),
                                        ];
                                    })
                                }
                            />
                        : <></>
                    }
                </div>
            </ShopifyCard>
            {/* <div id="inventory-batch-orders-paginator">
                <Pagination
                    hasPrevious={1 < page}
                    onPrevious={() => {
                        setPage(page - 1);
                    }}
                    hasNext={page < total_pages}
                    onNext={() => {
                        setPage(page + 1);
                    }}
                />
            </div> */}
        </Page>
    );
}

export default InventoryBatchOrderPage;

InventoryBatchOrderPage.Layout = Layout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};