import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Link,
  Button,
  DataTable,
  Badge,
  Pagination,
} from "@shopify/polaris";
import { ExportMinor, ImportMinor, TransferWithinShopifyMajor } from "@shopify/polaris-icons";
import TabOptions from "@components/common/tab-options";
import LocationPicker from "@components/common/location-picker";
import { fetchAPI, formatDate, showToast, toastOptions } from "@utils/helpers";
import EmptyDataTable from "@components/ui/loaders/empty-data-table";
import ExportBatchModal from "@components/inventory-batch/export-batch-modal";
import NoResult from "@components/common/no-result";
import ImportBatchModal from "@components/inventory-batch/import-batch-modal";
import TransfersModal from "@components/inventory-batch/transfers-modal";
import Search from "@components/common/search";
import Layout from "@components/layouts/layout";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { Status } from "@shopify/polaris/build/ts/latest/src/components/Badge";
import {
  BatchItemDate,
  InventoryBatch,
  InventoryBatchPaginationData,
  Product,
  SortFilterOptions,
} from "@ts-types/types";
import { useUI } from "@contexts/ui.context";
import { ShopifyCard } from "@components/ui/shopify/card";
import { ROUTES } from "@utils/routes";
import { useModalAction } from "@components/ui/modal/modal.context";

function InventoryBatchPage(props: any) {
  const { location: selected_location } = useUI();
  const { openModal, closeModal } = useModalAction();
  const [is_loading, setIsLoading] = useState(true);
  const [selected_tab, setSelectedTab] = useState(0);
  const [page, setPage] = useState(1);
  const [paginationData, setPaginationData] = useState<any>({last_page: 1});
  const [batch_search, setBatchSearch] = useState("");
  const [batch_rows, setBatchRows] = useState<Array<InventoryBatch>>([]);
  const [export_modal_active, setExportModalActive] = useState(false);
  const [import_modal_active, setImportModalActive] = useState(false);
  const [transfers_modal_active, setTransfersModalActive] = useState(false);

  const tabOptions = [
    {
      key: "",
      label: "All",
    },
    {
      key: "in_stock",
      label: "In stock",
    },
    {
      key: "discounted",
      label: "Discounted",
    },
    {
      key: "out_of_stock",
      label: "Out of stock",
    },
    {
      key: "past_best_before",
      label: "Past Best Before",
    },
    {
      key: "upcoming_best_before",
      label: "Upcoming Best Before",
    },
    {
      key: "expired",
      label: "Expired",
    },
    {
      key: "unverified",
      label: "Unverified",
    },
  ];
  const statusColors: Array<{ key: string; value: Status }> = [
    {
      key: "in_stock",
      value: "success",
    },
    {
      key: "discounted",
      value: "warning",
    },
    {
      key: "out_of_stock",
      value: "warning",
    },
    {
      key: "expired",
      value: "critical",
    },
  ];

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      getInventoryBatchesData();
    }, 100);
    return () => clearTimeout(delayDebounceFn);
  }, [selected_location, selected_tab, page]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      getInventoryBatchesData();
    }, 1000);
    return () => clearTimeout(delayDebounceFn);
  }, [batch_search]);

  function getInventoryBatchesData(options: SortFilterOptions | null = null) {
    let location_id = selected_location ? String(selected_location.id) : "";
    let filter = tabOptions[selected_tab].key;
    const searchPayload = {
      location: location_id,
      filter,
      search: batch_search,
      page,
      order_by:
        options && options.sort_field ? options.sort_field : "created_at",
      order_direction:
        options && options.sort_direction ? options.sort_direction : "DESC",
    };
    setIsLoading(true);
    fetchAPI("GET", API_ENDPOINTS.INVENTORY_BATCH, searchPayload)
      .then((response) => {
        const batch_data = response as InventoryBatchPaginationData;
        setPaginationData({
          from: batch_data.from,
          to: batch_data.to,
          total: batch_data.total,
          last_page: batch_data.last_page
        })
        setBatchRows(batch_data.data);
      })
      .catch((error) => {
        showToast(toastOptions.BATCH_NOT_FOUND);
      })
      .finally(() => {
        setTimeout(() => {
          setIsLoading(false);
        }, 2000);
      });
  }

  function handleSelectTab(option: number) {
    setPage(1);
    setSelectedTab(option);
  }

  function handleBatchSort(index: number, direction: string) {
    let field = null;
    switch (index) {
      case 0:
        field = "id";
        break;
      case 1:
        field = "name";
        break;
      case 2:
        field = "lot_number";
        break;
      case 3:
        field = "bin_location";
        break;
      case 5:
        field = "status";
        break;
      case 6:
        field = "expiry_date";
        break;
      case 7:
        field = "last_verified_at";
        break;
      case 8:
        field = "best_before";
        break;
      default:
        return;
    }
    getInventoryBatchesData({
      sort_field: field,
      sort_direction: direction == "ascending" ? "ASC" : "DESC",
    });
  }

  function handleBatchModal(batch_id: string | number) {
    openModal(
      "EDIT_INVENTORY_BATCH_VIEW",
      { batch_id },
      {
        modalTitle: "Edit inventory batch",
        onDismiss: handleBatchModalClosed,
        hideCancelBtn: true,
      }
    );
  }

  function handleBatchModalClosed(status = false) {
    if (status === true) {
      getInventoryBatchesData();
    }
    closeModal();
  }

  function toggleExportModal() {
    setExportModalActive(!export_modal_active);
  }

  function toggleImportModal() {
    setImportModalActive(!import_modal_active);
  }

  function toggleTransfersModal() {
    setTransfersModalActive(!transfers_modal_active);
  }

  function handleImportModalClosed(status = false) {
    if (status === true) {
      getInventoryBatchesData();
    }
    toggleImportModal();
  }

  function renderProductLink(product: Product) {
    return (
      <Link
        url={`${ROUTES.PRODUCTS}/${product.parent_id}/${product.shopify_variant_id}`}
        removeUnderline
      >
        {product.parent_name}
        {product.name != product.parent_name ? ` (${product.name})` : ""}
      </Link>
    );
  }

  function renderBatchItemBestBeforeDate(dates: Array<BatchItemDate> | null) {
    if (!dates || !dates.length) return "—";
    return dates.map(({ date }, index) => (
      <p key={index}>{formatDate(date)}</p>
    ));
  }

  function renderBatchOrderLink(batch: InventoryBatch) {
    return (
      <Button url={`${ROUTES.INVENTORY_BATCHES}/${batch.id}/orders`}>
        View {`${batch.order_count}`} orders
      </Button>
    );
  }

  function renderBadgeStatus(batch: InventoryBatch) {
    const status_color =
      statusColors.find((option) => option.key === batch.status)?.value ??
      "success";
    const status_label =
      tabOptions.find((option) => option.key === batch.status)?.label ??
      batch.status;
    return <Badge status={status_color}>{status_label}</Badge>;
  }

  return (
    <Page title="Inventory Batch">
      <div className="flex mb-5">
        <div className="mr-4">
          <Button icon={ExportMinor} onClick={toggleExportModal}>
            Export
          </Button>
        </div>
        <div className="mr-4">
          <Button icon={ImportMinor} onClick={toggleImportModal}>
            Import
          </Button>
        </div>
        <div>
          <Button icon={TransferWithinShopifyMajor} onClick={toggleTransfersModal}>
            View Transfers
          </Button>
        </div>
      </div>
      <ShopifyCard>
        <div>
          <div className="flex items-stretch border-b">
            <TabOptions
              options={tabOptions}
              selected={selected_tab}
              onChange={handleSelectTab}
            />
            <div className="self-center ml-auto mr-4">
              <LocationPicker />
            </div>
          </div>
          <div className="Polaris-Connected">
            <div
              className="Polaris-Connected__Item Polaris-Connected__Item--primary"
              style={{ padding: "10px 15px" }}
            >
              <Search
                value={batch_search}
                onChange={setBatchSearch}
                placeholder="Search batch details"
                showClear={true}
              />
            </div>
          </div>
          {is_loading ? <EmptyDataTable /> : <></>}
          {!is_loading && (!batch_rows || batch_rows.length <= 0) ? (
            <NoResult
              is_filtering={selected_tab}
              onResetButtonClicked={() => setSelectedTab(0)}
            />
          ) : (
            <></>
          )}
          {batch_rows.length > 0 ? (
            <div className={is_loading ? "hidden" : ""}>
              <DataTable
                hideScrollIndicator
                columnContentTypes={[
                  "text",
                  "text",
                  "text",
                  "text",
                  "text",
                  "text",
                  "text",
                  "text",
                  "text",
                  "text",
                ]}
                headings={[
                  "Batch ID",
                  "Batch name",
                  "Batch / Lot number",
                  "Bin Location",
                  "Product",
                  "Status",
                  "Expiry date",
                  "Last verified",
                  "Best Before date(s)",
                  "Orders",
                ]}
                rows={batch_rows.map((batch) => {
                  const batch_item = batch.items[0];
                  return [
                    <a
                      className="no-underline"
                      onClick={() => handleBatchModal(batch.id)}
                    >
                      {batch.id}
                    </a>,
                    <a
                      className="no-underline"
                      onClick={() => handleBatchModal(batch.id)}
                    >
                      {batch.name ?? `Batch #${batch.id}`}
                    </a>,
                    batch.lot_number,
                    batch.bin_location,
                    batch_item?.product
                      ? renderProductLink(batch_item.product)
                      : "",
                    renderBadgeStatus(batch),
                    batch_item?.expire_at
                      ? formatDate(batch_item.expire_at)
                      : "—",
                    batch.last_verified_at ? formatDate(batch.last_verified_at) : "—",
                    renderBatchItemBestBeforeDate(batch_item?.dates),
                    renderBatchOrderLink(batch),
                  ];
                })}
                sortable={[
                  true,
                  true,
                  true,
                  true,
                  false,
                  true,
                  true,
                  true,
                  true,
                  false,
                ]}
                defaultSortDirection="descending"
                onSort={handleBatchSort}
                footerContent={`Showing ${paginationData.from} - ${paginationData.to} of ${paginationData.total} results`}
              />
            </div>
          ) : (
            <></>
          )}
          <ExportBatchModal
            active={export_modal_active}
            onDismiss={toggleExportModal}
          ></ExportBatchModal>
          <ImportBatchModal
            active={import_modal_active}
            onDismiss={handleImportModalClosed}
          ></ImportBatchModal>
          <TransfersModal
            active={transfers_modal_active}
            onDismiss={toggleTransfersModal}
          />
        </div>
      </ShopifyCard>
      <div id="inventory-batch-paginator">
        <Pagination
          hasPrevious={1 < page}
          onPrevious={() => {
            setPage(page - 1);
          }}
          hasNext={page < paginationData.last_page}
          onNext={() => {
            setPage(page + 1);
          }}
        />
      </div>
    </Page>
  );
}

export default InventoryBatchPage;

InventoryBatchPage.Layout = Layout;
