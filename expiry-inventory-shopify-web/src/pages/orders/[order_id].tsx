import React from "react";
import type { GetServerSideProps } from "next";
import OrderDetails from "@components/order/order-details";
import Layout from "@components/layouts/layout";

function OrderDetailsPage(props: any) {
    return (
        <OrderDetails {...props} />
    );
}

export default OrderDetailsPage;

OrderDetailsPage.Layout = Layout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};