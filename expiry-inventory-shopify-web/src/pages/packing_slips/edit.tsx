import { But<PERSON>, Layout, Page } from "@shopify/polaris";
import { PrintMinor, RefreshMinor } from "@shopify/polaris-icons";
import { useEffect, useState } from "react";
import { fetchAPI, fetchAPIWithShopURL, showToast, toastOptions } from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import PageLoading from "@components/common/page-loading";
import dynamic from "next/dynamic";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import PageLayout from "@components/layouts/layout";
import { ShopifyCard } from "@components/ui/shopify/card";
import { useShopify } from "@contexts/shopify.context";

const CodeEditor = dynamic(
  () => import("@uiw/react-textarea-code-editor").then((mod) => mod.default),
  { ssr: false }
);

export default function OrderPackingSlipTemplatePage() {
    const { shop, host } = useShopify();
    const [current_template, setCurrentTemplate] = useState<string | null>(null);
    const [default_template, setDefaultTemplate] = useState<string | null>(null);

    useEffect(() => {
        if(shop) {
            getPackingSlipTemplate(shop);
        }
    }, [shop]);

    function getPackingSlipTemplate(shop: string) {
        fetchAPIWithShopURL("GET", API_ENDPOINTS.PREFERENCE_PRINT, shop)
            .then((response) => {
                setCurrentTemplate(response as string);
            });
    }
    
    function saveTemplate() {
        fetchAPI("PUT", API_ENDPOINTS.PREFERENCE_PRINT, {liquid: current_template})
            .then(() => {
                showToast(toastOptions.PACKING_SLIP_SAVED);
            })
            .catch(() => {
                showToast(toastOptions.PACKING_SLIP_NOT_SAVED);
            });
    }

    function resetDefault() {
        // eslint-disable-next-line no-restricted-globals
        if(confirm("Are you sure to reset to default template?")) {
            if(default_template) {
                setCurrentTemplate(default_template);
                showToast(toastOptions.PACKING_SLIP_RESET);
            }
            else {
                fetchAPI("GET", API_ENDPOINTS.ORDER_PRINT)
                    .then((response_data) => {
                        setDefaultTemplate(response_data as string);
                        setCurrentTemplate(response_data as string);
                        showToast(toastOptions.PACKING_SLIP_RESET);
                    });
            }
        }
    }

    if(!shop || !current_template) {
        return <PageLoading />;
    }

    return (
        <Page
            breadcrumbs={[{content: "Settings", url: ROUTES.SETTINGS}]}
            title="Edit packing slip template"
            primaryAction={<Button primary onClick={() => saveTemplate()}>Save template</Button>}
        >
            <div className="flex mb-5">
                <div className="mr-4">
                    <Button onClick={resetDefault} icon={RefreshMinor} destructive>Reset to default template</Button>
                </div>
                <Button url={`${ROUTES.PACKING_SLIP_PREVIEW}?shop=${shop}&host=${host}`} icon={PrintMinor} external>Preview packing slip</Button>
            </div>
            <div className="mt-4">
                <Layout>
                    <Layout.Section>
                        <ShopifyCard>
                            <CodeEditor
                                value={current_template}
                                language="html"
                                onChange={(e) => setCurrentTemplate(e.target.value)}
                                padding={15}
                                style={{
                                    fontSize: 12,
                                    backgroundColor: "#f5f5f5",
                                    fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace',
                                }}
                            />
                        </ShopifyCard>
                    </Layout.Section>
                </Layout>
            </div>
        </Page>
    );
}

OrderPackingSlipTemplatePage.Layout = PageLayout;