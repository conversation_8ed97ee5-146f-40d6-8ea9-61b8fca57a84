import type { GetServerSideProps } from "next";
import React, { useEffect, useState } from "react";
import { PDFExport } from "@progress/kendo-react-pdf";
import OrderPackingSlip from "@components/order/order-packing-slip";

function OrderPackingSlipPDFPage(props: any) {
    const { order_id, items } = props.query;
    const [is_rendered, setIsRendered] = useState(false);
    const [pdf_preview, setPdfPreview] = useState<PDFExport | null>(null);

    useEffect(() => {
        downloadPDF();
    }, [pdf_preview, is_rendered]);

    function downloadPDF() {
        if(pdf_preview && is_rendered) {
            pdf_preview.save();
        }
    }

    return (
        <>
            <div className="overlay text-center pt-4">
                <button className="download-button" onClick={downloadPDF}>Download PDF</button>
                <p>Your PDF should be downloaded automatically...If it doesn"t download, click the button above to download it manually</p>
            </div>
            <PDFExport
                paperSize="A4"
                fileName="packing_slip.pdf"
                title="Packing slip"
                ref={(r) => setPdfPreview(r)}
            >
                    <div className="print-layout">
                        <OrderPackingSlip order_id={order_id} items={items} onRenderCompleted={() => setIsRendered(true)} />
                    </div>
            </PDFExport>
        </>
    );
}

export default OrderPackingSlipPDFPage;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};