import type { GetServerSideProps } from "next";
import OrderPackingSlip from "@components/order/order-packing-slip";

function OrderPackingSlipPage(props: any) {
    const { order_id, items } = props.query;
    return <OrderPackingSlip order_id={order_id} items={items} />;
}

export default OrderPackingSlipPage;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};