import React, { useEffect, useState } from "react";
import { NextSeo } from "next-seo";
import ChecklistItem from "@components/common/checklist-item";
import { fetchAPI } from "@utils/helpers";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { PlansResponse } from "@ts-types/types";
import cn from "classnames";
import PageLoading from "@components/common/page-loading";

export default function PlanListingPage() {
    const [plans, setPlans] = useState<PlansResponse | null>(null);
    const [billing_period, setBillingPeriod] = useState("month");

    function retrievePlans() {
        fetchAPI("GET", API_ENDPOINTS.PLAN, {}, false)
            .then((plan_settings) => {
                setPlans(plan_settings as PlansResponse);
            });
    }

    useEffect(() => {
        retrievePlans();
    }, []);

    function formatAmountValue(value: number) {
        return Intl.NumberFormat().format(value);
    }

    if(!plans) {
        return (
            <PageLoading />
        );
    }
    
    return (
        <>
            <NextSeo
                title="Pricing Details | Zesty Expiry Inventory Tracker"
                description="Plans listing"
            />
            <div className="p-6">
                <div>
                    <div className="mb-8">
                        <h1 className="text-4xl font-semibold text-center">
                            Pricing Details
                        </h1>
                    </div>
                    <div className="mb-3">
                        <div className="flex justify-center gap-1 mb-4">
                            <button
                                className={cn(
                                    "rounded text-white text-base px-4 py-1.5",
                                    {
                                        "bg-green-900": billing_period == "month",
                                        "bg-green-700 hover:bg-green-800": billing_period == "year",
                                    }
                                )}
                                onClick={() => {setBillingPeriod("month")}}
                            >
                                Billed Monthly
                            </button>
                            <button
                                className={cn(
                                    "rounded text-white text-base px-4 py-1.5",
                                    {
                                        "bg-green-900": billing_period == "year",
                                        "bg-green-600 hover:bg-green-700": billing_period == "month",
                                    }
                                )}
                                onClick={() => {setBillingPeriod("year")}}
                            >
                                Billed Annually
                            </button>
                        </div>
                    </div>
                    <div className="flex flex-col lg:flex-row gap-4">
                        <div className="flex gap-4 w-full">
                            <div className="flex w-1/2 border">
                                <div className="flex flex-grow">
                                    <div className="flex flex-col w-full p-8" style={{minHeight: "40em"}}>
                                        <div className="mb-auto">
                                            <p className="mb-4 text-muted">{plans.free.name}</p>
                                            <h2 className="text-2xl font-semibold">
                                                Free
                                            </h2>
                                            <div className="my-4 py-4">
                                                {plans.free.features.map((text, index) => <ChecklistItem key={index} text={text} />)}
                                            </div>
                                        </div>
                                        <button className="w-full rounded-md bg-white border border-green-600 text-green-600 text-base hover:bg-green-600 hover:text-white py-4">
                                            Try for Free
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div className="flex w-1/2 border">
                                <div className="flex flex-grow">
                                    <div className="flex flex-col w-full p-8" style={{minHeight: "40em"}}>
                                        <div className="mb-auto">
                                            <p className="mb-4 text-muted">{plans.pro.name}</p>
                                            <div className="flex items-center gap-0.5">
                                                <h2 className="text-2xl font-semibold">
                                                    ${formatAmountValue(billing_period == "month" ? plans.pro.month.price : plans.pro.year.price)}
                                                </h2>
                                                <div className="mt-0.5">
                                                    <h3 className="text-md text-gray-500">
                                                        /{billing_period}
                                                    </h3>
                                                </div>
                                            </div>
                                            <div className="my-4 py-4">
                                                {plans.pro.features.map((text, index) => <ChecklistItem key={index} text={text} />)}
                                            </div>
                                        </div>
                                        <button className="w-full rounded-md bg-green-600 text-white text-base hover:bg-green-700 py-4">
                                            Get Started
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex gap-4 w-full">
                            <div className="flex w-1/2 border">
                                <div className="flex flex-grow">
                                    <div className="flex flex-col w-full p-8" style={{minHeight: "40em"}}>
                                        <div className="mb-auto">
                                            <p className="mb-4 text-muted">{plans.advanced.name}</p>
                                            <div className="flex items-center gap-0.5">
                                                <h2 className="text-2xl font-semibold">
                                                    ${formatAmountValue(billing_period == "month" ? plans.advanced.month.price : plans.advanced.year?.price)}
                                                </h2>
                                                <div className="mt-0.5">
                                                    <h3 className="text-md text-gray-500">
                                                        /{billing_period}
                                                    </h3>
                                                </div>
                                            </div>
                                            <div className="my-4 py-4">
                                                {plans.advanced.features.map((text, index) => <ChecklistItem key={index} text={text} />)}
                                            </div>
                                        </div>
                                        <button className="w-full rounded-md bg-green-600 text-white text-base hover:bg-green-700 py-4">
                                            Get Started
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div className="flex w-1/2 border">
                                <div className="flex flex-grow">
                                    <div className="flex flex-col w-full p-8" style={{minHeight: "40em"}}>
                                        <div className="mb-auto">
                                            <p className="mb-4 text-muted">{plans.plus.name}</p>
                                            <div className="flex items-center gap-0.5">
                                                <h2 className="text-2xl font-semibold">
                                                    ${formatAmountValue(billing_period == "month" ? plans.plus.month.price : plans.plus.year.price)}
                                                </h2>
                                                <div className="mt-0.5">
                                                    <h3 className="text-md text-gray-500">
                                                        /{billing_period}
                                                    </h3>
                                                </div>
                                            </div>
                                            <div className="my-4 py-4">
                                                {plans.plus.features.map((text, index) => <ChecklistItem key={index} text={text} />)}
                                            </div>
                                        </div>
                                        <button className="w-full rounded-md bg-green-600 text-white text-base hover:bg-green-700 py-4">
                                            Get Started
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}