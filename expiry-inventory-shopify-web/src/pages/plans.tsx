import { useRouter } from "next/router";
import { <PERSON>ton, ButtonGroup, Layout, Page, Stack } from "@shopify/polaris";
import React, { useEffect, useState } from "react";
import ChecklistItem from "@components/common/checklist-item";
import { fetchAPI, showToast, toastOptions } from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import PlanDowngradeConfirmModal from "@components/plan/plan-downgrade-confirm-modal";
import PlansSkeleton from "@components/ui/loaders/plans-skeleton";
import PageLayout from "@components/layouts/layout";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { Plan, PlansResponse, RecurringPlan } from "@ts-types/types";
import { ShopifyCard } from "@components/ui/shopify/card";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { ShopifyText } from "@components/ui/shopify/text";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Redirect } from "@shopify/app-bridge/actions";
import { PLAN_TYPE_ENUM } from "@utils/constants";

const PLAN_ACTION = {
    UPGRADE: "upgrade",
    DOWNGRADE: "downgrade"
};

export default function PlanPage() {
    const { query } = useRouter();
    const [current_plan, setCurrentPlan] = useState<{ plan: string; billing_period: string }>({
        plan: PLAN_TYPE_ENUM.FREE,
        billing_period: "month"
    });
    const [plans, setPlans] = useState<PlansResponse | null>(null);
    const [is_modal_active, setIsModalActive] = useState(false);
    const [plan_action, setPlanAction] = useState<string>(PLAN_ACTION.UPGRADE);
    const [selected_plan, setSelectedPlan] = useState<string>(PLAN_TYPE_ENUM.FREE);
    const [changingPlan, setChangingPlan] = useState<string | null>(null);
    const [billing_period, setBillingPeriod] = useState("month");
    const app = useAppBridge();
    const {
        data: settings,
        refetch: refetchSettings,
    } = useSettingsQuery();

    useEffect(() => {
        if(settings) {
            setCurrentPlan({
                plan: settings.plan,
                billing_period: settings.billing_period ?? "month"
            });
        }
    }, [settings]);

    useEffect(() => {
        if(query && query.charge_id) {
            const { charge_id } = query;
            if(charge_id) {
                fetchAPI("PUT", `${API_ENDPOINTS.PLAN}/${charge_id}`, null)
                    .then(() => {
                        refetchSettings();
                        showToast(toastOptions.PLAN_UPGRADED);
                    })
                    .catch((error) => {
                        showToast({ message: error });
                    })
                    .finally(() => {
                        //@ts-ignore
                        const redirect = Redirect.create(app);
                        redirect.dispatch(Redirect.Action.APP, ROUTES.PLANS);
                    });
            }
            else {
                refetchSettings();
            }
        }
        else {
            refetchSettings();
        }
        retrievePlans();
    }, [query]);

    function retrievePlans() {
        fetchAPI("GET", API_ENDPOINTS.PLAN)
            .then((plan_settings) => {
                setPlans(plan_settings as PlansResponse);
            });
    }

    function changePlan(plan: string, action: string, is_required_prompt = false) {
        if(is_required_prompt) {
            setIsModalActive(true);
            setPlanAction(action);
            setSelectedPlan(plan);
        }
        else {
            setChangingPlan(plan);
            if(action == PLAN_ACTION.UPGRADE) {
                upgradePlan(plan);
            }
            else {
                downgradePlan();
            }
        }
    }

    function upgradePlan(plan: string) {
        fetchAPI("POST", API_ENDPOINTS.PLAN_UPGRADE, { plan_type: plan, plan_period: billing_period })
            .then((recurring_plan) => {
                if(recurring_plan) {
                    const redirect = Redirect.create(app);
                    redirect.dispatch(Redirect.Action.REMOTE, (recurring_plan as RecurringPlan).confirmation_url);
                }
            })
            .catch((error) => {
                showToast({message: error});
            })
            .finally(() => {
                setChangingPlan(null);
            });
    }

    function downgradePlan() {
        fetchAPI("POST", API_ENDPOINTS.PLAN_DOWNGRADE)
            .then(() => {
                showToast(toastOptions.PLAN_DOWNGRADED);
                planDowngraded(true);
            })
            .finally(() => {
                setChangingPlan(null);
            });
    }

    function planDowngraded(success: boolean) {
        if(success) {
            refetchSettings();
        }
        setIsModalActive(false);
    }

    function formatAmountValue(value: number) {
        return Intl.NumberFormat().format(value);
    }

    if(!plans) {
        return (
            <PlansSkeleton />
        );
    }
    
    return (
        <Page>
            <Layout>
                <Layout.Section>
                    <div className="mb-4">
                        <ShopifyText as="h1" variant="heading3xl" alignment="center">
                            Pick the plan that’s right for you.
                        </ShopifyText>
                    </div>
                </Layout.Section>
                <Layout.Section>
                    <div className="flex justify-center mb-4">
                        <ButtonGroup segmented>
                            <Button pressed={billing_period == "month"} primary onClick={() => {setBillingPeriod("month")}}>
                                Billed Monthly
                            </Button>
                            <Button pressed={billing_period == "year"} primary onClick={() => {setBillingPeriod("year")}}>
                                Billed Annually
                            </Button>
                        </ButtonGroup>
                    </div>
                </Layout.Section>
                <Layout.Section>
                    <div className="flex flex-col lg:flex-row gap-4">
                        <div className="flex gap-4 w-full">
                            <div className="flex w-1/2">
                                <ShopifyCard>
                                    <div className="flex flex-col h-full p-8" style={{minHeight: "40em"}}>
                                        <div className="mb-auto">
                                            <p className="mb-4 text-muted">{plans.free.name}</p>
                                            <ShopifyText as="h2" variant="heading2xl">
                                                Free
                                            </ShopifyText>
                                            <div className="my-4 py-4">
                                                {plans.free.features.map((text, index) => <ChecklistItem key={index} text={text} />)}
                                            </div>
                                        </div>
                                        <Button
                                            fullWidth
                                            loading={changingPlan == PLAN_TYPE_ENUM.FREE}
                                            disabled={current_plan.plan == PLAN_TYPE_ENUM.FREE || changingPlan == PLAN_TYPE_ENUM.FREE}
                                            primary
                                            onClick={() => { current_plan.plan != PLAN_TYPE_ENUM.FREE && changePlan(PLAN_TYPE_ENUM.FREE, PLAN_ACTION.DOWNGRADE, true) }}
                                        >
                                            {current_plan.plan == PLAN_TYPE_ENUM.FREE ? "Current plan" : "Change plan"}
                                        </Button>
                                    </div>
                                </ShopifyCard>
                            </div>
                            <div className="flex w-1/2">
                                <ShopifyCard>
                                    <div className="flex flex-col h-full p-8" style={{minHeight: "40em"}}>
                                        <div className="mb-auto">
                                            <p className="mb-4 text-muted">{plans.pro.name}</p>
                                            <Stack spacing="extraTight">
                                                <ShopifyText as="h2" variant="heading2xl">
                                                    ${formatAmountValue(billing_period == "month" ? plans.pro.month.price : plans.pro.year.price)}
                                                </ShopifyText>
                                                <div className="mt-0.5">
                                                    <ShopifyText as="h3" variant="headingMd" color="subdued" fontWeight="regular">
                                                        /{billing_period}
                                                    </ShopifyText>
                                                </div>
                                            </Stack>
                                            <div className="my-4 py-4">
                                                {plans.pro.features.map((text, index) => <ChecklistItem key={index} text={text} />)}
                                            </div>
                                        </div>
                                        <Button
                                            fullWidth
                                            loading={changingPlan == PLAN_TYPE_ENUM.PRO}
                                            disabled={[PLAN_TYPE_ENUM.PRO, PLAN_TYPE_ENUM.TRIAL_PRO].includes(current_plan.plan) && current_plan.billing_period == billing_period || changingPlan == PLAN_TYPE_ENUM.PRO}
                                            primary
                                            onClick={() => { changePlan(PLAN_TYPE_ENUM.PRO, PLAN_ACTION.UPGRADE, current_plan.plan == PLAN_TYPE_ENUM.ADVANCED || current_plan.plan == PLAN_TYPE_ENUM.PLUS) }}
                                        >
                                            {[PLAN_TYPE_ENUM.PRO, PLAN_TYPE_ENUM.TRIAL_PRO].includes(current_plan.plan) && current_plan.billing_period == billing_period ? "Current plan" : "Change plan"}
                                        </Button>
                                    </div>
                                </ShopifyCard>
                            </div>
                        </div>
                        <div className="flex gap-4 w-full">
                            <div className="flex w-1/2">
                                <ShopifyCard>
                                    <div className="flex flex-col h-full p-8" style={{minHeight: "40em"}}>
                                        <div className="mb-auto">
                                            <p className="mb-4 text-muted">{plans.advanced.name}</p>
                                            <Stack spacing="extraTight">
                                                <ShopifyText as="h2" variant="heading2xl">
                                                    ${formatAmountValue(billing_period == "month" ? plans.advanced.month.price : plans.advanced.year.price)}
                                                </ShopifyText>
                                                <div className="mt-0.5">
                                                    <ShopifyText as="h3" variant="headingMd" color="subdued" fontWeight="regular">
                                                        /{billing_period}
                                                    </ShopifyText>
                                                </div>
                                            </Stack>
                                            <div className="my-4 py-4">
                                                {plans.advanced.features.map((text, index) => <ChecklistItem key={index} text={text} />)}
                                            </div>
                                        </div>
                                        <Button
                                            fullWidth
                                            loading={changingPlan == PLAN_TYPE_ENUM.ADVANCED}
                                            disabled={current_plan.plan == PLAN_TYPE_ENUM.ADVANCED && current_plan.billing_period == billing_period || changingPlan == PLAN_TYPE_ENUM.ADVANCED}
                                            primary
                                            onClick={() => { changePlan(PLAN_TYPE_ENUM.ADVANCED, PLAN_ACTION.UPGRADE, current_plan.plan == PLAN_TYPE_ENUM.PLUS) }}
                                        >
                                            {current_plan.plan == PLAN_TYPE_ENUM.ADVANCED && current_plan.billing_period == billing_period ? "Current plan" : "Change plan"}
                                        </Button>
                                    </div>
                                </ShopifyCard>
                            </div>
                            <div className="flex w-1/2">
                                <ShopifyCard>
                                    <div className="flex flex-col h-full p-8" style={{minHeight: "40em"}}>
                                        <div className="mb-auto">
                                            <p className="mb-4 text-muted">{plans.plus.name}</p>
                                            <Stack spacing="extraTight">
                                                <ShopifyText as="h2" variant="heading2xl">
                                                    ${formatAmountValue(billing_period == "month" ? plans.plus.month.price : plans.plus.year.price)}
                                                </ShopifyText>
                                                <div className="mt-0.5">
                                                    <ShopifyText as="h3" variant="headingMd" color="subdued" fontWeight="regular">
                                                        /{billing_period}
                                                    </ShopifyText>
                                                </div>
                                            </Stack>
                                            <div className="my-4 py-4">
                                                {plans.plus.features.map((text, index) => <ChecklistItem key={index} text={text} />)}
                                            </div>
                                        </div>
                                        <Button
                                            fullWidth
                                            loading={changingPlan == PLAN_TYPE_ENUM.PLUS}
                                            disabled={current_plan.plan == PLAN_TYPE_ENUM.PLUS && current_plan.billing_period == billing_period || changingPlan == PLAN_TYPE_ENUM.PLUS}
                                            primary
                                            onClick={() => { changePlan(PLAN_TYPE_ENUM.PLUS, PLAN_ACTION.UPGRADE) }}
                                        >
                                            {current_plan.plan == PLAN_TYPE_ENUM.PLUS && current_plan.billing_period == billing_period ? "Current plan" : "Change plan"}
                                        </Button>
                                    </div>
                                </ShopifyCard>
                            </div>
                        </div>
                    </div>
                </Layout.Section>
            </Layout>
            <PlanDowngradeConfirmModal active={is_modal_active} onConfirm={() => changePlan(selected_plan, plan_action)} onDismiss={planDowngraded} />
        </Page>
    );
}

PlanPage.Layout = PageLayout;