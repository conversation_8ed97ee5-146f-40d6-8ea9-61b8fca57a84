import type { GetServerSideProps } from "next";
import React from "react";
import PageLayout from "@components/layouts/layout";
import ProductDetails from "@components/product/product-details";
import { useUI } from "@contexts/ui.context";
import PageLoading from "@components/common/page-loading";

function ProductViewPage(props: any) {
    const query = props.query;
    const { product_id, variant_id } = query;
    const { location, locations } = useUI();
    return (
        (!location && !locations) ? <PageLoading /> : <ProductDetails product_id={product_id} variant_id={variant_id} {...props} />
    );
}

export default ProductViewPage;

ProductViewPage.Layout = PageLayout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            query: ctx.query
        },
    };
};