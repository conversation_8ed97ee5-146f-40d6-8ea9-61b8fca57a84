import React, { useEffect, useState } from "react";
import {
  Page,
  DataTable,
  Button,
  Pagination,
  Link,
  Filters,
  FiltersProps,
  ChoiceList,
} from "@shopify/polaris";
import { ClockMajor, ExportMinor } from "@shopify/polaris-icons";
import moment from "moment";

import TabOptions from "@components/common/tab-options";
import LocationPicker from "@components/common/location-picker";
import {
  dateDiff,
  fetchAPI,
  formatDate,
  showToast,
  toastOptions,
} from "@utils/helpers";
import EmptyDataTable from "@components/ui/loaders/empty-data-table";
import NoResult from "@components/common/no-result";
import { ROUTES } from "@utils/routes";
import Search from "@components/common/search";
import Layout from "@components/layouts/layout";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import {
  Product,
  ProductPaginationData,
  SortFilterOptions,
} from "@ts-types/types";
import { useUI } from "@contexts/ui.context";
import { ShopifyCard } from "@components/ui/shopify/card";
import { useRouter } from "next/router";
import { useModalAction } from "@components/ui/modal/modal.context";
import ExportProductModal from "@components/product/export-product-modal";

const tabOptions = [
  {
    key: "",
    label: "All",
  },
  {
    key: "with_batches",
    label: "With batches",
  },
  {
    key: "without_batches",
    label: "Without batches",
  },
  {
    key: "discounted",
    label: "Discounted",
  },
];

const emptyFilterState: {
  query: {
    label: string;
    value: "";
  };
  inventoryStatus: {
    label: string;
    value: string[];
  };
  batchedStatus: {
    label: string;
    value: string[];
  };
} = {
  query: {
    label: "Search",
    value: "",
  },
  inventoryStatus: {
    label: "Inventory status",
    value: [],
  },
  batchedStatus: {
    label: "Batched status",
    value: [],
  },
};

function ProductPage() {
  const router = useRouter();
  const {
    location: selected_location,
    inventory_status,
    setInventoryStatus: saveInventoryStatus,
    batched_status,
    setBatchedStatus: saveBatchedStatus,
  } = useUI();
  const { openModal, closeModal } = useModalAction();
  const [is_loading, setIsLoading] = useState(true);
  const [selected_tab, setSelectedTab] = useState(0);
  const [page, setPage] = useState(1);
  const [total_pages, setTotalPage] = useState(1);
  const [product_search, setProductSearch] = useState("");
  const [product_rows, setProductRows] = useState<Array<Product>>([]);
  const [inventoryStatus, setInventoryStatus] = useState<string[]>(
    inventory_status ?? []
  );
  const [batchedStatus, setBatchedStatus] = useState<string[]>(
    batched_status ?? []
  );
  const [export_modal_active, setExportModalActive] = useState(false);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (selected_location) {
        getProductsData();
      }
    }, 100);
    return () => clearTimeout(delayDebounceFn);
  }, [selected_location, selected_tab, page]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setPage(1);
      getProductsData();
    }, 1000);
    return () => clearTimeout(delayDebounceFn);
  }, [product_search, inventoryStatus, batchedStatus]);

  function getProductsData(options: SortFilterOptions | null = null) {
    let location_id = selected_location ? String(selected_location.id) : "";
    let filter = tabOptions[selected_tab].key;
    const searchPayload = {
      limit: 20,
      location: location_id,
      filter,
      ...(inventoryStatus.length
        ? { inventory_status: inventoryStatus[0] }
        : {}),
      ...(batchedStatus?.includes("with_unaccounted_quantity")
        ? { with_unaccounted_quantity: 1 }
        : {}),
      search: product_search,
      page,
      order_by: options && options.sort_field ? options.sort_field : "",
      order_direction:
        options && options.sort_direction ? options.sort_direction : "",
    };
    setIsLoading(true);
    fetchAPI("GET", API_ENDPOINTS.PRODUCT, searchPayload)
      .then((response) => {
        setTotalPage((response as ProductPaginationData).last_page);
        const productData = (response as ProductPaginationData).data;
        if (product_search.length > 0 && productData.length == 1) {
          router.push(
            `${ROUTES.PRODUCTS}/${productData[0].parent_id}/${productData[0].shopify_variant_id}`
          );
        }
        setProductRows(productData);
      })
      .catch((error) => {
        showToast(toastOptions.PRODUCTS_GET_FAILED);
      })
      .finally(() => {
        setTimeout(() => {
          setIsLoading(false);
        }, 2000);
      });
  }

  function handleSelectTab(option: number) {
    setPage(1);
    setSelectedTab(option);
  }

  function handleProductSort(index: number, direction: string) {
    let field = null;
    switch (index) {
      case 0:
        field = "parent_name";
        break;
      case 1:
        field = "sku";
        break;
      case 2:
      case 3:
        field = "expiry_date";
        break;
      default:
        return;
    }
    getProductsData({
      sort_field: field,
      sort_direction: direction == "ascending" ? "ASC" : "DESC",
    });
  }

  const handleFilterChange =
    (key: string) => (value: string | string[] | number | [number, number]) => {
      if (key === "inventoryStatus") {
        setInventoryStatus(value as string[]);
        saveInventoryStatus(value as string[]);
      }
      if (key === "batchedStatus") {
        setBatchedStatus(value as string[]);
        saveBatchedStatus(value as string[]);
      }
    };

  const handleFilterRemove = (key: string) => {
    if (key === "inventoryStatus") {
      setInventoryStatus(emptyFilterState.inventoryStatus.value);
      saveInventoryStatus(emptyFilterState.inventoryStatus.value);
    }
    if (key === "batchedStatus") {
      setBatchedStatus(emptyFilterState.batchedStatus.value);
      saveBatchedStatus(emptyFilterState.batchedStatus.value);
    }
  };

  const handleFiltersClearAll = () => {
    Object.entries(emptyFilterState).forEach(([key]) =>
      handleFilterRemove(key)
    );
  };

  const filters = [
    {
      key: "inventoryStatus",
      label: "Inventory status",
      value: inventoryStatus,
      filter: (
        <ChoiceList
          title="Inventory status"
          titleHidden
          choices={[
            { label: "In stock", value: "in_stock" },
            { label: "Expired", value: "expired" },
            { label: "Past best before", value: "past_best_before" },
          ]}
          selected={inventoryStatus}
          onChange={handleFilterChange("inventoryStatus")}
        />
      ),
      shortcut: true,
      pinned: true,
    },
    {
      key: "batchedStatus",
      label: "Batched status",
      value: batchedStatus,
      filter: (
        <ChoiceList
          title="Batched status"
          titleHidden
          choices={[
            {
              label: "With unaccounted quantity",
              value: "with_unaccounted_quantity",
            },
          ]}
          selected={batchedStatus}
          onChange={handleFilterChange("batchedStatus")}
          allowMultiple
        />
      ),
      shortcut: true,
      pinned: true,
    },
  ];

  const appliedFilters: FiltersProps["appliedFilters"] = [];

  filters.forEach(({ key, label, value }) => {
    if (value?.length) {
      appliedFilters.push({
        key,
        label: `${label}: ${value
          .map((val) => val.replaceAll("_", " "))
          .join(", ")}`,
        onRemove: () => handleFilterRemove(key),
      });
    }
  });

  function handleAddBatchClicked(product_id: string | number | null) {
    openModal(
      "EDIT_INVENTORY_BATCH_VIEW",
      { product_id },
      {
        modalTitle: "Add inventory batch",
        onDismiss: handleBatchModalClosed,
        hideCancelBtn: true,
      }
    );
  }

  function toggleExportModal() {
    setExportModalActive(!export_modal_active);
  }

  function handleBatchModalClosed(status = false) {
    if (status === true) {
      getProductsData();
    }
    closeModal();
  }

  return (
    <Page title="Products">
      <div className="flex mb-5">
        <div className="mr-4">
          <Button icon={ExportMinor} onClick={toggleExportModal}>
            Export
          </Button>
        </div>
        <div>
          <Button url={ROUTES.INVENTORY_HISTORY} icon={ClockMajor}>
            View inventory batch history
          </Button>
        </div>
      </div>
      <ShopifyCard>
        <div>
          <div className="flex items-stretch border-b">
            <TabOptions
              options={tabOptions}
              selected={selected_tab}
              onChange={handleSelectTab}
            />
            <div className="self-center ml-auto mr-4">
              <LocationPicker />
            </div>
          </div>
          <div className="Polaris-Connected">
            <div
              className="Polaris-Connected__Item Polaris-Connected__Item--primary"
              style={{ padding: "10px 15px" }}
            >
              <Search
                value={product_search}
                onChange={setProductSearch}
                placeholder="Search inventory by name, SKU, or barcode"
                showClear={true}
              />
            </div>
          </div>
          <div className="Polaris-Connected">
            <div
              className="Polaris-Connected__Item Polaris-Connected__Item--primary"
              style={{ padding: "10px 15px" }}
            >
              <Filters
                queryValue=""
                filters={filters}
                hideQueryField
                disableQueryField
                appliedFilters={appliedFilters}
                onQueryChange={() => {}}
                onQueryClear={() => {}}
                onClearAll={handleFiltersClearAll}
              ></Filters>
            </div>
          </div>
          {is_loading ? <EmptyDataTable /> : <></>}
          {!is_loading && (!product_rows || product_rows.length <= 0) ? (
            <NoResult
              is_filtering={selected_tab}
              onResetButtonClicked={() => setSelectedTab(0)}
            />
          ) : (
            <></>
          )}
          {product_rows.length > 0 ? (
            <div className={is_loading ? "hidden" : ""}>
              <DataTable
                hideScrollIndicator
                columnContentTypes={[
                  "text",
                  "text",
                  "text",
                  "text",
                  "numeric",
                  "numeric",
                  "numeric",
                  "numeric",
                  "text",
                ]}
                headings={[
                  "Product",
                  "SKU",
                  inventoryStatus?.includes("past_best_before")
                    ? "Best before"
                    : "Expiry date",
                  inventoryStatus?.includes("expired")
                    ? "Days past expiry"
                    : inventoryStatus?.includes("past_best_before")
                    ? "Days past best before"
                    : "Days to expiry",
                  "Available qty",
                  "Expired qty",
                  "Total qty",
                  "In stock batches",
                  "Batch tracking",
                ]}
                rows={product_rows.map((row) => {
                  const nearest_expire_date = inventoryStatus?.includes(
                    "expired"
                  )
                    ? row.batch_items?.at(0)?.expire_at
                    : inventoryStatus?.includes("past_best_before")
                    ? row.batch_items
                        ?.at(0)
                        ?.dates.map(({ date }) => moment(date))
                        .filter((date) => date.isBefore(moment.now()))
                        .sort((a, b) => b.diff(a))
                        ?.at(0)
                        ?.toDate()
                    : row.nearest_expire_date;
                  const expiry_days_left = nearest_expire_date
                    ? inventoryStatus?.includes("expired") ||
                      inventoryStatus?.includes("past_best_before")
                      ? dateDiff(nearest_expire_date, moment())
                      : dateDiff(moment(), nearest_expire_date)
                    : null;
                  return [
                    <div className="flex gap-2">
                      <span className="flex-shrink-0 Polaris-Thumbnail Polaris-Thumbnail--sizeSmall self-start">
                        <img
                          className="Polaris-Thumbnail__Image"
                          alt={row.name}
                          src={
                            row.image_url
                              ? row.image_url
                              : "/assets/no-image.jpg"
                          }
                        />
                      </span>
                      <div className="self-center">
                        <p>
                          <Link
                            url={`${ROUTES.PRODUCTS}/${row.parent_id}/${row.shopify_variant_id}`}
                            removeUnderline
                          >
                            {row.parent_name}
                          </Link>
                        </p>
                        {row.name != row.parent_name ? (
                          <p>
                            <Link
                              url={`${ROUTES.PRODUCTS}/${row.parent_id}/${row.shopify_variant_id}`}
                              removeUnderline
                            >
                              {row.name}
                            </Link>
                          </p>
                        ) : (
                          <></>
                        )}
                      </div>
                    </div>,
                    row.sku ? row.sku : "—",
                    nearest_expire_date ? formatDate(nearest_expire_date) : "—",
                    Number(expiry_days_left) > 0
                      ? expiry_days_left
                      : expiry_days_left === 0
                      ? "Today"
                      : "—",
                    row.available_quantity,
                    row.expired_quantity,
                    row.quantity,
                    row.batch_items.length,
                    row.is_inventory_managed ? (
                      <Button onClick={() => handleAddBatchClicked(row.id)}>
                        Add batch
                      </Button>
                    ) : (
                      <Link
                        url={`${ROUTES.PRODUCTS}/${row.parent_id}/${row.shopify_variant_id}`}
                        removeUnderline
                      >
                        Setup
                      </Link>
                    ),
                  ];
                })}
                sortable={[
                  true,
                  true,
                  true,
                  true,
                  false,
                  false,
                  false,
                  false,
                  false,
                ]}
                defaultSortDirection="descending"
                onSort={handleProductSort}
              />
            </div>
          ) : (
            <></>
          )}
          <ExportProductModal
            exportInitialValues={{
              filter: tabOptions[selected_tab],
              location: selected_location,
              inventory_status: inventoryStatus,
              with_unaccounted_quantity: batchedStatus?.includes(
                "with_unaccounted_quantity"
              ),
            }}
            active={export_modal_active}
            onDismiss={toggleExportModal}
          ></ExportProductModal>
        </div>
      </ShopifyCard>
      <div id="product-paginator">
        <Pagination
          hasPrevious={1 < page}
          onPrevious={() => {
            setPage(page - 1);
          }}
          hasNext={page < total_pages}
          onNext={() => {
            setPage(page + 1);
          }}
        />
      </div>
    </Page>
  );
}

export default ProductPage;

ProductPage.Layout = Layout;
