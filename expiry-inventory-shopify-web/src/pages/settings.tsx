import { useEffect, useState } from "react";
import {
  Button,
  Checkbox,
  FormLayout,
  Layout,
  Page,
  Select,
  TextField,
} from "@shopify/polaris";
import {
  fetchAPI,
  setMerchantSettings,
  showToast,
  toastOptions,
} from "@utils/helpers";
import { ROUTES } from "@utils/routes";
import ChecklistItem from "@components/common/checklist-item";
import PreferencesSkeleton from "@components/ui/loaders/preferences-skeleton";
import PageLayout from "@components/layouts/layout";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import { ShopifyText } from "@components/ui/shopify/text";
import {
  BatchExpirationPeriod,
  BatchDiscount,
  PlansResponse,
  ProductPaginationData,
  SlowMovingDiscount,
} from "@ts-types/types";
import { ShopifyCard } from "@components/ui/shopify/card";
import { useSettingsQuery } from "@data/settings/use-settings.query";
import { useShopify } from "@contexts/shopify.context";
import { useQueryClient } from "react-query";
import ThemeExpirySetupInstructionsModal from "@components/theme/theme-expiry-setup-instructions-modal";
import { useModalAction } from "@components/ui/modal/modal.context";
import { PLAN_TYPE_ENUM } from "@utils/constants";

const DEFAULT_DISCOUNT = { discount_rate: 0, type: "expiry", days_before_expire: 0 };
const DEFAULT_SLOW_MOVING_PRODUCT = {
  track_period: 0,
  unit: "days",
  sales_qty: 0,
  discount_rate: 0,
  discount_period: 0,
};
const DEFAULT_NOTIFY_BATCH_EXPIRATION = { period: 30, unit: "days" };

function PreferencesPage() {
  const { shop, host } = useShopify();
  const { openModal } = useModalAction();
  const [is_remove_when_expired, setIsRemoveWhenExpired] = useState(true);
  const [days_before_expire, setDaysBeforeExpire] = useState<string | number>(
    0
  );
  const [is_apply_discount, setIsApplyDiscount] = useState(false);
  const [batch_discounts, setBatchDiscounts] = useState<Array<BatchDiscount>>([
    DEFAULT_DISCOUNT,
  ]);
  const [is_apply_slow_moving, setIsApplySlowMoving] = useState(false);
  const [slow_moving_discounts, setSlowMovingDiscounts] = useState<
    Array<SlowMovingDiscount>
  >([DEFAULT_SLOW_MOVING_PRODUCT]);
  const [is_auto_update_discount, setIsAutoUpdateDiscount] = useState(false);
  const [is_email_notification, setIsEmailNotification] = useState(true);
  const [is_notify_batch_out_of_stock, setIsBatchOutOfStockNotification] =
    useState(true);
  const [is_notify_batch_expiration, setIsNotifyBatchExpiration] =
    useState(false);
  const [notify_batch_expiration, setNotifyBatchExpiration] = useState<
    Array<BatchExpirationPeriod>
  >([DEFAULT_NOTIFY_BATCH_EXPIRATION]);
  const [is_notify_batch_export, setIsNotifyBatchExport] = useState(true);
  const [notification_email, setNotificationEmail] = useState("");
  const [is_show_storefront_expire_at, setIsShowStorefrontExpireAt] =
    useState(false);
  const [is_sync_batch_quantity, setIsSyncBatchQuantity] = useState(true);
  const [current_plan, setCurrentPlan] = useState<{
    plan: string;
    billing_period: string;
  }>({
    plan: PLAN_TYPE_ENUM.FREE,
    billing_period: "month",
  });
  const [plans, setPlans] = useState<PlansResponse | null>(null);
  const [total_batch_products, setTotalBatchProducts] = useState(0);
  const [saving, setSaving] = useState(false);
  const [is_onboard_modal_active, setIsOnboardModalActive] = useState(false);
  const queryClient = useQueryClient();
  const { data: settings, isLoading } = useSettingsQuery();

  useEffect(() => {
    getPlans();
    getBatchProducts();
  }, []);

  useEffect(() => {
    if (settings) {
      setIsRemoveWhenExpired(settings.is_remove_when_expired ?? false);
      setDaysBeforeExpire(settings.days_before_expire);
      setIsApplyDiscount(settings.is_apply_discount ?? false);
      setBatchDiscounts(settings.batch_discounts.map((discount) => ({ ...discount, type: discount.type ?? "expiry" })) ?? [DEFAULT_DISCOUNT]);
      setIsApplySlowMoving(settings.is_apply_slow_moving ?? false);
      setSlowMovingDiscounts(
        settings.slow_moving_discounts ?? [DEFAULT_SLOW_MOVING_PRODUCT]
      );
      setIsAutoUpdateDiscount(settings.is_auto_update_discount ?? false);
      setIsEmailNotification(settings.is_email_notification ?? false);
      setIsBatchOutOfStockNotification(
        settings.is_notify_batch_out_of_stock ?? false
      );
      setIsNotifyBatchExpiration(settings.is_notify_batch_expiration ?? false);
      setIsNotifyBatchExport(settings.is_notify_batch_export === false ? false : true);
      setNotifyBatchExpiration(
        settings.notify_batch_expiration
          ? Array.isArray(settings.notify_batch_expiration)
            ? settings.notify_batch_expiration
            : [settings.notify_batch_expiration]
          : [DEFAULT_NOTIFY_BATCH_EXPIRATION]
      );
      setNotificationEmail(settings.notification_email ?? "");
      setIsShowStorefrontExpireAt(
        settings.is_show_storefront_expire_at ?? false
      );
      setIsSyncBatchQuantity(settings.is_sync_batch_quantity ?? false);
      setCurrentPlan({
        plan: settings.plan,
        billing_period: settings.billing_period ?? "month",
      });
    }
  }, [settings]);

  function getPlans() {
    fetchAPI("GET", API_ENDPOINTS.PLAN).then((_plans) => {
      setPlans(_plans as PlansResponse);
    });
  }

  function getBatchProducts() {
    fetchAPI("GET", API_ENDPOINTS.PRODUCT_PARENT_WITH_BATCHES).then((response) => {
      setTotalBatchProducts((response as ProductPaginationData).total);
    });
  }

  function savePreferences() {
    const batchDiscounts =
      current_plan.plan != PLAN_TYPE_ENUM.FREE
        ? batch_discounts
            .filter(
              (discount) =>
                (discount.discount_rate as number) > 0 &&
                (discount.days_before_expire as number) > 0
            )
        : [];
    const isApplyBatchDiscount =
      current_plan.plan != PLAN_TYPE_ENUM.FREE
        ? is_apply_discount && batchDiscounts.length
        : false;
    const slowMovingDiscounts =
      current_plan.plan != PLAN_TYPE_ENUM.FREE
        ? slow_moving_discounts
            .filter(
              (discount) =>
                (discount.sales_qty as number) > 0 &&
                (discount.discount_rate as number) > 0
            )
            .sort(
              (a, b) => (a.track_period as number) - (b.track_period as number)
            )
        : [];
    const isApplySlowMovingDiscount =
      current_plan.plan != PLAN_TYPE_ENUM.FREE
        ? is_apply_slow_moving && slowMovingDiscounts.length
        : false;
    const notifyBatchExpiration =
      current_plan.plan != PLAN_TYPE_ENUM.FREE
        ? notify_batch_expiration.filter(
            ({ period }) => !!period && period != ""
          )
        : [];
    const isNotifyBatchExpiration =
      current_plan.plan != PLAN_TYPE_ENUM.FREE
        ? is_notify_batch_expiration && notifyBatchExpiration.length
        : false;
    const payload = {
      is_remove_when_expired,
      days_before_expire,
      is_apply_discount: isApplyBatchDiscount,
      batch_discounts: isApplyBatchDiscount ? batchDiscounts : [],
      is_apply_slow_moving: isApplySlowMovingDiscount,
      is_auto_update_discount:
        current_plan.plan != PLAN_TYPE_ENUM.FREE ? is_auto_update_discount : false,
      slow_moving_discounts: isApplySlowMovingDiscount
        ? slowMovingDiscounts
        : [],
      is_email_notification,
      is_notify_batch_out_of_stock:
        current_plan.plan != PLAN_TYPE_ENUM.FREE ? is_notify_batch_out_of_stock : false,
      is_notify_batch_expiration: isNotifyBatchExpiration,
      notify_batch_expiration: isNotifyBatchExpiration
        ? notifyBatchExpiration
        : [],
      is_notify_batch_export,
      notification_email,
      is_show_storefront_expire_at,
      is_sync_batch_quantity,
    };
    setSaving(true);
    setMerchantSettings(payload, settings)
      .then((success) => {
        if (success) {
          queryClient.invalidateQueries([API_ENDPOINTS.PREFERENCE]);
          showToast(toastOptions.PREFERENCES_SAVED);
        } else {
          showToast(toastOptions.PREFERENCES_NOT_SAVED);
        }
      })
      .finally(() => {
        setSaving(false);
      });
  }

  function addBatchExpiration() {
    const _batch_expiration = [...notify_batch_expiration];
    _batch_expiration.push(DEFAULT_NOTIFY_BATCH_EXPIRATION);
    setNotifyBatchExpiration(_batch_expiration);
  }
  function removeBatchExpiration(index: number) {
    const _batch_expiration = [...notify_batch_expiration];
    _batch_expiration.splice(index, 1);
    setNotifyBatchExpiration(_batch_expiration);
  }
  function onBatchExpirationsChange(
    batch_expiration: BatchExpirationPeriod,
    index: number
  ) {
    const _batch_expiration = [...notify_batch_expiration];
    _batch_expiration[index] = batch_expiration;
    setNotifyBatchExpiration(_batch_expiration);
  }

  function addDiscount() {
    const _batch_discounts = [...batch_discounts];
    _batch_discounts.push(DEFAULT_DISCOUNT);
    setBatchDiscounts(_batch_discounts);
  }
  function removeDiscount(index: number) {
    const _batch_discounts = [...batch_discounts];
    _batch_discounts.splice(index, 1);
    setBatchDiscounts(_batch_discounts);
  }
  function onBatchDiscountsChange(
    batch_discount: BatchDiscount,
    index: number
  ) {
    const _batch_discounts = [...batch_discounts];
    _batch_discounts[index] = batch_discount;
    setBatchDiscounts(_batch_discounts);
  }

  function addSlowMovingDiscount() {
    const _slow_moving_discounts = [...slow_moving_discounts];
    _slow_moving_discounts.push(DEFAULT_SLOW_MOVING_PRODUCT);
    setSlowMovingDiscounts(_slow_moving_discounts);
  }
  function removeSlowMovingDiscount(index: number) {
    const _slow_moving_discounts = [...slow_moving_discounts];
    _slow_moving_discounts.splice(index, 1);
    setSlowMovingDiscounts(_slow_moving_discounts);
  }
  function onSlowMovingDiscountsChange(
    slow_moving_discount: SlowMovingDiscount,
    index: number
  ) {
    const _slow_moving_discounts = [...slow_moving_discounts];
    _slow_moving_discounts[index] = slow_moving_discount;
    setSlowMovingDiscounts(_slow_moving_discounts);
  }

  function handleEditMetaPresets() {
    openModal("META_PRESET_VIEW", null, { modalTitle: "Edit meta presets", hideCancelBtn: true });
  }

  function handleBatchDeletion() {
    openModal("CLEAR_BATCHES_VIEW", null, { modalTitle: "Delete all batches", hideCancelBtn: true });
  }

  if (!plans || isLoading || !settings) {
    return <PreferencesSkeleton />;
  }

  return (
    <Page
      title="Settings"
      primaryAction={{
        content: "Save",
        onAction: savePreferences,
        loading: saving,
        disabled: saving,
      }}
    >
      <Layout>
        <Layout.AnnotatedSection
          title="Batch expiry"
          description={
            <div>
              <p className="mb-2">
                Manage what happens to batches before they reach their expiry
                date.
              </p>
              {/* <p><a href="">Learn more</a></p> */}
            </div>
          }
        >
          <ShopifyCard sectioned>
            <FormLayout>
              <Checkbox
                label={
                  <ShopifyText variant="bodyMd" as="p">
                    <ShopifyText
                      variant="bodyMd"
                      fontWeight="semibold"
                      as="span"
                    >
                      Remove batch quantity
                    </ShopifyText>{" "}
                    from inventory
                  </ShopifyText>
                }
                helpText="Batches are marked as expired once they have been removed from inventory."
                checked={is_remove_when_expired}
                onChange={setIsRemoveWhenExpired}
              />
              <div className="Polaris-Connected ml-4">
                <div
                  className="Polaris-Connected__Item Polaris-Connected__Item--primary days_expire_textf"
                  style={{ padding: "10px 15px" }}
                >
                  <TextField
                    label=""
                    labelHidden
                    autoComplete="off"
                    suffix="days before expiry date"
                    inputMode="numeric"
                    value={String(days_before_expire)}
                    onChange={setDaysBeforeExpire}
                  ></TextField>
                </div>
              </div>
              <p>
                These actions occur at 12:00 am UTC on the day that they are
                scheduled.
              </p>
            </FormLayout>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Batch discounts"
          description={
            <div>
              <p className="mb-2">Manage how batches are discounted.</p>
              <p className="font-semibold mb-2">
                If discount overlaps with slow moving products, batch expiry
                will take precedence.
              </p>
              {/* <p><a href="">Learn more</a></p> */}
            </div>
          }
        >
          <ShopifyCard>
            <FormLayout>
              {current_plan.plan != PLAN_TYPE_ENUM.FREE ? (
                <ShopifyCard.Section>
                  <Checkbox
                    label={
                      <ShopifyText variant="bodyMd" as="p">
                        <ShopifyText
                          variant="bodyMd"
                          fontWeight="semibold"
                          as="span"
                        >
                          Apply discount
                        </ShopifyText>{" "}
                        to batch
                      </ShopifyText>
                    }
                    checked={is_apply_discount}
                    onChange={setIsApplyDiscount}
                  />
                  <div className="divide-y divide-gray-300">
                    {batch_discounts.map((batch_discount, idx) => (
                      <div key={idx} className="ml-4">
                        <div className="space-y-2" style={{ padding: "5px 15px" }}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <TextField
                                label=""
                                labelHidden
                                autoComplete="off"
                                suffix="% discount"
                                inputMode="numeric"
                                value={String(batch_discount.discount_rate)}
                                onChange={(val) =>
                                  onBatchDiscountsChange(
                                    { ...batch_discount, discount_rate: val },
                                    idx
                                  )
                                }
                                disabled={!is_apply_discount}
                              ></TextField>
                              <Select
                                label=""
                                labelHidden
                                options={[
                                  { label: "expiry", value: "expiry" },
                                  { label: "best before", value: "best_before" },
                                ]}
                                value={batch_discount.type}
                                onChange={(val) =>
                                  onBatchDiscountsChange(
                                    { ...batch_discount, type: val },
                                    idx
                                  )
                                }
                                disabled={!is_apply_discount}
                              ></Select>
                            </div>
                            <div>
                              <Button
                                plain
                                destructive
                                onClick={() => removeDiscount(idx)}
                              >
                                Remove
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <div>
                              <TextField
                                label=""
                                labelHidden
                                autoComplete="off"
                                suffix={`days before ${batch_discount.type?.replaceAll("_", " ")} date`}
                                inputMode="numeric"
                                value={String(batch_discount.days_before_expire)}
                                onChange={(val) =>
                                  onBatchDiscountsChange(
                                    {
                                      ...batch_discount,
                                      days_before_expire: val,
                                    },
                                    idx
                                  )
                                }
                                disabled={!is_apply_discount}
                              ></TextField>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button onClick={addDiscount}>Add Discount</Button>
                  </div>
                </ShopifyCard.Section>
              ) : (
                <ShopifyCard.Section>
                  <div className="mb-2">
                    <ShopifyText
                      as="h3"
                      variant="bodyMd"
                      color="subdued"
                      fontWeight="regular"
                    >
                      Upgrade to {plans?.pro?.name} plan to manage product
                      automatic discounts.
                    </ShopifyText>
                  </div>
                  <Button outline url={ROUTES.PLANS}>
                    View plans
                  </Button>
                </ShopifyCard.Section>
              )}
            </FormLayout>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Slow moving product discounts"
          description={
            <div>
              <p className="mb-2">
                Manage discounts for slow moving product batches.
              </p>
              <p className="font-semibold mb-2">
                If discount overlaps with batch expiry, batch expiry will take
                precedence.
              </p>
            </div>
          }
        >
          <ShopifyCard>
            <FormLayout>
              {current_plan.plan != PLAN_TYPE_ENUM.FREE ? (
                <ShopifyCard.Section>
                  <Checkbox
                    label={
                      <ShopifyText variant="bodyMd" as="p">
                        <ShopifyText
                          variant="bodyMd"
                          fontWeight="semibold"
                          as="span"
                        >
                          Apply discount
                        </ShopifyText>{" "}
                        for slow moving products
                      </ShopifyText>
                    }
                    helpText="Specifying discount days to 0 will remove discount when min. sales achieved."
                    checked={is_apply_slow_moving}
                    onChange={setIsApplySlowMoving}
                  />
                  <div className="divide-y divide-gray-300 mt-4">
                    {slow_moving_discounts.map((slow_moving_discount, idx) => (
                      <div key={idx} className="ml-4">
                        <div className="flex flex-col items-center py-2 pl-8">
                          <div className="w-full mb-2">
                            <div className="flex justify-start items-center gap-8">
                              <div className="flex items-center">
                                <span>Track period</span>
                                <div className="mr-1">
                                  <TextField
                                    label=""
                                    labelHidden
                                    autoComplete="off"
                                    inputMode="numeric"
                                    value={String(
                                      slow_moving_discount.track_period
                                    )}
                                    onChange={(val) =>
                                      onSlowMovingDiscountsChange(
                                        {
                                          ...slow_moving_discount,
                                          track_period: val,
                                        },
                                        idx
                                      )
                                    }
                                    disabled={!is_apply_slow_moving}
                                  ></TextField>
                                </div>
                                <Select
                                  label=""
                                  labelHidden
                                  options={[
                                    { label: "day", value: "days" },
                                    { label: "month", value: "months" },
                                  ]}
                                  value={slow_moving_discount.unit}
                                  onChange={(val) =>
                                    onSlowMovingDiscountsChange(
                                      { ...slow_moving_discount, unit: val },
                                      idx
                                    )
                                  }
                                  disabled={!is_apply_slow_moving}
                                ></Select>
                              </div>
                              <div className="flex items-center">
                                <span>Sales lower than</span>
                                <TextField
                                  label=""
                                  labelHidden
                                  autoComplete="off"
                                  suffix="(qty)"
                                  inputMode="numeric"
                                  value={String(slow_moving_discount.sales_qty)}
                                  onChange={(val) =>
                                    onSlowMovingDiscountsChange(
                                      {
                                        ...slow_moving_discount,
                                        sales_qty: val,
                                      },
                                      idx
                                    )
                                  }
                                  disabled={!is_apply_slow_moving}
                                ></TextField>
                              </div>
                            </div>
                          </div>
                          <div className="w-full mb-2">
                            <div className="flex justify-start items-center gap-8">
                              <span>Apply</span>
                              <TextField
                                label=""
                                labelHidden
                                autoComplete="off"
                                suffix="% discount"
                                inputMode="numeric"
                                value={String(
                                  slow_moving_discount.discount_rate
                                )}
                                onChange={(val) =>
                                  onSlowMovingDiscountsChange(
                                    {
                                      ...slow_moving_discount,
                                      discount_rate: val,
                                    },
                                    idx
                                  )
                                }
                                disabled={!is_apply_slow_moving}
                              ></TextField>
                              <span>for</span>
                              <TextField
                                label=""
                                labelHidden
                                autoComplete="off"
                                suffix="days"
                                inputMode="numeric"
                                value={String(
                                  slow_moving_discount.discount_period
                                )}
                                onChange={(val) =>
                                  onSlowMovingDiscountsChange(
                                    {
                                      ...slow_moving_discount,
                                      discount_period: val,
                                    },
                                    idx
                                  )
                                }
                                disabled={!is_apply_slow_moving}
                              ></TextField>
                              <Button
                                plain
                                destructive
                                onClick={() => removeSlowMovingDiscount(idx)}
                              >
                                Remove
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button onClick={addSlowMovingDiscount}>
                      Add Discount
                    </Button>
                  </div>
                </ShopifyCard.Section>
              ) : (
                <ShopifyCard.Section>
                  <div className="mb-2">
                    <ShopifyText
                      as="h3"
                      variant="bodyMd"
                      color="subdued"
                      fontWeight="regular"
                    >
                      Upgrade to {plans?.pro?.name} plan to manage slow moving
                      product discounts.
                    </ShopifyText>
                  </div>
                  <Button outline url={ROUTES.PLANS}>
                    View plans
                  </Button>
                </ShopifyCard.Section>
              )}
            </FormLayout>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Discounts Activation"
          description="Manage activation setting for expiry and slow moving product discounts."
        >
          <ShopifyCard>
            {current_plan.plan != PLAN_TYPE_ENUM.FREE ? (
              <ShopifyCard.Section>
                <FormLayout>
                  <Checkbox
                    label="Apply discount to product automatically"
                    helpText="Leaving uncheck will prompt for approval before applying discount to products."
                    checked={is_auto_update_discount}
                    onChange={setIsAutoUpdateDiscount}
                  />
                </FormLayout>
              </ShopifyCard.Section>
            ) : (
              <ShopifyCard.Section>
                <div className="mb-2">
                  <ShopifyText
                    as="h3"
                    variant="bodyMd"
                    color="subdued"
                    fontWeight="regular"
                  >
                    Upgrade to {plans?.pro?.name} plan to manage discounts
                    activation.
                  </ShopifyText>
                </div>
                <Button outline url={ROUTES.PLANS}>
                  View plans
                </Button>
              </ShopifyCard.Section>
            )}
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Staff notifications"
          description={
            <div>
              <p className="mb-2">
                Choose if you want to be notified when a batch is removed from
                inventory, when a product expires, or to opt-in to order
                notifications.
              </p>
              {/* <p><a href="">Learn more</a></p> */}
            </div>
          }
        >
          <ShopifyCard title="Email notifications">
            <ShopifyCard.Section>
              <FormLayout>
                <p>
                  Notifications are sent at 12:00 am UTC on the day that they
                  are scheduled.
                </p>
                <Checkbox
                  label="Notify email recipient when batch quantities are removed from inventory."
                  checked={is_email_notification}
                  onChange={setIsEmailNotification}
                />
                {current_plan.plan != PLAN_TYPE_ENUM.FREE ? (
                  <div className="">
                    <Checkbox
                      label="Notify email recipient when batch becomes out of stock."
                      checked={is_notify_batch_out_of_stock}
                      onChange={setIsBatchOutOfStockNotification}
                    />
                    <div className="">
                      <Checkbox
                        label="Notify email recipient prior to batch expiration for specific periods:"
                        checked={is_notify_batch_expiration}
                        onChange={setIsNotifyBatchExpiration}
                      />
                    </div>
                    <div className="space-y-2 mt-4">
                      {notify_batch_expiration.map((batch_expiration, idx) => (
                        <div key={idx} className="flex items-center gap-2 ml-4">
                          <div className="w-[100px]">
                            <TextField
                              label=""
                              labelHidden
                              autoComplete="off"
                              inputMode="numeric"
                              value={String(batch_expiration.period)}
                              onChange={(val) =>
                                onBatchExpirationsChange(
                                  { ...batch_expiration, period: val },
                                  idx
                                )
                              }
                              disabled={!is_notify_batch_expiration}
                            ></TextField>
                          </div>
                          <Select
                            label=""
                            labelHidden
                            options={[
                              { label: "day", value: "days" },
                              { label: "month", value: "months" },
                            ]}
                            value={batch_expiration.unit}
                            onChange={(val) =>
                              onBatchExpirationsChange(
                                { ...batch_expiration, unit: val },
                                idx
                              )
                            }
                            disabled={!is_notify_batch_expiration}
                          ></Select>
                          <Button
                            plain
                            destructive
                            onClick={() => removeBatchExpiration(idx)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-end mt-4">
                      <Button onClick={addBatchExpiration}>
                        Add Expiration Period
                      </Button>
                    </div>
                    <div className="mt-4">
                      <Checkbox
                        label="Notify email recipient on batch export completed"
                        checked={is_notify_batch_export}
                        onChange={setIsNotifyBatchExport}
                      />
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="mb-2">
                      <ShopifyText
                        as="h3"
                        variant="bodyMd"
                        color="subdued"
                        fontWeight="regular"
                      >
                        Upgrade to {plans?.pro?.name} plan for more batch
                        changes notifications.
                      </ShopifyText>
                    </div>
                    <Button outline url={ROUTES.PLANS}>
                      View plans
                    </Button>
                  </div>
                )}
              </FormLayout>
            </ShopifyCard.Section>
            <ShopifyCard.Section>
              <FormLayout>
                <TextField
                  autoComplete="off"
                  label="Email recipient"
                  inputMode="text"
                  helpText="Separate multiple email addresses using semicolons. E.g. <EMAIL>; <EMAIL>"
                  value={notification_email}
                  onChange={setNotificationEmail}
                ></TextField>
              </FormLayout>
            </ShopifyCard.Section>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Inventory"
          description={
            <div>
              <p className="mb-2">
                Manage how batch quantity adjustments in Zesty Expiry Inventory
                Tracker affect inventory in Shopify.
              </p>
              {/* <p><a href="">Learn more</a></p> */}
            </div>
          }
        >
          <ShopifyCard sectioned>
            <FormLayout>
              <Checkbox
                label={
                  <ShopifyText variant="bodyMd" as="p">
                    Batch quantities added or adjusted on Zesty Expiry Inventory
                    Tracker are reflected in Shopify.
                  </ShopifyText>
                }
                helpText="When batch quantities are added or adjusted, the quantity adjustments are automatically synced with Shopify."
                checked={is_sync_batch_quantity}
                onChange={setIsSyncBatchQuantity}
              />
            </FormLayout>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Expiry/Best Before date on storefront"
          description={
            <div>
              <p className="mb-2">
                Manage how your customers see product expiry/best before dates.
              </p>
              {/* <p><a href="">Learn more</a></p> */}
            </div>
          }
        >
          <ShopifyCard>
            <FormLayout>
              {current_plan.plan != PLAN_TYPE_ENUM.FREE ? (
                <>
                  <ShopifyCard.Section>
                    <Checkbox
                      label={
                        <ShopifyText variant="bodyMd" as="p">
                          Expiry/Best Before date for products are visible to
                          customers on storefront.
                        </ShopifyText>
                      }
                      helpText="When inventory batches are added or updated, the expiry date and best before date at storefront are automatically updated to the earliest upcoming date"
                      checked={is_show_storefront_expire_at}
                      onChange={setIsShowStorefrontExpireAt}
                    />
                  </ShopifyCard.Section>
                  <ShopifyCard.Section>
                    <div className="mb-2">
                      <ShopifyText
                        as="h3"
                        variant="bodyMd"
                        color="subdued"
                        fontWeight="regular"
                      >
                        Learn how to setup expiry date in your store's theme to
                        display expiry date.
                      </ShopifyText>
                    </div>
                    <Button
                      outline
                      onClick={() => setIsOnboardModalActive(true)}
                    >
                      View installation guide
                    </Button>
                  </ShopifyCard.Section>
                </>
              ) : (
                <ShopifyCard.Section>
                  <div className="mb-2">
                    <ShopifyText
                      as="h3"
                      variant="bodyMd"
                      color="subdued"
                      fontWeight="regular"
                    >
                      Upgrade to {plans?.pro?.name} plan to display product
                      expiry dates on your store-end product page.
                    </ShopifyText>
                  </div>
                  <Button outline url={ROUTES.PLANS}>
                    View plans
                  </Button>
                </ShopifyCard.Section>
              )}
            </FormLayout>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Inventory Batch Meta Presets"
          description={<div>{/* <p><a href="">Learn more</a></p> */}</div>}
        >
          <ShopifyCard>
            <FormLayout>
              {current_plan.plan != PLAN_TYPE_ENUM.FREE ? (
                <ShopifyCard.Section>
                  <div className="mb-2">
                    <ShopifyText
                      as="h3"
                      variant="bodyMd"
                      color="subdued"
                      fontWeight="regular"
                    >
                      Manage meta preset fields for inventory batch.
                    </ShopifyText>
                  </div>
                  <Button
                    outline
                    onClick={handleEditMetaPresets}
                  >
                    Edit meta
                  </Button>
                </ShopifyCard.Section>
              ) : (
                <ShopifyCard.Section>
                  <div className="mb-2">
                    <ShopifyText
                      as="h3"
                      variant="bodyMd"
                      color="subdued"
                      fontWeight="regular"
                    >
                      Upgrade to {plans?.pro?.name} plan to edit the inventory batch meta presets.
                    </ShopifyText>
                  </div>
                  <Button outline url={ROUTES.PLANS}>
                    View plans
                  </Button>
                </ShopifyCard.Section>
              )}
            </FormLayout>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Packing slip templates"
          description={<div>{/* <p><a href="">Learn more</a></p> */}</div>}
        >
          <ShopifyCard>
            <FormLayout>
              {current_plan.plan != PLAN_TYPE_ENUM.FREE ? (
                <ShopifyCard.Section>
                  <div className="mb-2">
                    <ShopifyText
                      as="h3"
                      variant="bodyMd"
                      color="subdued"
                      fontWeight="regular"
                    >
                      Manage your packing slip template for details to show to
                      your customer.
                    </ShopifyText>
                  </div>
                  <Button
                    outline
                    url={`${ROUTES.PACKING_SLIP_EDIT}?shop=${shop}&host=${host}`}
                  >
                    Edit packing slip template
                  </Button>
                </ShopifyCard.Section>
              ) : (
                <ShopifyCard.Section>
                  <div className="mb-2">
                    <ShopifyText
                      as="h3"
                      variant="bodyMd"
                      color="subdued"
                      fontWeight="regular"
                    >
                      Upgrade to {plans?.pro?.name} plan to edit the packing
                      slip liquid template.
                    </ShopifyText>
                  </div>
                  <Button outline url={ROUTES.PLANS}>
                    View plans
                  </Button>
                </ShopifyCard.Section>
              )}
            </FormLayout>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Delete all batches"
          description={<div>{/* <p><a href="">Learn more</a></p> */}</div>}
        >
          <ShopifyCard>
            <FormLayout>
              <ShopifyCard.Section>
                <div className="mb-2">
                  <ShopifyText
                    as="h3"
                    variant="bodyMd"
                    color="subdued"
                    fontWeight="regular"
                  >
                    Performing this will remove all inventory batches created in your account.
                  </ShopifyText>
                </div>
                <Button
                  destructive
                  onClick={handleBatchDeletion}
                >
                  Delete all batches
                </Button>
              </ShopifyCard.Section>
            </FormLayout>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title="Subscription"
          description={
            <p className="mb-2">View and manage your subscription plan.</p>
          }
        >
          <ShopifyCard>
            <ShopifyCard.Section title="Price">
              {current_plan.plan == PLAN_TYPE_ENUM.TRIAL_PRO ? "" : (
                "$" +
                (current_plan.plan != PLAN_TYPE_ENUM.FREE
                  ? plans[current_plan.plan][
                      current_plan.billing_period as "month" | "year"
                    ].price.toFixed(2)
                  : "0.00") + ` per ${current_plan.billing_period} - `
              )}
              {current_plan.plan == PLAN_TYPE_ENUM.TRIAL_PRO ? "Trial" : plans[current_plan.plan]?.name}
            </ShopifyCard.Section>
            <ShopifyCard.Section title="Usage">
              <p>
                {total_batch_products} /{" "}
                {current_plan.plan != PLAN_TYPE_ENUM.FREE
                  ? plans?.pro?.batch_products_limit > 0
                    ? plans?.pro?.batch_products_limit
                    : "Unlimited"
                  : plans?.free?.batch_products_limit}
              </p>
              <ShopifyText
                as="h3"
                variant="bodyMd"
                color="subdued"
                fontWeight="regular"
              >
                Batch tracked products
              </ShopifyText>
            </ShopifyCard.Section>
            <ShopifyCard.Section
              title={`Your are currently subscribed to ${
                current_plan.plan == PLAN_TYPE_ENUM.TRIAL_PRO ? "Trial" : plans[current_plan.plan]?.name
              } Plan`}
            >
              <p>Your plan includes the following features:</p>
              <div className="mt-2">
                {plans[current_plan.plan == PLAN_TYPE_ENUM.TRIAL_PRO ? PLAN_TYPE_ENUM.PRO : current_plan.plan]?.features.map((text, index) => (
                  <ChecklistItem
                    key={index}
                    text={text}
                    className="items-center"
                    variant="chevron"
                  />
                ))}
              </div>
            </ShopifyCard.Section>
            <ShopifyCard.Section subdued>
              <FormLayout>
                <Button outline url={ROUTES.PLANS}>
                  View plans
                </Button>
              </FormLayout>
            </ShopifyCard.Section>
          </ShopifyCard>
        </Layout.AnnotatedSection>

        <div className="w-full text-right mt-4">
          <Button primary onClick={savePreferences}>
            Save
          </Button>
        </div>
      </Layout>
      {current_plan.plan != PLAN_TYPE_ENUM.FREE ? (
        <ThemeExpirySetupInstructionsModal
          active={is_onboard_modal_active}
          onConfirm={() => setIsOnboardModalActive(false)}
          onDismiss={() => setIsOnboardModalActive(false)}
        />
      ) : (
        <></>
      )}
    </Page>
  );
}

export default PreferencesPage;

PreferencesPage.Layout = PageLayout;
