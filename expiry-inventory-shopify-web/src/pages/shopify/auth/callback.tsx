import type { GetServerSideProps } from "next";
import React from "react";
import PageLoading from "@components/common/page-loading";
import { ROUTES } from "@utils/routes";
import Layout from "@components/layouts/layout";
import { fetchAPI } from "@utils/helpers";
import { API_ENDPOINTS } from "@utils/api/endpoints";

export default function ShopifyCallback() {
    return <PageLoading />;
}

ShopifyCallback.Layout = Layout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    const { req, query } = ctx;

    const { shop, host, embedded } = query;

    if(shop) {
        if (embedded === "1") {
            // to exitiframe
            // @ts-ignore
            const redirectUriParams = new URLSearchParams({
                shop,
                host,
            }).toString();
            // @ts-ignore
            const queryParams = new URLSearchParams({
                ...query,
                shop,
                redirectUri: `${process.env.NEXT_PUBLIC_APP_URL!}${ROUTES.SHOPIFY}?${redirectUriParams}`,
            }).toString();

            return {
                redirect: {
                    permanent: false,
                    destination: `${ROUTES.EXIT_IFRAME}?${queryParams}`,
                },
            };
        }

        const cookies = req.cookies;
        if(!cookies || !cookies.nonce || !query.state || query.state != cookies.nonce) {
            return {
                redirect: {
                    permanent: false,
                    destination: ROUTES.SHOPIFY_LOGIN,
                },
            };
        }

        if(query.hmac && shop && host) {
            try {
                const data = await fetchAPI("POST", API_ENDPOINTS.SHOPIFY_AUTH_CALLBACK, query, false)
                return {
                    redirect: {
                        permanent: false,
                        destination: data as string,
                    },
                };
            }
            catch(error) {
                return {
                    redirect: {
                        permanent: false,
                        destination: ROUTES.SHOPIFY_LOGIN,
                    },
                };
            }
        }
    }

    return {
        redirect: {
            permanent: false,
            destination: ROUTES.SHOPIFY_LOGIN,
        },
    };
};