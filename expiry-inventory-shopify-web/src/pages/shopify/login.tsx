import { Button, Text, TextField } from "@shopify/polaris";
import { useState } from "react";
import { showToast } from "@utils/helpers";
import { useShopifyAuthMutation } from "@data/shopify/use-shopify-auth.mutation";

export default function ShopifyPromptShop() {
    const { mutate: shopifyAuth, isLoading } = useShopifyAuthMutation();
    const [shop, setShop] = useState("");

    function login() {
        shopifyAuth({ shop }, {
            onSuccess(data) {
                window.location.assign(data as string);
            },
            onError(err) {
                if(err) {
                    //@ts-ignore
                    showToast({ message: err as string });
                }
            }
        });
    }

    return (
        <div className="flex justify-center items-center m-auto" style={{width: "50%"}}>
            <div className="text-center" style={{width: "100%"}}>
                <div className="flex justify-center items-center" style={{width: "100%", height: "15rem"}}>
                    <Text variant="heading3xl" as="h1"><PERSON><PERSON>y Expiry Inventory Tracker</Text>
                </div>
                <Text variant="bodyMd" as="p" color="subdued">Enter shop domain to login or install this app.</Text>
                <div className="mt-2">
                    <TextField autoComplete="off" label="" labelHidden placeholder="myshop.myshopify.com" value={shop} onChange={setShop} />
                </div>
                <div className="mt-4">
                    <Button primary fullWidth size="large" loading={isLoading} onClick={login}>Login/Install</Button>
                </div>
            </div>
        </div>
    );
}