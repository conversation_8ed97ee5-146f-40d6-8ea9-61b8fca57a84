@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --border-color: #dee2e6;
}

div {
  --p-background: #fafafa;
  --p-border: #dee2e6;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  background-color: white !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.Polaris-Spinner svg {
  fill: var(--p-action-primary);
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

a {
  color: var(--p-interactive, #006fbb);
  text-decoration: none;
  cursor: pointer;
}

.Polaris-Banner__Button:hover {
  text-decoration: underline;
}

a.delete-link {
  color: #ff1b1b;
}

a.green-link {
  color: #008060;
}

a.link {
  color: #454f5b;
}

a.link .Polaris-Icon {
  display: inline-block;
}

.Polaris-DataTable__Cell {
  text-align: left;
}

.Polaris-DataTable__Cell--numeric {
  text-align: right;
}

#products-quantity-paginator {
  justify-content: center;
  width: 100%;
  display: inline-flex;
}

#inventory-batch-paginator {
  padding: 2.4rem 1.6rem;
  justify-content: center;
  width: 100%;
  display: inline-flex;
}

#inventory-batch-orders-paginator {
  padding: 2.4rem 1.6rem;
  justify-content: center;
  width: 100%;
  display: inline-flex;
}

#order-paginator {
  padding: 2.4rem 1.6rem;
  justify-content: center;
  width: 100%;
  display: inline-flex;
}

#import-history-paginator {
  padding: 2.4rem 1.6rem;
  justify-content: center;
  width: 100%;
  display: inline-flex;
}

.days_expire_textf .Polaris-TextField {
  width: 50%;
}

#product-paginator {
  padding: 2.4rem 1.6rem;
  justify-content: center;
  width: 100%;
  display: inline-flex;
}