.template-expiry-date-inventory-batch * {
    font-family: "Open Sans", "sans-serif";
    font-size: 14px;
    box-sizing: border-box;
    font-weight: 300;
}

.template-expiry-date-inventory-batch {
    margin: auto;
    padding: 10px 30px 0 30px;
    min-height: 600px;
}

.template-expiry-date-inventory-batch p {
    margin: 0 0 7px 0;
}

.template-expiry-date-inventory-batch a,
.template-expiry-date-inventory-batch a:link,
.template-expiry-date-inventory-batch a:visited {
    color: #000;
    font-weight: 300;
    text-decoration: none;
}

.template-expiry-date-inventory-batch .header {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: top;
    margin-bottom: 30px;
}

.template-expiry-date-inventory-batch .header p {
    margin: 0;
}

.template-expiry-date-inventory-batch .shop-title {
    -webkit-box-flex: 6;
    -webkit-flex: 6;
    flex: 6;
    font-size: 30px;
    font-weight: 400;
}

.template-expiry-date-inventory-batch .shop-logo {
    max-width: 240px;
}

.template-expiry-date-inventory-batch .order-title {
    -webkit-box-flex: 4;
    -webkit-flex: 4;
    flex: 4;
}

.template-expiry-date-inventory-batch .customer-addresses {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: top;
    margin-bottom: 15px;
}

.template-expiry-date-inventory-batch .shipping-address {
    flex-grow: 1;
    flex-basis: 0;
}

.template-expiry-date-inventory-batch .billing-address {
    flex-grow: 1;
    flex-basis: 0;
}

.template-expiry-date-inventory-batch .order-details {
    text-align: right;
    flex-grow: 1;
    flex-basis: 0;
}

.template-expiry-date-inventory-batch .address-detail,
.template-expiry-date-inventory-batch .order-detail {
    margin: 15px 0 0;
    line-height: 1.5;
}

.template-expiry-date-inventory-batch .subtitle-bold {
    font-weight: bold;
    margin: 0;
    font-size: 13px;
}

.template-expiry-date-inventory-batch .order-detail + .subtitle-bold {
    margin-top: 15px;
}

.template-expiry-date-inventory-batch .order-table {
    display: block;
}

.template-expiry-date-inventory-batch .order-table-row {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin: 15px 0;
    page-break-inside: avoid;
}

.template-expiry-date-inventory-batch .order-table-header {
    margin-bottom: 0;
}

.template-expiry-date-inventory-batch .order-table-header .item-image-and-description {
    -webkit-box-flex: 8;
    -webkit-flex: 8;
    flex: 8;
    margin-right: 30px;
}

.template-expiry-date-inventory-batch .order-table-header .order-table-cell {
    white-space: nowrap;
}

.template-expiry-date-inventory-batch .order-table-cell {
    -webkit-box-flex: 2;
    -webkit-flex: 2;
    flex: 2;
    margin: 0;
}

.template-expiry-date-inventory-batch .item-image {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    margin-right: 15px;
    min-width: 58px;
}

.template-expiry-date-inventory-batch .item-description {
    -webkit-box-flex: 7;
    -webkit-flex: 7;
    flex: 7;
}

.template-expiry-date-inventory-batch .item-description-line {
    display: block;
    margin: 0;
}

.template-expiry-date-inventory-batch .item-description p {
    margin: 0;
    line-height: 1.5;
}

.template-expiry-date-inventory-batch .item-line-price {
    -webkit-box-flex: 3;
    -webkit-flex: 3;
    flex: 3;
}

.template-expiry-date-inventory-batch .missing-line-items-text {
    margin: 15px 0;
    padding: 0 7px;
}

.template-expiry-date-inventory-batch .notes-and-pricing {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: top;
    margin-bottom: 15px;
}

.template-expiry-date-inventory-batch .notes {
    flex-grow: 2;
    flex-basis: 0;
}

.template-expiry-date-inventory-batch .notes-row {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: top;
    margin: 15px 0;
    page-break-inside: avoid;
}

.template-expiry-date-inventory-batch .notes-title {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}

.template-expiry-date-inventory-batch .notes-details {
    -webkit-box-flex: 3;
    -webkit-flex: 3;
    flex: 3;
    margin-right: 30px;
}

.template-expiry-date-inventory-batch .footer {
    margin-top: 30px;
    text-align: center;
    line-height: 1.5;
}

.template-expiry-date-inventory-batch .footer p {
    margin: 0;
    margin-bottom: 15px;
}

.template-expiry-date-inventory-batch hr {
    height: 2px;
    border-bottom: 2px solid #e1e1e1;
    margin: 0;
}

.template-expiry-date-inventory-batch .aspect-ratio {
    position: relative;
    display: block;
    background: #fafbfc;
    padding: 0;
}

.template-expiry-date-inventory-batch .aspect-ratio-square {
    width: 58px; height: 58px;
}

.template-expiry-date-inventory-batch .aspect-ratio-square .quantity-badge {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate3d(0.5rem, -50%, 0);
    z-index: 20;
    align-items: center;
    font-size: 1rem;
    display: inline-flex;
    text-transform: none;
    letter-spacing: normal;
    border: 2px solid #fff;
    background-color: #dfe3e8;
    color: #414f3e;
    border-radius: 2rem;
    white-space: nowrap;
    vertical-align: middle;
    padding: 0 0.5rem;
}

.opacity-0 {
    opacity: 0
}

.text-right {
    text-align: right;
}

.border-bottom {
    border-bottom: 1px solid #333;
}

.template-expiry-date-inventory-batch .aspect-ratio::before {
    z-index: 1;
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border: 1px solid rgba(195, 207, 216, 0.3);
}

.template-expiry-date-inventory-batch .aspect-ratio--square {
    width: 100%;
    padding-bottom: 100%;
}

.template-expiry-date-inventory-batch .aspect-ratio__content {
    position: absolute;
    max-width: 100%;
    max-height: 100%;
    display: block;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
}

.template-expiry-date-inventory-batch .pricing {
    flex-grow: 1;
    flex-basis: 0;
}

.template-expiry-date-inventory-batch .pricing-row {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 15px 0;
    page-break-inside: avoid;
}

.template-expiry-date-inventory-batch .pricing-title {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}

.template-expiry-date-inventory-batch .pricing-details {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}

.template-expiry-date-inventory-batch .label-warning {
    display: inline-block;
    background-color: #ffea8a;
    border-radius: 10px;
    padding: 1px 6px;
    margin-top: 3px;
}

@media screen {
    .printer-preview-content {
        border: 2px solid transparent;
    }
    [contenteditable="true"]:active,
    [contenteditable="true"]:focus {
        border: 2px solid #5c6ac4;
        outline: none;
        border-radius: 4px;
    }
}

@media print {
    .printer-preview-content {
        page-break-after: always;
    }
    html {
        -webkit-print-color-adjust: exact;
    }
    body,
    html {
        height: 100vh;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden;
        background: #fff !important;
        background-color: #fff !important;
    }
    .container-fluid .row:first-of-type {
        padding-top: 0 !important;
    }
    [contenteditable="true"]:active,
    [contenteditable="true"]:focus {
        border: none;
        outline: none;
        border-radius: 0;
    }
    #preview,
    .card,
    .printer-preview-content {
        width: 100% !important;
        background-color: #fff !important;
        padding: 0 !important;
        border: none !important;
        clear: both;
    }
    #preview:after,
    .card:after,
    .printer-preview-content:after {
        content: " ";
        display: table;
    }
    #sidebar,
    #preview-placeholder,
    #templates-placeholder,
    #preview .card-header,
    #preview .hide,
    .hide-on-print,
    .hs-widget-container {
        display: none !important;
    }
    .printer-inline-preview {
        break-before: page;
        page-break-before: always;
        break-after: avoid-page;
        page-break-after: avoid;
        overflow: hidden;
        clear: both;
    }
    #preview .printer-inline-preview {
        margin-bottom: 0 !important;
    }
    .printer-inline-preview-first {
        break-before: avoid-page !important;
        page-break-before: avoid !important;
    }
    .printer-inline-preview-first:after {
        content: " ";
        display: table;
    }
    .col-md-pull-3,
    .col-lg-pull-4,
    .col-md-push-9,
    .col-lg-push-8 {
        margin: 0 !important;
        left: 0;
        right: 0;
    }
    .clearfix:before,
    .clearfix:after {
        content: " ";
        display: table;
    }
    .clearfix:after {
        clear: both;
    }
    .clearfix {
        zoom: 1;
        height: 1px;
        line-height: 1px;
        font-size: 1px;
    }
}
@media screen, print {
    .printer-preview-content body,
    .printer-preview-content html {
        font-size: 13px;
        line-height: 1.33;
        background-color: none;
        background: none;
        padding-top: 0 !important;
    }
    .printer-preview-content #body {
        min-height: 0 !important;
    }
    .printer-preview-content {
        font-size: 13px;
        padding: 0.5em;
    }
    .printer-preview-content * {
        margin: 0;
        padding: 0;
        line-height: 1.33;
        color: #000;
        font-family: "Open Sans", "sans-serif";
    }
    .printer-preview-content h1,
    .printer-preview-content h2,
    .printer-preview-content h3,
    .printer-preview-content h4,
    .printer-preview-content h5,
    .printer-preview-content h6,
    .printer-preview-content p,
    .printer-preview-content li,
    .printer-preview-content span {
        color: #000;
    }
    .printer-preview-content h1,
    .printer-preview-content h2,
    .printer-preview-content h3,
    .printer-preview-content h4,
    .printer-preview-content h5,
    .printer-preview-content h6,
    .printer-preview-content p,
    .printer-preview-content ul,
    .printer-preview-content ol {
        margin: 1em 0 1em 0;
    }
    .printer-preview-content h1,
    .printer-preview-content h2,
    .printer-preview-content h3,
    .printer-preview-content h4,
    .printer-preview-content h5,
    .printer-preview-content h6 {
        font-weight: bold;
    }
    .printer-preview-content h1 {
        font-size: 250%;
    }
    .printer-preview-content h2 {
        font-size: 175%;
    }
    .printer-preview-content h3 {
        font-size: 135%;
    }
    .printer-preview-content h4 {
        font-size: 100%;
    }
    .printer-preview-content h5 {
        font-size: 100%;
    }
    .printer-preview-content h6 {
        font-size: 90%;
        font-style: italic;
    }
    .printer-preview-content hr {
        clear: both;
        overflow: hidden;
        margin: 1.5em 0 1.5em 0;
        border: 0;
        border-bottom: 1px solid #e1e1e1;
        height: 0;
    }
    .printer-preview-content
        img[src$=".svg"].aspect-ratio__content.placeholder {
        color: transparent;
        fill: #d3dbe2;
        max-width: 20px;
        max-height: 20px;
    }
    .printer-preview-content a:link,
    .printer-preview-content a:visited {
        text-decoration: underline;
    }
    .printer-preview-content b,
    .printer-preview-content strong {
        font-weight: bold;
    }
    .printer-preview-content li {
        margin: 0 0 0 2em;
    }
    .printer-preview-content .header-row + .row {
        margin-top: 0px;
    }
    .printer-preview-content .documents {
        float: none;
        width: auto;
    }
    .printer-preview-content div.order-table {
        display: block !important;
    }
    .printer-preview-content table thead {
        display: table-row-group;
    }
    .printer-preview-content table tbody tr {
        page-break-inside: avoid !important;
        page-break-after: auto !important;
    }
    .printer-preview-content table tbody tr td {
        page-break-inside: avoid !important;
    }
    .printer-preview-content .box,
    .printer-preview-content .header-row,
    .printer-preview-content .printer-preview-top,
    .printer-preview-content .section.buttons.section-print {
        display: none !important;
    }
    .printer-preview-content .printer-preview-content {
        font-family: "Open Sans", "sans-serif";
        padding: 0;
        border: none;
        margin: 0;
    }
    .printer-preview-content table {
        width: 100%;
        border-spacing: 0;
        border-collapse: collapse;
    }
    .printer-preview-content td,
    .printer-preview-content th {
        width: auto;
        border-spacing: 0;
        border-collapse: collapse;
        border-bottom: none;
    }
    .printer-preview-content #wrapper table.table-tabular,
    .printer-preview-content .table-tabular {
        border: 1px solid #e1e1e1;
        margin: 0 0 0 0;
        width: 100%;
        border-spacing: 0;
        border-collapse: collapse;
    }
    .printer-preview-content #wrapper table.table-tabular th,
    .printer-preview-content #wrapper table.table-tabular td,
    .printer-preview-content .table-tabular th,
    .printer-preview-content .table-tabular td {
        padding: 0.5em;
        text-align: left;
    }
    .printer-preview-content #wrapper table.table-tabular th,
    .printer-preview-content .table-tabular th {
        border-bottom: 1px solid #e1e1e1;
    }
    .printer-preview-content #wrapper table.table-tabular td,
    .printer-preview-content .table-tabular td {
        border-bottom: 1px solid #e1e1e1;
        text-align: left;
    }
    .printer-preview-content #wrapper table.table-tabular tfoot td,
    .printer-preview-content .table-tabular tfoot td {
        border-bottom-width: 0px;
        border-top: 1px solid #e1e1e1;
        padding-top: 1em;
    }
    .printer-preview-content .row {
        margin: 0;
    }
    .pos-view-body {
        min-height: 780px !important;
        background: #ffffff !important;
    }
}

.overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background: white;
    left: 0;
    top: 0;
    z-index: 5;
}

.download-button {
    color: var(--p-interactive);
}

.print-layout {
    padding: 2em;
}
.print-layout .template-expiry-date-inventory-batch * {
    font-size: 11px;
    font-weight: lighter;
}

.print-layout .template-expiry-date-inventory-batch a,
.print-layout .template-expiry-date-inventory-batch a:link,
.print-layout .template-expiry-date-inventory-batch a:visited {
    font-weight: 200;
}

.print-layout .template-expiry-date-inventory-batch .header p {
    margin: 0;
}

.print-layout .template-expiry-date-inventory-batch .shop-title {
    font-size: 20px;
    font-weight: 400;
}

.print-layout .template-expiry-date-inventory-batch .subtitle-bold {
    font-size: 10px;
    font-weight: bold;
}

.print-layout .printer-preview-content b,
.print-layout .printer-preview-content strong {
    font-weight: bold;
}

.print-layout .template-expiry-date-inventory-batch .aspect-ratio-square {
    width: 45px;
    height: 45px;
}