export declare type Order = {
  id: string | number;
  merchant_id: string | number;
  shopify_order_id: string | number;
  customer_id: string | number | null;
  customer: Customer | null;
  items: Array<OrderItem>;
  order_number: string;
  amount: number;
  billing_address: {
    first_name: string;
    address1: string;
    phone: string | null;
    city: string;
    zip: string;
    province: string;
    country: string;
    last_name: string;
    address2: string;
    company: string | null;
    latitude: string | null;
    longitude: string | null;
    name: string;
    country_code: string;
    province_code: string;
  } | null;
  shipping_address: {
    first_name: string;
    address1: string;
    phone: string | null;
    city: string;
    zip: string;
    province: string;
    country: string;
    last_name: string;
    address2: string;
    company: string | null;
    latitude: string | null;
    longitude: string | null;
    name: string;
    country_code: string;
    province_code: string;
  } | null;
  note: string | null;
  status: string;
  fulfillment_status: string;
  batch_assign_status: string;
  created_at: Date;
  updated_at: Date;
};

export declare type OrderItem = {
  id: string | number;
  line_item_id: string | number | null;
  order_id: string | number;
  name: string;
  product_id: string | number;
  product: Product;
  batch_items: Array<OrderItemAssign>;
  quantity: number;
  price: number;
};

export declare type OrderItemAssign = {
  pivot: {
    id: string | number;
    order_item_id: string | number;
    batch_item_id: string | number;
    quantity: number;
  };
  created_at: Date;
  updated_at: Date;
} & BatchItem;

export declare type BatchItem = {
  id: string | number;
  inventory_batch_id: string | number;
  product_id: string | number;
  batch: InventoryBatch;
  product: Product;
  quantity: number;
  expire_at: Date | null;
  dates: Array<BatchItemDate>;
  status: string;
  deleted_at: Date | null;
};

export declare type BatchItemDate = {
  id: string | number;
  batch_item_id: string | number;
  date: Date;
  deleted_at: Date | null;
};

export declare type Product = {
  id: string | number;
  merchant_id: string | number;
  shopify_variant_id: string | number;
  batch_items: Array<BatchItem>;
  name: string;
  parent_id: string | number;
  parent_name: string;
  display_name: string;
  sku: string;
  image_url: string;
  price: number;
  original_price: number;
  compare_price: number;
  status: string;
  quantity: string | number;
  available_quantity: number;
  expired_quantity: number;
  quantity_at_location: number;
  is_inventory_managed: boolean;
  is_item_stocked: boolean;
  stocked_quantity: number;
  nearest_expire_date: Date;
  created_at: Date;
  updated_at: Date;
};

export declare type ProductStoreURL = {
  preview_url: string | null;
  store_url: string | null;
};

export declare type Discount = {
  id: string | number;
  batch_item_id: string | number;
  product_id: string | number;
  type: string;
  discount_rate: number;
  discounted_price: number;
  discount_period: number | null;
  discount_end_at: Date | null;
  track_period: string | null;
  track_unit: string | null;
  status: string;
  created_at: Date;
  updated_at: Date;
  product: Product;
  batch_item: BatchItem;
};

export declare type InventoryBatch = {
  id: string | number;
  merchant_id: string | number;
  location_id: string | number;
  location: Location;
  items: Array<BatchItem>;
  name: string | null;
  received_at: Date | null;
  lot_number: string | null;
  bin_location: string | null;
  barcode: string | null;
  invoice_number: string | null;
  description: string | null;
  status: string;
  order_count: number;
  is_sync_quantity: boolean;
  meta: Array<Meta> | null;
  last_verified_at?: Date | null;
  created_at: Date;
  updated_at: Date;
};

export declare type InventoryBatchInput = {
  location_id?: string | number;
  items: Array<InventoryBatchItemInput>;
  name: string;
  is_sync_quantity: boolean;
  received_at: string | null;
  lot_number: string | null;
  bin_location: string | null;
  barcode: string | null;
  invoice_number: string | null;
  description: string | null;
  meta: Array<Meta>;
};

export declare type InventoryBatchTransferInput = {
  source_batch_id: string | number;
  destination_batch_id: string | number;
  product_id: string | number;
  quantity: string | number;
  source_quantity_adjustment: boolean;
  destination_quantity_adjustment: boolean;
  is_draft?: boolean;
};

export declare type Transfer = {
  id: string | number;
  merchant_id: string | number;
  source_batch_id: string | number;
  destination_batch_id: string | number;
  product_id: string | number;
  quantity: number;
  source_quantity_adjustment: boolean;
  destination_quantity_adjustment: boolean;
  is_draft: boolean;
  status: string;
  notes: string | null;
  executed_at: Date | null;
  created_at: Date;
  updated_at: Date;
  source_batch: InventoryBatch;
  destination_batch: InventoryBatch;
  product: Product;
};

export declare type InventoryBatchItemInput = {
  product_id: string | number;
  quantity: string | number;
  expire_at: string | null;
  dates: Array<string> | null;
};

export declare type Meta = {
  name: string;
  value: string;
};

export declare type InventoryBatchVerification = {
  id: string | number;
  inventory_batch_id: string | number;
  quantity: number;
  adjusted_by: string;
  created_at: Date;
};

export declare type InventoryHistory = {
  id: string | number;
  merchant_id: string | number;
  action_id: string | number;
  adjusted_by: string | null;
  action: HistoryAction;
  batch_id: string | number;
  batch: InventoryBatch;
  order_id: string | number | null;
  order: Order;
  adjustment: number;
  result_quantity: number;
  location_quantity: number;
  created_at: Date;
  updated_at: Date;
};

export declare type HistoryAction = {
  id: string | number;
  name: string;
  slug: string;
  trigger_by: string | null;
};

export declare type ImportHistory = {
  id: string | number;
  merchant_id: string | number;
  model: string;
  path: string;
  rows_count: number;
  status: string;
  created_at: Date;
  updated_at: Date;
};

export declare type Location = {
  id: string | number;
  merchant_id: string | number;
  shopify_location_id: string | number;
  name: string;
  created_at: Date;
  updated_at: Date;
};

export declare type Customer = {
  id: string | number;
  user_id: string | number;
  user: User;
  merchant_id: string | number;
  shopify_customer_id: string | number;
  name: string;
  email: string;
  phone: string;
  created_at: Date;
  updated_at: Date;
};

export declare type User = {
  id: string | number;
  name: string;
  email: string;
  created_at: Date;
  updated_at: Date;
};

export declare type Plan = {
  name: string;
  features: Array<string>;
  batch_products_limit: number;
  month: {
    price: number;
  };
  year: {
    price: number;
  };
  orders_limit_per_month: number;
};

export declare type BatchExpirationPeriod = {
  period: string | number;
  unit: string;
};

export declare type BatchDiscount = {
  type?: string;
  discount_rate: string | number;
  days_before_expire: string | number;
};

export declare type SlowMovingDiscount = {
  track_period: string | number;
  unit: string;
  sales_qty: string | number;
  discount_rate: string | number;
  discount_period: string | number;
};

export declare type Settings = {
  is_sync_batch_quantity: boolean;
  is_disable_adjust_quantity: boolean;
  is_show_storefront_expire_at: boolean;
  slow_moving_discounts: Array<SlowMovingDiscount>;
  is_remove_when_expired: boolean;
  notification_email: string | null;
  custom_dashboard_day_filter?: number;
  shop: {
    country: string;
    has_gift_cards: boolean;
    multi_location_enabled: boolean;
    source: string | null;
    province_code: string;
    money_with_currency_in_emails_format: string;
    plan_display_name: string;
    province: string;
    eligible_for_payments: boolean;
    id: number;
    longitude: number;
    zip: number;
    has_discounts: boolean;
    password_enabled: boolean;
    iana_timezone: string;
    auto_configure_tax_inclusivity: boolean | null;
    enabled_presentment_currencies: Array<string>;
    plan_name: string;
    requires_extra_payments_agreement: boolean;
    country_code: string;
    weight_unit: string;
    cookie_consent_level: string;
    phone: string;
    domain: string;
    customer_email: string;
    name: string;
    money_with_currency_format: string;
    county_taxes: boolean;
    money_in_emails_format: string;
    finances: boolean;
    shop_owner: string;
    pre_launch_enabled: boolean;
    city: string;
    timezone: string;
    latitude: number;
    created_at: Date;
    taxes_included: boolean;
    checkout_api_supported: boolean;
    setup_required: boolean;
    money_format: string;
    updated_at: Date;
    country_name: string;
    currency: string;
    email: string;
    primary_location_id: number;
    google_apps_login_enabled: boolean | null;
    address2: string | null;
    address1: string | null;
    primary_locale: string;
    has_storefront: boolean;
    tax_shipping: boolean | null;
    transactional_sms_disabled: boolean;
    google_apps_domain: string | null;
    myshopify_domain: string;
    visitor_tracking_consent_preference: string;
    marketing_sms_consent_enabled_at_checkout: boolean;
  };
  batch_discounts: Array<BatchDiscount>;
  meta_presets: Array<string> | null;
  is_apply_discount: boolean;
  is_email_notification: boolean;
  is_notify_batch_out_of_stock: boolean;
  is_notify_batch_expiration: boolean;
  notify_batch_expiration: Array<BatchExpirationPeriod>;
  is_notify_batch_export: boolean;
  plan: string;
  billing_period: string;
  days_before_expire: number;
  is_apply_slow_moving: boolean;
  is_auto_update_discount: boolean;
};

export declare type Theme = {
  id: string | number;
  name: string;
  is_published: boolean;
  is_supported: boolean;
};

export declare type ChartDataByDate = {
  [key: string]: { quantity: number; cost: number };
};

export declare type RecurringPlan = {
  confirmation_url: string;
};

export declare type SortFilterOptions = {
  sort_field?: string;
  sort_direction?: string;
};

export declare type PackingSlipRenderItem = {
  order_item_id: string | number;
  name: string;
  total_quantity: number;
  checked: boolean;
  assignments: Array<{
    batch_item_id: string | number | null;
    assignment_id: string | number | null;
    batch_name: string | null;
    quantity: number;
    checked: boolean;
  }>;
};

export declare type BasePagination = {
  current_page: number;
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
};

export declare type BatchProductAnalytics = {
  id: number | string;
  shopify_variant_id: number | string;
  parent_id: number | string;
  name: string;
  price: number;
  original_price: number;
  quantity: number;
  nearest_expire_date: Date;
  best_before_dates: Array<Date>;
  discount: Discount;
};

export declare type BatchProductAnalyticsResponse = {
  data: Array<BatchProductAnalytics>;
} & BasePagination;

export declare type InventoryBatchPaginationData = {
  data: Array<InventoryBatch>;
} & BasePagination;

export declare type ProductPaginationData = {
  data: Array<Product>;
} & BasePagination;

export declare type InventoryHistoryPaginationData = {
  data: Array<InventoryHistory>;
} & BasePagination;

export declare type ImportHistoryPaginationData = {
  data: Array<ImportHistory>;
} & BasePagination;

export declare type InventoryHistoryResponse = {
  product: Product;
  import: ImportHistory;
  history: InventoryHistoryPaginationData;
};

export declare type PlansResponse = {
  [key: string]: Plan;
};

export declare type InventoryBatchOrdersResponse = {
  inventory_batch: InventoryBatch;
  orders: Array<Order>;
};

export declare type OrderResponse = {
  data: Array<Order>;
} & BasePagination;

export declare type OrderBulkAssignResponse = {
  errors: Array<string>;
};

export declare type BaseQueryOptions = {
  page?: number;
  order_by?: string;
  order_direction?: string;
};

export declare type ReduxStateType = {
  shop: {
    _details: {
      shop_url: string | null;
      host: string | null;
      settings: Settings | null;
    };
  };
  location: {
    current_location: Location | null;
    _locations: Array<Location> | null;
  };
};
