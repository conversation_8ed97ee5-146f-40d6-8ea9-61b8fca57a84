import { useAuthenticatedFetch } from "@hooks/useAuthenticatedFetch";
import { useMemo } from "react";

export const useShopifyFetch = ({ url, query, fetchInitOptions }: { url: string; query?: any; fetchInitOptions?: any; }) => {
  const authenticatedFetch = useAuthenticatedFetch();

  const fetch = useMemo(() => {
    return async (queryOptions?: any, body?: any) => {
      let requestURL = url;
      const { appendPath } = queryOptions ?? {};
      if (appendPath) {
        requestURL += appendPath;
      }
      if (query) {
        requestURL += "?";
        requestURL += Object.keys(query).map(key => key + "=" + query[key]).join("&");
      }
      const response = await authenticatedFetch(requestURL, {
        ...fetchInitOptions,
        ...(body
          ? {
            headers: {
              ...fetchInitOptions?.headers,
              ...(fetchInitOptions?.is_form_data
                ? {}
                : { "Content-Type": "application/json" }
              )
            },
            body: fetchInitOptions?.is_form_data ? body : JSON.stringify(body)
          }
          : {}
        )
      });
      const response_data = await response.json();
      if (!response.ok) {
        return Promise.reject({ data: response_data, status: response.status, headers: response.headers })
      }
      const { data } = response_data;
      return data;
    };
  }, [url, JSON.stringify(query), JSON.stringify(fetchInitOptions)]);

  return fetch;
}