import { createSlice } from "@reduxjs/toolkit";
import { HYDRATE } from "next-redux-wrapper";

const initialState = {
    _details: {}
};

export const shopSlice = createSlice({
    name: "shop",
    initialState,
    reducers: {
        setShop: (state, action) => {
            state._details = { 
                ...state._details,
                ...action.payload
            };
        }
    },
    extraReducers: {
        [HYDRATE]: (state, action) => {
          return {
            ...state,
            ...action.payload,
          };
        },
    },
});

export const { setShop } = shopSlice.actions;

export default shopSlice.reducer;