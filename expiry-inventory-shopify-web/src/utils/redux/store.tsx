import { combineReducers, createStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist"
import storage from "redux-persist/lib/storage";
import { createWrapper } from "next-redux-wrapper";
import shopReducer from "./features/shop/shopSlice";

const persistConfig = {
    key: "root",
    storage,
};

const persistedReducer = persistReducer<any>(persistConfig, combineReducers({
    shop: shopReducer
}));

const _store = () => {
    let store = createStore(persistedReducer);
    let persistor = persistStore(store);
    return { store, persistor };
};

export const makeStore = () => {
    const isServer = typeof window === "undefined";
    if (isServer) {
      return createStore(persistedReducer);
    } else {
      let store = createStore(persistedReducer);
      //@ts-ignore
      store.__persistor = persistStore(store); // Nasty hack
      return store;
    }
};

export default _store();
export const wrapper = createWrapper(makeStore);