{"compilerOptions": {"baseUrl": ".", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "downlevelIteration": true, "jsx": "preserve", "paths": {"@components/*": ["src/components/*"], "@contexts/*": ["src/contexts/*"], "@data/*": ["src/data/*"], "@repositories/*": ["src/repositories/*"], "@settings/*": ["src/settings/*"], "@styles/*": ["src/styles/*"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"], "@ts-types/*": ["src/ts-types/*"], "@assets/*": ["src/assets/*"]}, "incremental": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}